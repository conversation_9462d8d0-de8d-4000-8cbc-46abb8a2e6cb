"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js"),l=require("../../utils/config.js");if(!Array){(e.resolveComponent("uni-search-bar")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+t)();const t=()=>"../../components/layout/CustomTabBar.js",n={__name:"index",setup(t){const n=e.computed((()=>d.value.bg_country_list_header||"")),u=e.computed((()=>d.value.bg_country_list_active_tab||"")),o=l.IMAGE_BASE_URL,r=e.ref(""),i=e.ref("ALL"),s=e.ref([]),v=e.ref(!1),c=e.getCurrentInstance(),d=e.ref(e.index.getStorageSync("staticAssets")||{}),f=[{label:"全部",value:"ALL"},{label:"亚洲",value:"ASIA"},{label:"欧洲",value:"EUROPE"},{label:"北美洲",value:"NORTH_AMERICA"},{label:"南美洲",value:"SOUTH_AMERICA"},{label:"非洲",value:"AFRICA"},{label:"大洋洲",value:"OCEANIA"}],m=e.ref(0),h=e.ref([]),b=e.ref(0),g=e.ref(!1),p=e.ref(0),_=e.ref(0),x=e.computed((()=>{if(!h.value.length||m.value>=h.value.length)return-999;const a=h.value[m.value];if(!a)return-999;const l=e.index.upx2px(20);return a.left+a.width/2-b.value-l})),y=e=>{b.value=e.detail.scrollLeft},A=()=>new Promise((a=>{const l=e.index.createSelectorQuery().in(c);l.select(".continent-tabs").boundingClientRect(),l.selectAll(".tab-item").boundingClientRect(),l.exec((e=>{if(e&&e[1]&&e[1].length){const l=e[0],t=e[1];_.value=l?l.width:0,h.value=t,g.value=!0,a(t)}else a([])}))})),w=()=>{var e;if(!h.value.length||m.value>=h.value.length)return;const a=h.value[m.value],l=_.value;if(!a||!l)return;let t=a.left+a.width/2-l/2;const n=Math.max(0,(null==(e=a.dataset)?void 0:e.scrollWidth)-l||0);t=Math.max(0,Math.min(t,n)),p.value=t},C=async()=>{await e.nextTick$1();let a=0;const l=async()=>{const e=await A();0===e.length&&a<5?(a++,setTimeout(l,100)):e.length>0&&w()};l()},T=async(l=!1)=>{if(!v.value){v.value=!0;try{const t=await a.getCountryList({continent:i.value,keyword:r.value});s.value=l?t.data:[...s.value,...t.data]}catch(t){console.error("获取国别列表失败:",t),e.index.showToast({title:"数据加载失败",icon:"none"})}finally{v.value=!1,l&&e.index.stopPullDownRefresh()}}},R=()=>{T(!0)},E=()=>{r.value="",T(!0)},I=()=>{};return e.onLoad((()=>{T(!0),setTimeout(C,200)})),e.onShow((()=>{e.index.hideTabBar(),setTimeout(C,100)})),e.onPullDownRefresh((()=>{T(!0)})),(a,l)=>e.e({a:e.o(R),b:e.o(E),c:e.o(R),d:e.o((e=>r.value=e)),e:e.p({placeholder:"搜索国别名称",radius:"100",bgColor:"#ffffff",modelValue:r.value}),f:e.f(f,((a,l,t)=>({a:e.t(a.label),b:a.value,c:e.n({active:i.value===a.value}),d:e.o((t=>(async(a,l)=>{i.value!==a&&(m.value,i.value=a,m.value=l,h.value.length||await A(),e.nextTick$1((()=>{w()})),T(!0))})(a.value,l)),a.value),e:"tab-"+l,f:i.value===a.value?`url(${u.value})`:"none"}))),g:e.o(y),h:p.value,i:x.value+"px",j:g.value?1:0,k:`url(${n.value})`,l:e.f(s.value,((a,l,t)=>({a:e.unref(o)+a.listCoverUrl,b:e.t(a.nameCn),c:e.t(a.nameEn),d:e.t(a.summary),e:e.unref(o)+a.flagUrl,f:a.id,g:e.o((l=>{return t=a.id,void e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${t}`});var t}),a.id)}))),m:v.value},v.value?{n:e.p({status:"loading"})}:(s.value.length,{}),{o:0===s.value.length,p:e.o(I),q:e.p({current:3})})}},u=e._export_sfc(n,[["__scopeId","data-v-02a10f8b"]]);wx.createPage(u);
