{"version": 3, "file": "EventCard.js", "sources": ["components/event/EventCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"event-card\" @click=\"$emit('click', event)\">\r\n    <view class=\"card-left\">\r\n      <image :src=\"getFullImageUrl(event.coverImageUrl)\" mode=\"aspectFill\" class=\"event-image\" :lazy-load=\"true\"></image>\r\n      <view v-if=\"event.registrationStatus !== undefined\" :class=\"['status-tag', getRegistrationStatusClass(event.registrationStatus)]\">\r\n        {{ formatRegistrationStatus(event.registrationStatus) }}\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"card-right\">\r\n      <text class=\"event-title\">{{ event.title }}</text>\r\n\r\n      <view class=\"event-info-row\">\r\n        <view class=\"time-location-item\">\r\n          <image class=\"event-info-icon\" :src=\"listTimeIconUrl\" mode=\"aspectFit\"></image>\r\n          <text class=\"info-text\">{{ formatEventDate(event.startTime) }}</text>\r\n        </view>\r\n        <view class=\"time-location-item\">\r\n          <image class=\"event-info-icon\" :src=\"listLocationIconUrl\" mode=\"aspectFit\"></image>\r\n          <text class=\"info-text\">{{ formatEventLocation(event) }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"event-info remaining-spots\">\r\n        <text class=\"spots-count\">\r\n          剩余名额: {{ calculateRemainingSpots(event.maxParticipants, event.registeredCount) }}\r\n        </text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n  \r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js'\r\nimport { formatEventDate } from '@/utils/date.js'\r\nimport { getFullImageUrl } from '@/utils/image.js'\r\nimport { formatEventLocation } from '@/utils/location.js'\r\n\r\nconst props = defineProps({\r\n  event: { type: Object, required: true }\r\n})\r\n\r\n// 静态资源 URL（不再使用本地兜底）\r\nconst listTimeIconUrl = ref('')\r\nconst listLocationIconUrl = ref('')\r\n\r\n// 组件挂载时读取静态资源配置\r\nonMounted(() => {\r\n  const assets = uni.getStorageSync('staticAssets')\r\n  \r\n  listTimeIconUrl.value = assets?.list_time || ''\r\n  listLocationIconUrl.value = assets?.list_location || ''\r\n})\r\n\r\n// formatEventLocation 函数已从 @/utils/location.js 导入\r\n\r\n/**\r\n * 格式化报名状态文本\r\n */\r\nconst formatRegistrationStatus = (status) => {\r\n  switch (status) {\r\n    case 0: return '即将开始'\r\n    case 1: return '报名中'\r\n    case 2: return '报名截止'\r\n    default: return '未知'\r\n  }\r\n}\r\n\r\n/**\r\n * 获取报名状态样式类\r\n */\r\nconst getRegistrationStatusClass = (status) => {\r\n  switch (status) {\r\n    case 0: return 'not-started'  // 未开始\r\n    case 1: return 'open'         // 报名中\r\n    case 2: return 'ended'        // 已截止\r\n    default: return 'unknown'\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.event-card {\r\n  width: 100%;\r\n  height: 272rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\r\n  border: none;\r\n  border-top: 2rpx solid #EEEEEE;\r\n  border-bottom: 2rpx solid #EEEEEE;\r\n  margin-bottom: 0rpx;\r\n  padding: 24rpx 24rpx;\r\n  display: flex;\r\n  overflow: hidden;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.card-left {\r\n  position: relative;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  flex-shrink: 0;\r\n  margin-top: 16rpx;\r\n  margin-bottom: 16rpx; \r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n  border-radius: 16rpx;\r\n}\r\n\r\n.status-tag {\r\n  position: absolute;\r\n  top: 12rpx;\r\n  left: 12rpx;\r\n  width: 96rpx;\r\n  height: 44rpx;\r\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n  border-radius: 22rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-sizing: border-box;\r\n  padding: 6rpx 12rpx;\r\n\r\n  color: #23232A;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n  font-weight: 500;\r\n  font-size: 22rpx;\r\n  text-align: center;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: 1.2;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n\r\n  &.ended {\r\n    background: #9B9A9A;\r\n    color: #FFFFFF;\r\n    width: 116rpx; // 报名截止文字较长，增加宽度\r\n  }\r\n  \r\n  &.not-started {\r\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n    color: #23232A;\r\n    width: 116rpx; // 即将开始文字较长，增加宽度\r\n  }\r\n  \r\n  &.open {\r\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n    color: #23232A;\r\n    // 报名中文字较短，保持默认宽度96rpx\r\n  }\r\n}\r\n\r\n.card-right {\r\n  flex: 1;\r\n  padding: 16rpx 20rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.event-title {\r\n  width: 346rpx;\r\n  height: 80rpx;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n  font-weight: normal;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  text-align: justify;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: 1.4;\r\n  margin-bottom: 24rpx;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  line-clamp: 2;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.event-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.event-info-row {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: flex-start !important;\r\n  gap: 24rpx !important;\r\n  margin-bottom: 18rpx !important;\r\n  flex-wrap: nowrap !important;\r\n}\r\n\r\n.time-location-item {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 8rpx !important;\r\n  flex-shrink: 0 !important;\r\n}\r\n\r\n.event-info-icon {\r\n  width: 32rpx !important;\r\n  height: 32rpx !important;\r\n  flex-shrink: 0 !important;\r\n}\r\n\r\n.info-text {\r\n  width: 176rpx !important;\r\n  height: 32rpx !important;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;\r\n  font-weight: normal !important;\r\n  font-size: 22rpx !important;\r\n  color: #9B9A9A !important;\r\n  text-align: left !important;\r\n  font-style: normal !important;\r\n  text-transform: none !important;\r\n  line-height: 32rpx !important;\r\n  overflow: hidden !important;\r\n  text-overflow: ellipsis !important;\r\n  white-space: nowrap !important;\r\n}\r\n\r\n.remaining-spots {\r\n  width: 154rpx;\r\n  height: 40rpx;\r\n  border-radius: 4rpx 4rpx 4rpx 4rpx;\r\n  border: 1rpx solid #FB8620;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0;\r\n  margin: 0;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n  flex-shrink: 0;\r\n\r\n  .spots-count {\r\n    width: 100%;\r\n    height: 36rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n    font-weight: normal;\r\n    font-size: 20rpx;\r\n    color: #FB8620;\r\n    text-align: center;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    line-height: 36rpx;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;AA6CA,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAC9B,UAAM,sBAAsBA,cAAG,IAAC,EAAE;AAGlCC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,sBAAgB,SAAQ,iCAAQ,cAAa;AAC7C,0BAAoB,SAAQ,iCAAQ,kBAAiB;AAAA,IACvD,CAAC;AAOD,UAAM,2BAA2B,CAAC,WAAW;AAC3C,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAKA,UAAM,6BAA6B,CAAC,WAAW;AAC7C,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;AC/EA,GAAG,gBAAgB,SAAS;"}