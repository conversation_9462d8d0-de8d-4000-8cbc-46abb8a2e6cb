"use strict";const e=require("../../common/vendor.js"),t=require("../../api/content/article.js"),a=require("./api/content/comment.js"),n=require("./api/common/mpHtmlStyles.js"),o=require("../../utils/config.js"),u=require("../../utils/image.js"),l=require("../../utils/date.js");if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-popup"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+r+i+(()=>"../../uni_modules/uview-plus/components/u-popup/u-popup.js"))();const r=()=>"../../uni_modules/mp-html/components/mp-html/mp-html.js",i=()=>"../../components/common/CommentItem.js",s={__name:"detail",setup(r){const i=e.index.upx2px(20),s=e.ref(0),c=e.ref(0),m=e.ref(0),v=e.ref(!1),g=e.ref(!1),p=e.ref(e.index.getStorageSync("staticAssets")||{}),d=e.computed((()=>p.value.icon_article_comment_input||"")),f=e.computed((()=>{const e=p.value.bg_article_summary_card;return e?{backgroundImage:`url('${e}')`}:{}})),y=()=>{try{if(!e.index.getStorageSync("token")){const a=(()=>{try{const e=getCurrentPages(),t=e[e.length-1],a="/"+t.route,n=t.options||{},o=Object.keys(n).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(n[e])}`)).join("&");return o?`${a}?${o}`:a}catch(e){return"/pages_sub/pages_article/detail"}})();try{e.index.setStorageSync("loginBackPage",a)}catch(t){}return e.index.navigateTo({url:"/pages_sub/pages_other/login"}),!1}return!0}catch(t){return e.index.navigateTo({url:"/pages_sub/pages_other/login"}),!1}},h=()=>{y()&&(j.content="",v.value=!0)},x=()=>{v.value=!1},b=()=>{e.index.navigateBack({delta:1})},_=e.ref(null),w=e.ref(null),S=e.ref([]),T=e.ref(0),j=e.reactive({content:"",isSubmitting:!1}),C=e.ref(!1),I=e.computed((()=>o.config.imageBaseUrl?o.config.imageBaseUrl.replace(/\/$/,""):"")),q=async()=>{if(w.value){C.value=!0;try{const e=await a.getCommentList({relatedId:w.value,relatedType:"article"});200===e.code&&e.data?(S.value=e.data.comments||[],T.value=e.data.total||0):(S.value=[],T.value=0)}catch(e){S.value=[],T.value=0}finally{C.value=!1}}},A=async()=>{if(!y())return;await(async({content:t,stateObject:n})=>{if(t.trim()&&!n.isSubmitting){n.isSubmitting=!0;try{const o=await a.addComment({relatedId:w.value,relatedType:"article",content:t.trim()});return 200===o.code?(e.index.showToast({title:"评论成功，待审核",icon:"success"}),await q(),!0):(e.index.showToast({title:o.msg||"评论失败",icon:"error"}),!1)}catch(o){return!1}finally{n.isSubmitting=!1}}})({content:j.content,stateObject:j})&&(j.content="",x())},B=async()=>{w.value?(await(async a=>{g.value=!1;try{const o=await t.getArticleDetail(a);if(200===o.code&&o.data){const e=o.data;let t=[];if(e.tags&&"string"==typeof e.tags)try{t=JSON.parse(e.tags)}catch(n){console.error("标签解析失败:",n)}else Array.isArray(e.tags)&&(t=e.tags);Array.isArray(t)||(t=[]),_.value={...e,parsedTags:t}}else e.index.showToast({title:o.msg||"获取文章失败",icon:"none"}),g.value=!0}catch(o){console.error("获取文章失败:",o),e.index.showToast({title:"网络请求失败",icon:"error"}),g.value=!0}})(w.value),_.value&&(await e.nextTick$1(),(()=>{try{const t=e.index.getMenuButtonBoundingClientRect();s.value=t.top,c.value=t.height,m.value=t.bottom+i}catch(t){const a=e.index.getSystemInfoSync();s.value=a.statusBarHeight||20,c.value=44,m.value=s.value+c.value+i}})(),await q())):g.value=!0},k=()=>{B()};return e.onLoad((e=>{w.value=e.id,B()})),(t,a)=>e.e({a:_.value},_.value?e.e({b:s.value+"px",c:e.p({name:"arrow-left",color:"#000000",size:"22"}),d:e.o(b),e:c.value+"px",f:e.unref(i)+"px",g:m.value+"px",h:e.unref(u.getFullImageUrl)(_.value.coverImageUrl),i:e.t(_.value.title),j:e.t(_.value.source),k:e.t(e.unref(l.formatDate)(_.value.publishTime,"YYYY-MM-DD")),l:e.t(_.value.viewCount),m:_.value.summary},_.value.summary?{n:e.t(_.value.summary),o:e.s(f.value)}:{},{p:e.p({content:_.value.content,domain:I.value,"tag-style":e.unref(n.tagStyle),"preview-img":!0,"lazy-load":!0}),q:_.value.parsedTags&&_.value.parsedTags.length>0},_.value.parsedTags&&_.value.parsedTags.length>0?{r:e.f(_.value.parsedTags,((t,a,n)=>({a:e.t(t.name||t),b:e.t(a<_.value.parsedTags.length-1?" / ":""),c:t.id||t.name})))}:{},{s:e.t(T.value),t:d.value,v:e.o(h),w:S.value.length>0},S.value.length>0?{x:e.f(S.value,((t,a,n)=>({a:t.id,b:"5654f69e-2-"+n,c:e.p({comment:t})})))}:{},{y:m.value+"px"}):g.value?{A:e.o(k)}:{},{z:g.value,B:e.o(x),C:j.content,D:e.o((e=>j.content=e.detail.value)),E:e.t(j.content.length),F:e.t(j.isSubmitting?"发布中...":"发布"),G:j.content.trim()?1:"",H:!j.content.trim()||j.isSubmitting,I:e.o(A),J:e.o(x),K:e.p({show:v.value,mode:"bottom",round:"20","safe-area-inset-bottom":!0})})}},c=e._export_sfc(s,[["__scopeId","data-v-5654f69e"]]);wx.createPage(c);
