<view class="event-list-page data-v-8e954d49"><view class="header-wrapper data-v-8e954d49"><image class="header-bg data-v-8e954d49" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-8e954d49"><view class="navbar-title data-v-8e954d49"><text class="title-text data-v-8e954d49">热门活动列表</text></view></view><view class="top-controls data-v-8e954d49"><up-subsection wx:if="{{c}}" class="data-v-8e954d49" bindchange="{{b}}" u-i="8e954d49-0" bind:__l="__l" u-p="{{c}}"></up-subsection><view class="search-wrapper data-v-8e954d49"><custom-search-box wx:if="{{g}}" class="data-v-8e954d49" bindsearch="{{d}}" bindinput="{{e}}" u-i="8e954d49-1" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"></custom-search-box></view></view></view><view wx:if="{{h}}" class="filter-bar sticky-filter-bar data-v-8e954d49"><view class="filter-main-buttons data-v-8e954d49"><view class="filter-button data-v-8e954d49" bindtap="{{l}}"><text class="filter-text data-v-8e954d49">{{i}}</text><up-icon wx:if="{{k}}" class="{{['data-v-8e954d49', j && 'rotate-180']}}" u-i="8e954d49-2" bind:__l="__l" u-p="{{k}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{p}}"><text class="filter-text data-v-8e954d49">{{m}}</text><up-icon wx:if="{{o}}" class="{{['data-v-8e954d49', n && 'rotate-180']}}" u-i="8e954d49-3" bind:__l="__l" u-p="{{o}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{t}}"><text class="filter-text data-v-8e954d49">{{q}}</text><up-icon wx:if="{{s}}" class="{{['data-v-8e954d49', r && 'rotate-180']}}" u-i="8e954d49-4" bind:__l="__l" u-p="{{s}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{y}}"><text class="filter-text data-v-8e954d49">{{v}}</text><up-icon wx:if="{{x}}" class="{{['data-v-8e954d49', w && 'rotate-180']}}" u-i="8e954d49-5" bind:__l="__l" u-p="{{x}}"></up-icon></view></view><view wx:if="{{z}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">排序</text><view class="option-grid data-v-8e954d49"><view wx:for="{{A}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{B}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{C}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{D}}" class="filter-panel data-v-8e954d49"><view class="option-grid data-v-8e954d49" style="margin-bottom:20rpx"><view class="{{['data-v-8e954d49', 'option-item', F]}}" bindtap="{{G}}"><text class="option-text data-v-8e954d49">{{E}}</text></view></view><text class="section-title data-v-8e954d49">热门地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{H}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view wx:if="{{I}}" class="data-v-8e954d49" style="margin-top:20rpx"><text class="section-title data-v-8e954d49">其他地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{J}}" wx:for-item="city" wx:key="b" class="{{['data-v-8e954d49', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-8e954d49">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{K}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{L}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{M}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">时间</text><view class="option-grid data-v-8e954d49"><view wx:for="{{N}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{O}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{P}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{Q}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">全部状态</text><view class="option-grid data-v-8e954d49"><view wx:for="{{R}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{S}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{T}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view></view><scroll-view wx:if="{{U}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-8e954d49" bindscrolltolower="{{ac}}" refresher-enabled refresher-triggered="{{ad}}" bindrefresherrefresh="{{ae}}"><view wx:if="{{V}}" class="empty-state data-v-8e954d49"><up-empty wx:if="{{W}}" class="data-v-8e954d49" u-i="8e954d49-6" bind:__l="__l" u-p="{{W}}"></up-empty><view wx:if="{{X}}" class="retry-container data-v-8e954d49"><up-button wx:if="{{Z}}" class="data-v-8e954d49" u-s="{{['d']}}" bindclick="{{Y}}" u-i="8e954d49-7" bind:__l="__l" u-p="{{Z}}"> 重新加载 </up-button></view></view><event-card wx:for="{{aa}}" wx:for-item="event" wx:key="a" class="data-v-8e954d49" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-8e954d49"><up-loadmore wx:if="{{ab}}" class="data-v-8e954d49" u-i="8e954d49-9" bind:__l="__l" u-p="{{ab}}"/></view></scroll-view><view wx:if="{{af}}" class="filter-bar calendar-filter-bar sticky-filter-bar data-v-8e954d49"><view class="filter-main-buttons data-v-8e954d49"><view class="filter-button data-v-8e954d49" bindtap="{{aj}}"><text class="filter-text data-v-8e954d49">{{ag}}</text><up-icon wx:if="{{ai}}" class="{{['data-v-8e954d49', ah && 'rotate-180']}}" u-i="8e954d49-10" bind:__l="__l" u-p="{{ai}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{an}}"><text class="filter-text data-v-8e954d49">{{ak}}</text><up-icon wx:if="{{am}}" class="{{['data-v-8e954d49', al && 'rotate-180']}}" u-i="8e954d49-11" bind:__l="__l" u-p="{{am}}"></up-icon></view><view class="filter-button filter-placeholder data-v-8e954d49"><text class="filter-text data-v-8e954d49"></text></view><view class="filter-button filter-placeholder data-v-8e954d49"><text class="filter-text data-v-8e954d49"></text></view></view><view wx:if="{{ao}}" class="filter-panel data-v-8e954d49"><view class="option-grid data-v-8e954d49" style="margin-bottom:20rpx"><view class="{{['data-v-8e954d49', 'option-item', aq]}}" bindtap="{{ar}}"><text class="option-text data-v-8e954d49">{{ap}}</text></view></view><text class="section-title data-v-8e954d49">热门地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{as}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view wx:if="{{at}}" class="data-v-8e954d49" style="margin-top:20rpx"><text class="section-title data-v-8e954d49">其他地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{av}}" wx:for-item="city" wx:key="b" class="{{['data-v-8e954d49', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-8e954d49">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{aw}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{ax}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{ay}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">时间</text><view class="option-grid data-v-8e954d49"><view wx:for="{{az}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{aA}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{aB}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view></view><scroll-view wx:if="{{aC}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-8e954d49" bindscrolltolower="{{aL}}" refresher-enabled refresher-triggered="{{aM}}" bindrefresherrefresh="{{aN}}"><view wx:if="{{aD}}" class="empty-state data-v-8e954d49"><up-empty wx:if="{{aE}}" class="data-v-8e954d49" u-i="8e954d49-12" bind:__l="__l" u-p="{{aE}}"></up-empty><view wx:if="{{aF}}" class="retry-container data-v-8e954d49"><up-button wx:if="{{aH}}" class="data-v-8e954d49" u-s="{{['d']}}" bindclick="{{aG}}" u-i="8e954d49-13" bind:__l="__l" u-p="{{aH}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-8e954d49" bindclickItem="{{aI}}" bindviewMore="{{aJ}}" u-i="8e954d49-14" bind:__l="__l" u-p="{{aK||''}}"/><view class="calendar-bottom-safe data-v-8e954d49"/></scroll-view><view wx:if="{{aO}}" class="filter-mask data-v-8e954d49" bindtap="{{aP}}"></view><custom-tab-bar wx:if="{{aQ}}" class="data-v-8e954d49" u-i="8e954d49-15" bind:__l="__l" u-p="{{aQ}}"/></view>