{"version": 3, "file": "EventInfoCard.js", "sources": ["components/event/EventInfoCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRJbmZvQ2FyZC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"info-card\">\r\n    <view class=\"card-header\">\r\n      <view :class=\"['status-tag-detail', getRegistrationStatusClass(calculatedRegistrationStatus)]\">\r\n        <image class=\"status-bg-image\" :src=\"detailBgUrl\" mode=\"aspectFit\"></image>\r\n        <text class=\"status-text\">{{ formatRegistrationStatus(calculatedRegistrationStatus) }}</text>\r\n      </view>\r\n      <view class=\"event-title-section\">\r\n        <text class=\"event-title\">{{ localEvent.title || '' }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"info-row\">\r\n      <image class=\"info-icon\" :src=\"detailTimeIconUrl\" mode=\"aspectFit\"></image>\r\n      <text class=\"info-text\">{{ formatEventTime }}</text>\r\n    </view>\r\n    <view class=\"info-row\">\r\n      <image class=\"info-icon\" :src=\"detailLocationIconUrl\" mode=\"aspectFit\"></image>\r\n      <text class=\"info-text\">{{ formatEventLocation(localEvent) }}</text>\r\n    </view>\r\n    <view class=\"info-row\">\r\n      <image class=\"info-icon\" :src=\"detailUserIconUrl\" mode=\"aspectFit\"></image>\r\n      <rich-text :nodes=\"remainingSpotsNodes\" class=\"info-text\"></rich-text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed, ref, onMounted } from 'vue'\r\nimport { formatEventStatus, getStatusClass } from '@/utils/tools.js'\r\nimport { formatDate } from '@/utils/date.js'\r\nimport { formatEventLocation } from '@/utils/location.js'\r\n\r\nconst props = defineProps({\r\n  eventDetail: { type: Object, required: true }\r\n})\r\n\r\n// 静态资源 URL（不再使用本地兜底）\r\nconst detailBgUrl = ref('')\r\nconst detailTimeIconUrl = ref('')\r\nconst detailLocationIconUrl = ref('')\r\nconst detailUserIconUrl = ref('')\r\n\r\n// 组件挂载时读取静态资源配置\r\nonMounted(() => {\r\n  const assets = uni.getStorageSync('staticAssets')\r\n  \r\n  detailBgUrl.value = assets?.detail_bg || ''\r\n  detailTimeIconUrl.value = assets?.detail_icon_time || ''\r\n  detailLocationIconUrl.value = assets?.detail_icon_location || ''\r\n  detailUserIconUrl.value = assets?.detail_icon_user || ''\r\n})\r\n\r\nconst localEvent = computed(() => props.eventDetail || {})\r\n\r\n// formatEventLocation 函数已从 @/utils/location.js 导入\r\n\r\n/**\r\n * 计算报名状态，与后端RegistrationStatus.calculateStatus逻辑保持一致\r\n * 0: 即将开始 (当前时间 < 报名开始时间)\r\n * 1: 报名中 (报名开始时间 <= 当前时间 <= 报名结束时间)  \r\n * 2: 报名截止 (当前时间 > 报名结束时间)\r\n */\r\nconst calculatedRegistrationStatus = computed(() => {\r\n  const registrationStartTime = localEvent.value?.registrationStartTime\r\n  const registrationEndTime = localEvent.value?.registrationEndTime\r\n  \r\n  try {\r\n    const now = new Date()\r\n    \r\n    // 未开始：报名开始时间不为空 && 当前时间 < 报名开始时间\r\n    if (registrationStartTime && now < new Date(registrationStartTime)) {\r\n      return 0 // 即将开始\r\n    }\r\n    // 报名中：(报名开始时间为空 || 当前时间 >= 报名开始时间) && (报名结束时间为空 || 当前时间 <= 报名结束时间)\r\n    else if ((!registrationStartTime || now >= new Date(registrationStartTime)) && \r\n             (!registrationEndTime || now <= new Date(registrationEndTime))) {\r\n      return 1 // 报名中\r\n    }\r\n    // 已结束：报名结束时间不为空 && 当前时间 > 报名结束时间\r\n    else if (registrationEndTime && now > new Date(registrationEndTime)) {\r\n      return 2 // 报名截止\r\n    }\r\n    \r\n    // 默认返回报名中（如果时间配置不明确）\r\n    return 1\r\n  } catch (error) {\r\n    console.warn('报名状态计算失败:', error)\r\n    return 1 // 默认显示报名中\r\n  }\r\n})\r\n\r\n/**\r\n * 格式化报名状态文本，与EventCard保持一致\r\n */\r\nconst formatRegistrationStatus = (status) => {\r\n  switch (status) {\r\n    case 0: return '即将开始'\r\n    case 1: return '报名中'\r\n    case 2: return '报名截止'\r\n    default: return '未知'\r\n  }\r\n}\r\n\r\n/**\r\n * 获取报名状态样式类，与EventCard保持一致\r\n */\r\nconst getRegistrationStatusClass = (status) => {\r\n  switch (status) {\r\n    case 0: return 'not-started'  // 未开始\r\n    case 1: return 'open'         // 报名中\r\n    case 2: return 'ended'        // 已截止\r\n    default: return 'unknown'\r\n  }\r\n}\r\n\r\nconst formatEventTime = computed(() => {\r\n  if (!localEvent.value?.startTime || !localEvent.value?.endTime) {\r\n    return '时间待定'\r\n  }\r\n  try {\r\n    const startTime = formatDate(localEvent.value.startTime, 'YYYY-MM-DD HH:mm')\r\n    const endTime = formatDate(localEvent.value.endTime, 'YYYY-MM-DD HH:mm')\r\n    return `${startTime} 至 ${endTime}`\r\n  } catch (error) {\r\n    console.warn('时间格式化失败:', error)\r\n    return '时间格式错误'\r\n  }\r\n})\r\n\r\nconst remainingSpotsNodes = computed(() => {\r\n  if (!localEvent.value) {\r\n    return [{ type: 'text', text: '加载中...' }]\r\n  }\r\n\r\n  const max = Number(localEvent.value.maxParticipants) || 0\r\n  if (max === 0) {\r\n    return [{ type: 'text', text: '剩余名额: 不限人数' }]\r\n  }\r\n\r\n  const registered = Number(localEvent.value.registeredCount) || 0\r\n  const remaining = Math.max(0, max - registered)\r\n\r\n  return [\r\n    {\r\n      type: 'node',\r\n      name: 'span',\r\n      children: [\r\n        { type: 'text', text: '剩余名额: ' },\r\n        {\r\n          type: 'node',\r\n          name: 'span',\r\n          attrs: { style: 'color: #023F98;' },\r\n          children: [{ type: 'text', text: String(remaining) }]\r\n        },\r\n        { type: 'text', text: `/${max}` }\r\n      ]\r\n    }\r\n  ]\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info-card {\r\n  background: #FFFFFF;\r\n  margin: 0 30rpx;\r\n  margin-top: -60rpx;\r\n  border-radius: 16rpx 16rpx 16rpx 16rpx;\r\n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);\r\n  padding: 30rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.info-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.status-tag-detail {\r\n  width: 90rpx;\r\n  height: 40rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  font-size: 22rpx;\r\n  border-radius: 12rpx;\r\n  margin-right: 16rpx;\r\n  flex-shrink: 0;\r\n  \r\n  // 与EventCard保持一致的状态样式\r\n  &.ended {\r\n    background: #9B9A9A;\r\n    color: #FFFFFF;\r\n    width: 110rpx; // 报名截止文字较长，增加宽度\r\n  }\r\n  &.ended .status-bg-image {\r\n    display: none;\r\n  }\r\n  \r\n  &.not-started {\r\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n    color: #23232A;\r\n    width: 110rpx; // 即将开始文字较长，增加宽度\r\n  }\r\n  &.not-started .status-bg-image {\r\n    display: none;\r\n  }\r\n  \r\n  &.open {\r\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n    color: #23232A;\r\n    // 报名中文字较短，保持默认宽度90rpx\r\n  }\r\n  &.open .status-bg-image {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.event-title-section {\r\n  flex: 1;\r\n  min-width: 0;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.event-title {\r\n  white-space: normal;\r\n  word-break: break-word;\r\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n  font-weight: normal;\r\n  font-size: 32rpx;\r\n  color: #23232A;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 24rpx;\r\n}\r\n\r\n.info-text {\r\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\r\n  font-size: 26rpx;\r\n  color: #606266;\r\n  margin-left: 16rpx;\r\n}\r\n\r\n.status-bg-image {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.status-text {\r\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n  font-weight: normal;\r\n  font-size: 22rpx;\r\n  color: #023F98;\r\n  position: relative;\r\n  z-index: 2;\r\n  \r\n  // 根据父级状态调整文字颜色\r\n  .status-tag-detail.ended & {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .status-tag-detail.not-started & {\r\n    color: #23232A;\r\n  }\r\n  \r\n  .status-tag-detail.open & {\r\n    color: #23232A;\r\n  }\r\n}\r\n</style>\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventInfoCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni", "computed", "formatDate"], "mappings": ";;;;;;;;;;;AAiCA,UAAM,QAAQ;AAKd,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,oBAAoBA,cAAG,IAAC,EAAE;AAChC,UAAM,wBAAwBA,cAAG,IAAC,EAAE;AACpC,UAAM,oBAAoBA,cAAG,IAAC,EAAE;AAGhCC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,kBAAY,SAAQ,iCAAQ,cAAa;AACzC,wBAAkB,SAAQ,iCAAQ,qBAAoB;AACtD,4BAAsB,SAAQ,iCAAQ,yBAAwB;AAC9D,wBAAkB,SAAQ,iCAAQ,qBAAoB;AAAA,IACxD,CAAC;AAED,UAAM,aAAaC,cAAAA,SAAS,MAAM,MAAM,eAAe,CAAA,CAAE;AAUzD,UAAM,+BAA+BA,cAAQ,SAAC,MAAM;;AAClD,YAAM,yBAAwB,gBAAW,UAAX,mBAAkB;AAChD,YAAM,uBAAsB,gBAAW,UAAX,mBAAkB;AAE9C,UAAI;AACF,cAAM,MAAM,oBAAI,KAAM;AAGtB,YAAI,yBAAyB,MAAM,IAAI,KAAK,qBAAqB,GAAG;AAClE,iBAAO;AAAA,QACR,YAES,CAAC,yBAAyB,OAAO,IAAI,KAAK,qBAAqB,OAC/D,CAAC,uBAAuB,OAAO,IAAI,KAAK,mBAAmB,IAAI;AACvE,iBAAO;AAAA,QACR,WAEQ,uBAAuB,MAAM,IAAI,KAAK,mBAAmB,GAAG;AACnE,iBAAO;AAAA,QACR;AAGD,eAAO;AAAA,MACR,SAAQ,OAAO;AACdD,sBAAAA,MAAA,MAAA,QAAA,4CAAa,aAAa,KAAK;AAC/B,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAKD,UAAM,2BAA2B,CAAC,WAAW;AAC3C,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAKA,UAAM,6BAA6B,CAAC,WAAW;AAC7C,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;;AACrC,UAAI,GAAC,gBAAW,UAAX,mBAAkB,cAAa,GAAC,gBAAW,UAAX,mBAAkB,UAAS;AAC9D,eAAO;AAAA,MACR;AACD,UAAI;AACF,cAAM,YAAYC,WAAAA,WAAW,WAAW,MAAM,WAAW,kBAAkB;AAC3E,cAAM,UAAUA,WAAAA,WAAW,WAAW,MAAM,SAAS,kBAAkB;AACvE,eAAO,GAAG,SAAS,MAAM,OAAO;AAAA,MACjC,SAAQ,OAAO;AACdF,sBAAAA,MAAA,MAAA,QAAA,6CAAa,YAAY,KAAK;AAC9B,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,sBAAsBC,cAAQ,SAAC,MAAM;AACzC,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,SAAQ,CAAE;AAAA,MACzC;AAED,YAAM,MAAM,OAAO,WAAW,MAAM,eAAe,KAAK;AACxD,UAAI,QAAQ,GAAG;AACb,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,aAAY,CAAE;AAAA,MAC7C;AAED,YAAM,aAAa,OAAO,WAAW,MAAM,eAAe,KAAK;AAC/D,YAAM,YAAY,KAAK,IAAI,GAAG,MAAM,UAAU;AAE9C,aAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,YACR,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO,EAAE,OAAO,kBAAmB;AAAA,cACnC,UAAU,CAAC,EAAE,MAAM,QAAQ,MAAM,OAAO,SAAS,GAAG;AAAA,YACrD;AAAA,YACD,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,GAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;AC9JD,GAAG,gBAAgB,SAAS;"}