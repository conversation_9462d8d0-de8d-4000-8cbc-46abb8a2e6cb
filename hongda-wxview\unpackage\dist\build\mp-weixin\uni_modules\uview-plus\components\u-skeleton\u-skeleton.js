"use strict";const t=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),e=require("../../libs/mixin/mixin.js"),n=require("../../libs/function/index.js"),a=require("../../libs/function/test.js"),s=require("../../../../common/vendor.js"),r={name:"u-skeleton",mixins:[i.mpMixin,e.mixin,t.props],data:()=>({width:0}),watch:{loading(){this.getComponentWidth()}},computed:{rowsArray(){/%$/.test(this.rowsHeight);const t=[];for(let i=0;i<this.rows;i++){let e={},s=a.test.array(this.rowsWidth)?this.rowsWidth[i]||(i===this.rows-1?"70%":"100%"):i===this.rows-1?"70%":this.rowsWidth,r=a.test.array(this.rowsHeight)?this.rowsHeight[i]||"18px":this.rowsHeight;e.marginTop=this.title||0!==i?this.title&&0===i?"20px":"12px":0,/%$/.test(s)?e.width=n.addUnit(this.width*parseInt(s)/100):e.width=n.addUnit(s),e.height=n.addUnit(r),t.push(e)}return t},uTitleWidth(){let t=0;return t=/%$/.test(this.titleWidth)?n.addUnit(this.width*parseInt(this.titleWidth)/100):n.addUnit(this.titleWidth),n.addUnit(t)}},mounted(){this.init()},methods:{addUnit:n.addUnit,init(){this.getComponentWidth()},async setNvueAnimation(){},async getComponentWidth(){await n.sleep(20),this.$uGetRect(".u-skeleton__wrapper__content").then((t=>{this.width=t.width}))}}};const h=s._export_sfc(r,[["render",function(t,i,e,n,a,r){return s.e({a:t.loading},t.loading?s.e({b:t.avatar},t.avatar?{c:s.n(`u-skeleton__wrapper__avatar--${t.avatarShape}`),d:s.n(t.animate&&"animate"),e:r.addUnit(t.avatarSize),f:r.addUnit(t.avatarSize)}:{},{g:t.title},t.title?{h:r.uTitleWidth,i:r.addUnit(t.titleHeight),j:s.n(t.animate&&"animate")}:{},{k:s.f(r.rowsArray,((t,i,e)=>({a:i,b:t.width,c:t.height,d:t.marginTop}))),l:s.n(t.animate&&"animate")}):{})}],["__scopeId","data-v-11ffa7e4"]]);wx.createComponent(h);
