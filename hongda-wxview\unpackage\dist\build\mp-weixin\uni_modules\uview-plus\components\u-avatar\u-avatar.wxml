<view class="{{['u-avatar', 'data-v-f570534d', m]}}" style="{{n + ';' + o}}" bindtap="{{p}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><open-data wx:if="{{a}}" class="data-v-f570534d" type="userAvatarUrl" style="{{b}}"/><block wx:if="{{c}}"></block><u-icon wx:elif="{{d}}" class="data-v-f570534d" u-i="f570534d-0" bind:__l="__l" u-p="{{e}}"></u-icon><up-text wx:elif="{{f}}" class="data-v-f570534d" u-i="f570534d-1" bind:__l="__l" u-p="{{g}}"></up-text><image wx:else class="{{['u-avatar__image', 'data-v-f570534d', h]}}" src="{{i}}" mode="{{j}}" binderror="{{k}}" style="{{l}}"></image></block></view>