"use strict";const e=require("./dispatchRequest.js"),t=require("./InterceptorManager.js"),r=require("./mergeConfig.js"),s=require("./defaults.js"),i=require("../utils.js"),d=require("../utils/clone.js");exports.Request=class{constructor(e={}){i.isPlainObject(e)||(e={},console.warn("设置全局参数必须接收一个Object")),this.config=d.clone({...s.defaults,...e}),this.interceptors={request:new t.InterceptorManager,response:new t.InterceptorManager}}setConfig(e){this.config=e(this.config)}middleware(t){t=r.mergeConfig(this.config,t);const s=[e.dispatchRequest,void 0];let i=Promise.resolve(t);for(this.interceptors.request.forEach((e=>{s.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((e=>{s.push(e.fulfilled,e.rejected)}));s.length;)i=i.then(s.shift(),s.shift());return i}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,r={}){return this.middleware({url:e,data:t,method:"POST",...r})}put(e,t,r={}){return this.middleware({url:e,data:t,method:"PUT",...r})}delete(e,t,r={}){return this.middleware({url:e,data:t,method:"DELETE",...r})}connect(e,t,r={}){return this.middleware({url:e,data:t,method:"CONNECT",...r})}head(e,t,r={}){return this.middleware({url:e,data:t,method:"HEAD",...r})}options(e,t,r={}){return this.middleware({url:e,data:t,method:"OPTIONS",...r})}trace(e,t,r={}){return this.middleware({url:e,data:t,method:"TRACE",...r})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}};
