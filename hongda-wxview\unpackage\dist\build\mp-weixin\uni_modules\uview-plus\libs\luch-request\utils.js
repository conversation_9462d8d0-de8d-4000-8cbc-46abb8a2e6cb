"use strict";const{toString:t}=Object.prototype;function e(e){return"[object Array]"===t.call(e)}function o(t,o){if(null!=t)if("object"!=typeof t&&(t=[t]),e(t))for(let e=0,n=t.length;e<n;e++)o.call(null,t[e],e,t);else for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.call(null,t[e],e,t)}exports.deepMerge=function t(){const e={};function n(o,n){"object"==typeof e[n]&&"object"==typeof o?e[n]=t(e[n],o):e[n]="object"==typeof o?t({},o):o}for(let r=0,c=arguments.length;r<c;r++)o(arguments[r],n);return e},exports.forEach=o,exports.isArray=e,exports.isDate=function(e){return"[object Date]"===t.call(e)},exports.isObject=function(t){return null!==t&&"object"==typeof t},exports.isPlainObject=function(t){return"[object Object]"===Object.prototype.toString.call(t)},exports.isURLSearchParams=function(t){return"undefined"!=typeof URLSearchParams&&t instanceof URLSearchParams},exports.isUndefined=function(t){return void 0===t};
