{"version": 3, "file": "HeaderComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9IZWFkZXJDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"header-container\" :style=\"headerStyle\">\r\n    <view class=\"header-content\">\r\n      <view class=\"search-bar-wrapper\" :style=\"searchBarStyle\" @click=\"handleSearchClick\">\r\n        <text class=\"search-placeholder\">搜索资讯、活动</text>\r\n        <image\r\n            v-if=\"searchIconUrl\"\r\n            class=\"search-icon\"\r\n            :src=\"searchIconUrl\"\r\n            mode=\"aspectFit\"\r\n        ></image>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue'; // [修改] 导入 computed\r\n\r\n// ====================================================================================\r\n// --- 您可以在这里调整布局的细节 ---\r\n//\r\n// 所有单位都是像素(px)，会自动处理rpx转换和屏幕适配\r\n// ====================================================================================\r\n\r\n/** * 【参数1】头部容器左右的内边距 (padding)。\r\n * 注意：修改此值后，也需要同步修改下面 <style> 部分 .header-content 的 padding 值。\r\n */\r\nconst HEADER_HORIZONTAL_PADDING = uni.upx2px(24); // 默认 24rpx\r\n\r\n/** * 【参数2】搜索栏右侧与胶囊按钮左侧的间距。\r\n */\r\nconst GAP_BETWEEN_SEARCH_AND_CAPSULE = 10; // 默认 10px 的间距\r\n\r\n/** * 【参数3】搜索栏高度的微调值。\r\n * 设置为 0  : 搜索栏与胶囊等高。\r\n * 设置为 -4 : 搜索栏比胶囊矮 4px。\r\n * 设置为 4  : 搜索栏比胶囊高 4px。\r\n */\r\nconst SEARCH_BAR_HEIGHT_ADJUSTMENT = 0; // 默认等高\r\n\r\n/** * 【参数4】头部容器在胶囊上下方增加的额外垂直内边距。\r\n * 这会让整个头部变得更高，给胶囊和搜索栏更多的“呼吸空间”。\r\n */\r\nconst HEADER_EXTRA_VERTICAL_PADDING = uni.upx2px(0); // 默认在上下各增加 10rpx 的额外边距\r\n\r\n\r\n// ====================================================================================\r\n// --- 以下是自动计算逻辑，通常无需修改 ---\r\n// ====================================================================================\r\n\r\n// [新增] 定义 ref 来存储从全局缓存中读取的静态资源\r\nconst assets = ref(uni.getStorageSync('staticAssets') || {});\r\n\r\n// [新增] 为“搜索图标”创建计算属性\r\nconst searchIconUrl = computed(() => {\r\n  // 使用我们约定的“暗号” icon_home_search\r\n  return assets.value.icon_home_search || '';\r\n});\r\n\r\n\r\nconst headerStyle = ref({});\r\nconst searchBarStyle = ref({});\r\nconst emit = defineEmits(['height-calculated']);\r\n\r\nonMounted(() => {\r\n  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n  const systemInfo = uni.getSystemInfoSync();\r\n\r\n  // --- 动态计算样式 ---\r\n\r\n  // 1. 计算头部内容区域的高度\r\n  const headerContentHeight =\r\n      (menuButtonInfo.top - systemInfo.statusBarHeight) * 2 +\r\n      menuButtonInfo.height +\r\n      (HEADER_EXTRA_VERTICAL_PADDING * 2);\r\n\r\n  // 2. 计算搜索框的宽度\r\n  const searchBarWidth =\r\n      menuButtonInfo.left -\r\n      HEADER_HORIZONTAL_PADDING -\r\n      GAP_BETWEEN_SEARCH_AND_CAPSULE;\r\n\r\n  // 3. 计算搜索框的高度\r\n  const searchBarHeight = menuButtonInfo.height + SEARCH_BAR_HEIGHT_ADJUSTMENT;\r\n\r\n  // 4. 计算头部容器完整的总高度\r\n  const totalHeaderHeight = headerContentHeight + systemInfo.statusBarHeight;\r\n\r\n  // 将计算结果赋值给响应式变量\r\n  headerStyle.value = {\r\n    height: `${totalHeaderHeight}px`,\r\n    paddingTop: `${systemInfo.statusBarHeight}px`\r\n  };\r\n\r\n  searchBarStyle.value = {\r\n    width: `${searchBarWidth}px`,\r\n    height: `${searchBarHeight}px`,\r\n  };\r\n\r\n  // 向父组件发送计算出的实际总高度\r\n  emit('height-calculated', totalHeaderHeight);\r\n});\r\n\r\n\r\nconst handleSearchClick = () => {\r\n  uni.navigateTo({\r\n    url: '/pages_sub/pages_other/search'\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header-container {\r\n  width: 100%;\r\n  background-color: #023f98;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 999;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.header-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  /* 这里的 padding-left/right 需要与 JS 中的 HEADER_HORIZONTAL_PADDING 常量值保持一致 */\r\n  padding: 0 24rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.search-bar-wrapper {\r\n  background-color: #ffffff;\r\n  border-radius: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.search-bar-wrapper:active {\r\n  opacity: 0.8;\r\n}\r\n\r\n.search-placeholder {\r\n  font-size: 28rpx;\r\n  color: #9b9a9a;\r\n}\r\n\r\n/* [新增] 自定义搜索图标的样式 */\r\n.search-icon {\r\n  width: 25rpx;\r\n  height: 25rpx;\r\n  flex-shrink: 0;\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["uni", "ref", "computed", "onMounted"], "mappings": ";;AAgCA,MAAM,iCAAiC;AAOvC,MAAM,+BAA+B;;;;;AAXrC,UAAM,4BAA4BA,cAAG,MAAC,OAAO,EAAE;AAgB/C,UAAM,gCAAgCA,cAAG,MAAC,OAAO,CAAC;AAQlD,UAAM,SAASC,cAAG,IAACD,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA,CAAE;AAG3D,UAAM,gBAAgBE,cAAQ,SAAC,MAAM;AAEnC,aAAO,OAAO,MAAM,oBAAoB;AAAA,IAC1C,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAC7B,UAAM,OAAO;AAEbE,kBAAAA,UAAU,MAAM;AACd,YAAM,iBAAiBH,oBAAI;AAC3B,YAAM,aAAaA,oBAAI;AAKvB,YAAM,uBACD,eAAe,MAAM,WAAW,mBAAmB,IACpD,eAAe,SACd,gCAAgC;AAGrC,YAAM,iBACF,eAAe,OACf,4BACA;AAGJ,YAAM,kBAAkB,eAAe,SAAS;AAGhD,YAAM,oBAAoB,sBAAsB,WAAW;AAG3D,kBAAY,QAAQ;AAAA,QAClB,QAAQ,GAAG,iBAAiB;AAAA,QAC5B,YAAY,GAAG,WAAW,eAAe;AAAA,MAC7C;AAEE,qBAAe,QAAQ;AAAA,QACrB,OAAO,GAAG,cAAc;AAAA,QACxB,QAAQ,GAAG,eAAe;AAAA,MAC9B;AAGE,WAAK,qBAAqB,iBAAiB;AAAA,IAC7C,CAAC;AAGD,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;;;;;;;;;;;;;;;AC5GA,GAAG,gBAAgB,SAAS;"}