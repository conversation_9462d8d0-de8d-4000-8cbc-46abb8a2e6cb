"use strict";const e=require("../../common/vendor.js"),o=require("../../api/data/user.js"),n=require("./api/data/policy.js");if(!Array){e.resolveComponent("up-navbar")()}Math;const l=Object.assign({name:"LoginPage"},{__name:"login",setup(l){const s=e.ref(!1),c=e.ref(""),a=e.ref(""),t=e.ref({userAgreement:null,privacyPolicy:null}),r=()=>{s.value=!s.value},i=async n=>{if(console.log("=== 开始微信手机号快捷登录流程 ==="),console.log("授权事件详情:",n),console.log("e.detail:",n.detail),console.log("e.detail.code:",n.detail.code),!n.detail.code)return console.log("微信未返回授权code"),console.log("错误信息:",n.detail.errMsg),console.log("可能原因：1. 用户拒绝授权 2. 配置问题 3. 网络异常"),void e.index.showToast({title:"获取手机号失败，请重试",icon:"none"});const l=n.detail.code;console.log("获取到微信手机号授权码:",l),e.index.showLoading({title:"正在登录..."});try{e.index.removeStorageSync("token"),e.index.removeStorageSync("userInfo"),console.log("开始执行uni.login...");const n=await e.index.login();console.log("uni.login结果:",n);const a=n.code;console.log("获得loginCode:",a);const t=await o.wxLoginApi(a);console.log("后端登录成功:",t);const r=t.token;e.index.setStorageSync("token",r),e.index.setStorageSync("userInfo",t.userInfo),console.log("使用phoneCode:",l),console.log("使用token:",r);const i=await o.getPhoneNumberApi(l);console.log("获取手机号成功:",i),console.log("开始更新本地用户信息...");try{const o=e.index.getStorageSync("userInfo");if(console.log("当前userInfo:",o),o)console.log("更新现有userInfo的手机号"),o.phoneNumber=i.phoneNumber,e.index.setStorageSync("userInfo",o);else{console.log("userInfo为空，创建新的用户信息对象");const o={phoneNumber:i.phoneNumber};e.index.setStorageSync("userInfo",o)}console.log("用户信息更新完成")}catch(s){throw console.error("更新用户信息时出错:",s),s}console.log("登录流程完全成功，最终保存的数据:"),console.log("- token:",e.index.getStorageSync("token")),console.log("- userInfo:",e.index.getStorageSync("userInfo")),console.log("准备隐藏加载提示..."),e.index.hideLoading(),console.log("准备显示成功提示..."),e.index.showToast({title:"登录成功",icon:"success"}),console.log("开始上报协议同意记录...");try{await y()}catch(c){console.error("协议同意记录上报异常:",c)}console.log("设置延迟返回定时器..."),setTimeout((()=>{console.log("定时器触发，准备返回上一页...");try{const o=e.index.getStorageSync("loginBackPage");o?(console.log("检测到指定的返回页面:",o),e.index.navigateBack({success:()=>{console.log("成功返回上一页")},fail:n=>{console.error("返回上一页失败，尝试直接跳转到指定页面:",n),o.startsWith("/pages/")&&e.index.redirectTo({url:o,fail:()=>{e.index.switchTab({url:"/pages/index/index"})}})}})):e.index.navigateBack({success:()=>{console.log("成功返回上一页")},fail:o=>{console.error("返回上一页失败，跳转到首页:",o),e.index.switchTab({url:"/pages/index/index"})}})}catch(o){console.warn("检查返回页面标记失败:",o),e.index.navigateBack({fail:o=>{console.error("返回上一页失败，跳转到首页:",o),e.index.switchTab({url:"/pages/index/index"})}})}}),2e3),console.log("登录方法执行完成，等待定时器触发返回...")}catch(c){console.error("登录过程中发生错误:",c),console.error("错误详情:",{message:c.message,stack:c.stack,name:c.name}),e.index.hideLoading();const o=c.message||"网络请求失败";console.error("显示错误提示:",o),e.index.showToast({title:o,icon:"none",duration:3e3})}},g=()=>{s.value||(console.log("用户未同意协议"),e.index.showToast({title:"请先同意用户协议和隐私政策",icon:"none"}))},u=()=>{console.log("点击用户协议链接"),e.index.navigateTo({url:"/pages_sub/pages_other/policy?type=user_agreement",success:()=>{console.log("成功跳转到用户协议页面")},fail:o=>{console.error("跳转用户协议页面失败:",o),e.index.showToast({title:"页面跳转失败",icon:"none",duration:2e3})}})},d=()=>{console.log("点击隐私政策链接"),e.index.navigateTo({url:"/pages_sub/pages_other/policy?type=privacy_policy",success:()=>{console.log("成功跳转到隐私政策页面")},fail:o=>{console.error("跳转隐私政策页面失败:",o),e.index.showToast({title:"页面跳转失败",icon:"none",duration:2e3})}})},p=async()=>{try{console.log("开始加载协议版本信息...");const[e,o]=await Promise.all([n.getLatestPolicyApi("user_agreement"),n.getLatestPolicyApi("privacy_policy")]);e&&200===e.code&&e.data&&(t.value.userAgreement=e.data.version,console.log("用户协议版本:",e.data.version)),o&&200===o.code&&o.data&&(t.value.privacyPolicy=o.data.version,console.log("隐私政策版本:",o.data.version)),console.log("协议版本信息加载完成:",t.value)}catch(e){console.error("加载协议版本信息失败:",e),t.value.userAgreement="1.0.0",t.value.privacyPolicy="1.0.0"}},y=async()=>{try{console.log("=== 开始上报协议同意记录 ==="),console.log("当前协议版本信息:",t.value),t.value.userAgreement&&t.value.privacyPolicy||(console.log("协议版本信息不完整，尝试重新加载..."),await p(),console.log("重新加载后的协议版本信息:",t.value));const e=[];if(t.value.userAgreement?(console.log("准备上报用户协议，版本:",t.value.userAgreement),e.push(n.acceptPolicyApi("user_agreement",t.value.userAgreement))):(console.warn("用户协议版本为空，使用默认版本1.0.0"),e.push(n.acceptPolicyApi("user_agreement","1.0.0"))),t.value.privacyPolicy?(console.log("准备上报隐私政策，版本:",t.value.privacyPolicy),e.push(n.acceptPolicyApi("privacy_policy",t.value.privacyPolicy))):(console.warn("隐私政策版本为空，使用默认版本1.0.0"),e.push(n.acceptPolicyApi("privacy_policy","1.0.0"))),0===e.length)return void console.error("无法创建上报请求");console.log(`准备并行执行${e.length}个上报请求...`);const o=await Promise.all(e);console.log("协议同意记录上报原始结果:",o);let l=0;o.forEach(((e,o)=>{const n=0===o?"用户协议":"隐私政策";console.log(`${n}上报结果:`,e),e&&200===e.code?(l++,console.log(`${n}同意记录上报成功`)):console.error(`${n}同意记录上报失败:`,e)})),l===o.length?console.log("所有协议同意记录上报成功"):console.warn(`部分协议同意记录上报失败，成功数量: ${l}/${o.length}`)}catch(e){console.error("上报协议同意记录过程中发生异常:",e),console.error("错误详情:",{message:e.message,stack:e.stack})}};return e.onLoad((()=>{try{const o=e.index.getStorageSync("staticAssets");c.value=(null==o?void 0:o["login-bg"])||"",a.value=(null==o?void 0:o["logo-hd"])||""}catch(o){}p()})),(o,n)=>e.e({a:c.value,b:e.p({title:"登录",autoBack:!0,safeAreaInsetTop:!0,fixed:!0,placeholder:!0,bgColor:"transparent",zIndex:99,leftIconColor:"#333333",titleStyle:{fontFamily:"Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif",fontWeight:"normal",fontSize:"32rpx",color:"#000000",lineHeight:"44rpx"}}),c:a.value,d:s.value?"getPhoneNumber":"",e:e.o(i),f:e.o(g),g:s.value},(s.value,{}),{h:s.value?1:"",i:e.o(u),j:e.o(d),k:e.o(r)})}}),s=e._export_sfc(l,[["__scopeId","data-v-b7c422fe"]]);wx.createPage(s);
