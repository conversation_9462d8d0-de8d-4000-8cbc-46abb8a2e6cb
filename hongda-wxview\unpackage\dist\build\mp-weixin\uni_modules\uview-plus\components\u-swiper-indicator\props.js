"use strict";const r=require("../../libs/vue.js"),i=require("../../libs/config/props.js"),t=r.defineMixin({props:{length:{type:[String,Number],default:()=>i.props.swiperIndicator.length},current:{type:[String,Number],default:()=>i.props.swiperIndicator.current},indicatorActiveColor:{type:String,default:()=>i.props.swiperIndicator.indicatorActiveColor},indicatorInactiveColor:{type:String,default:()=>i.props.swiperIndicator.indicatorInactiveColor},indicatorMode:{type:String,default:()=>i.props.swiperIndicator.indicatorMode}}});exports.props=t;
