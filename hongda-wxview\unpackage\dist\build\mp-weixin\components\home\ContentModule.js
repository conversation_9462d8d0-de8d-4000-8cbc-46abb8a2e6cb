"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/countryPolicy.js");if(!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon")+e.resolveComponent("u-empty"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js"))();const t={__name:"ContentModule",props:{countryId:{type:[Number,String],required:!0},policyType:{type:String,required:!0}},setup(t){const l=t,o=e.ref([]),u=e.ref([]),n=e.ref(0),c=e.ref(!0),r=e.computed((()=>{if(c.value||0===u.value.length||!u.value[n.value])return[];return null===u.value[n.value].tagId?o.value:o.value.filter((e=>o.value))})),i=async()=>{if(c.value=!0,!l.countryId||!l.policyType)return o.value=[],u.value=[],void(c.value=!1);try{await d(),n.value=0,await p()}catch(e){console.error(`获取${l.policyType}政策数据失败:`,e),o.value=[],u.value=[]}finally{c.value=!1}},d=async()=>{try{const e=await a.getCountryPolicyTags({countryId:l.countryId,policyType:l.policyType});if(200===e.code){const a=e.data||[];u.value=[{tagId:null,name:"全部"},...a.map((e=>({tagId:e.tagId,name:e.tagName})))]}else console.warn("获取标签失败:",e),u.value=[{tagId:null,name:"全部"}]}catch(e){console.error("获取标签失败:",e),u.value=[{tagId:null,name:"全部"}]}},p=async()=>{try{const e=u.value[n.value],t=e&&null!==e.tagId?e.tagId:null,c=await a.listCountryPolicyArticle({countryId:l.countryId,policyType:l.policyType,tagId:t,pageNum:1,pageSize:100});o.value=c.rows||[]}catch(e){console.error(`获取${l.policyType}政策文章失败:`,e),o.value=[]}};return e.watch((()=>l.policyType),((e,a)=>{e&&e!==a&&i()})),e.watch((()=>l.countryId),((e,a)=>{e&&e!==a&&i()})),e.onMounted((()=>{i()})),(a,t)=>e.e({a:e.f(u.value,((a,t,l)=>({a:e.t(a.name),b:a.tagId||"all",c:n.value===t?1:"",d:e.o((e=>(async e=>{n.value=e,await p()})(t)),a.tagId||"all")}))),b:c.value},c.value?{c:e.p({mode:"circle",text:"加载中...",size:"24"})}:r.value.length>0?{e:e.f(r.value,((a,t,l)=>({a:e.t(a.title),b:"0d5e5c99-1-"+l,c:a.articleId,d:e.o((t=>{return l=a.articleId,void e.index.navigateTo({url:`/pages_sub/pages_country/policy_detail?id=${l}`});var l}),a.articleId)}))),f:e.p({name:"arrow-right",color:"#BFBFBF",size:"16"})}:{g:e.p({mode:"list",text:"暂无相关内容"})},{d:r.value.length>0})}},l=e._export_sfc(t,[["__scopeId","data-v-0d5e5c99"]]);wx.createComponent(l);
