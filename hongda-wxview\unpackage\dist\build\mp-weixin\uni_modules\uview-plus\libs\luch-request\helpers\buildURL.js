"use strict";const e=require("../utils.js");function i(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}exports.buildURL=function(r,t){if(!t)return r;let n;if(e.isURLSearchParams(t))n=t.toString();else{const r=[];e.forEach(t,((t,n)=>{null!=t&&(e.isArray(t)?n=`${n}[]`:t=[t],e.forEach(t,(t=>{e.isDate(t)?t=t.toISOString():e.isObject(t)&&(t=JSON.stringify(t)),r.push(`${i(n)}=${i(t)}`)})))})),n=r.join("&")}if(n){const e=r.indexOf("#");-1!==e&&(r=r.slice(0,e)),r+=(-1===r.indexOf("?")?"?":"&")+n}return r};
