"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),n=require("../../../../common/vendor.js"),o={name:"u-safe-bottom",mixins:[t.mpMixin,i.mixin,e.props],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){const e={};return e.height=s.addUnit(s.getWindowInfo().safeAreaInsets.bottom,"px"),s.deepMerge(e,s.addStyle(this.customStyle))}},mounted(){this.isNvue=!0}};const r=n._export_sfc(o,[["render",function(e,t,i,s,o,r){return{a:n.s(r.style),b:n.n(!o.isNvue&&"u-safe-area-inset-bottom")}}],["__scopeId","data-v-fc255804"]]);wx.createComponent(r);
