"use strict";const e=require("./props.js"),o=require("../../libs/mixin/mpMixin.js"),t=require("../../libs/mixin/mixin.js"),i=require("../../libs/function/index.js"),s=require("../../../../common/vendor.js"),n={name:"u-popup",mixins:[o.mpMixin,t.mixin,e.props],data(){return{overlayDuration:this.duration+50}},watch:{show(e,o){if(!0===e){const e=this.$children;this.retryComputedComponentRect(e)}}},computed:{transitionStyle(){const e={zIndex:this.zIndex,position:"fixed",display:"flex"};return e[this.mode]=0,"left"===this.mode||"right"===this.mode?i.deepMerge(e,{bottom:0,top:0}):"top"===this.mode||"bottom"===this.mode?i.deepMerge(e,{left:0,right:0}):"center"===this.mode?i.deepMerge(e,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0}):void 0},contentStyle(){const e={};if(i.getWindowInfo(),"center"!==this.mode&&(e.flex=1),this.bgColor&&(e.backgroundColor=this.bgColor),this.round){const o=i.addUnit(this.round);"top"===this.mode?(e.borderBottomLeftRadius=o,e.borderBottomRightRadius=o):"bottom"===this.mode?(e.borderTopLeftRadius=o,e.borderTopRightRadius=o):"center"===this.mode&&(e.borderRadius=o)}return i.deepMerge(e,i.addStyle(this.customStyle))},position(){return"center"===this.mode?this.zoom?"fade-zoom":"fade":"left"===this.mode?"slide-left":"right"===this.mode?"slide-right":"bottom"===this.mode?"slide-up":"top"===this.mode?"slide-down":void 0}},emits:["open","close","click","update:show"],methods:{overlayClick(){this.closeOnClickOverlay&&(this.$emit("update:show",!1),this.$emit("close"))},open(e){this.$emit("update:show",!0)},close(e){this.$emit("update:show",!1),this.$emit("close")},afterEnter(){this.$emit("open")},clickHandler(){"center"===this.mode&&this.overlayClick(),this.$emit("click")},retryComputedComponentRect(e){const o=["u-calendar-month","u-album","u-collapse-item","u-dropdown","u-index-item","u-index-list","u-line-progress","u-list-item","u-rate","u-read-more","u-row","u-row-notice","u-scroll-list","u-skeleton","u-slider","u-steps-item","u-sticky","u-subsection","u-swipe-action-item","u-tabbar","u-tabs","u-tooltip"];for(let t=0;t<e.length;t++){const s=e[t],n=s.$children;o.includes(s.$options.name)&&"function"==typeof(null==s?void 0:s.init)&&i.sleep(50).then((()=>{s.init()})),n.length&&this.retryComputedComponentRect(n)}}}};if(!Array){(s.resolveComponent("u-overlay")+s.resolveComponent("u-status-bar")+s.resolveComponent("u-icon")+s.resolveComponent("u-safe-bottom")+s.resolveComponent("u-transition"))()}Math||((()=>"../u-overlay/u-overlay.js")+(()=>"../u-status-bar/u-status-bar.js")+(()=>"../u-icon/u-icon.js")+(()=>"../u-safe-bottom/u-safe-bottom.js")+(()=>"../u-transition/u-transition.js"))();const r=s._export_sfc(n,[["render",function(e,o,t,i,n,r){return s.e({a:s.o(((...e)=>r.open&&r.open(...e))),b:e.overlay},e.overlay?{c:s.o(r.overlayClick),d:s.p({show:e.show,zIndex:e.zIndex,duration:n.overlayDuration,customStyle:e.overlayStyle,opacity:e.overlayOpacity})}:{},{e:e.safeAreaInsetTop},(e.safeAreaInsetTop,{}),{f:e.closeable},e.closeable?{g:s.p({name:"close",color:"#909399",size:"18",bold:!0}),h:s.o(((...e)=>r.close&&r.close(...e))),i:s.n("u-popup__content__close--"+e.closeIconPos)}:{},{j:e.safeAreaInsetBottom},(e.safeAreaInsetBottom,{}),{k:s.s(r.contentStyle),l:s.o(((...o)=>e.noop&&e.noop(...o))),m:s.o(((...o)=>e.noop&&e.noop(...o))),n:s.o(r.afterEnter),o:s.o(r.clickHandler),p:s.p({show:e.show,customStyle:r.transitionStyle,mode:r.position,duration:e.duration}),q:s.n(e.customClass),r:0==e.show?"0px":"",s:0==e.show?"0px":""})}],["__scopeId","data-v-a61cf373"]]);wx.createComponent(r);
