"use strict";const t=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),p=t.defineMixin({props:{modelValue:{type:[String,Number],default:()=>e.props.input.value},type:{type:String,default:()=>e.props.input.type},fixed:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.input.fixed},disabled:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.input.disabled},disabledColor:{type:String,default:()=>e.props.input.disabledColor},clearable:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.input.clearable},password:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.input.password},maxlength:{type:[String,Number],default:()=>e.props.input.maxlength},placeholder:{type:String,default:()=>e.props.input.placeholder},placeholderClass:{type:String,default:()=>e.props.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>e.props.input.placeholderStyle},showWordLimit:{type:Boolean,default:()=>e.props.input.showWordLimit},confirmType:{type:String,default:()=>e.props.input.confirmType},confirmHold:{type:Boolean,default:()=>e.props.input.confirmHold},holdKeyboard:{type:Boolean,default:()=>e.props.input.holdKeyboard},focus:{type:Boolean,default:()=>e.props.input.focus},autoBlur:{type:Boolean,default:()=>e.props.input.autoBlur},disableDefaultPadding:{type:Boolean,default:()=>e.props.input.disableDefaultPadding},cursor:{type:[String,Number],default:()=>e.props.input.cursor},cursorSpacing:{type:[String,Number],default:()=>e.props.input.cursorSpacing},selectionStart:{type:[String,Number],default:()=>e.props.input.selectionStart},selectionEnd:{type:[String,Number],default:()=>e.props.input.selectionEnd},adjustPosition:{type:Boolean,default:()=>e.props.input.adjustPosition},inputAlign:{type:String,default:()=>e.props.input.inputAlign},fontSize:{type:[String,Number],default:()=>e.props.input.fontSize},color:{type:String,default:()=>e.props.input.color},prefixIcon:{type:String,default:()=>e.props.input.prefixIcon},prefixIconStyle:{type:[String,Object],default:()=>e.props.input.prefixIconStyle},suffixIcon:{type:String,default:()=>e.props.input.suffixIcon},suffixIconStyle:{type:[String,Object],default:()=>e.props.input.suffixIconStyle},border:{type:String,default:()=>e.props.input.border},readonly:{type:Boolean,default:()=>e.props.input.readonly},shape:{type:String,default:()=>e.props.input.shape},formatter:{type:[Function,null],default:()=>e.props.input.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}});exports.props=p;
