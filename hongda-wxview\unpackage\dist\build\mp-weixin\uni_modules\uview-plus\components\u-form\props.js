"use strict";const e=require("../../libs/vue.js"),r=require("../../libs/config/props.js"),o=e.defineMixin({props:{model:{type:Object,default:()=>r.props.form.model},rules:{type:[Object,Function,Array],default:()=>r.props.form.rules},errorType:{type:String,default:()=>r.props.form.errorType},borderBottom:{type:Boolean,default:()=>r.props.form.borderBottom},labelPosition:{type:String,default:()=>r.props.form.labelPosition},labelWidth:{type:[String,Number],default:()=>r.props.form.labelWidth},labelAlign:{type:String,default:()=>r.props.form.labelAlign},labelStyle:{type:Object,default:()=>r.props.form.labelStyle}}});exports.props=o;
