"use strict";const i=require("./icons.js"),t=require("./props.js"),e=require("../../libs/config/config.js"),s=require("../../libs/mixin/mpMixin.js"),n=require("../../libs/mixin/mixin.js"),o=require("../../libs/function/index.js"),l=require("./util.js"),c=require("../../../../common/vendor.js"),r={name:"u-icon",beforeCreate(){l.fontUtil.loadFont()},data:()=>({}),emits:["click"],mixins:[s.mpMixin,n.mixin,t.props],computed:{uClasses(){let i=[];return i.push(this.customPrefix+"-"+this.name),"uicon"==this.customPrefix?i.push("u-iconfont"):i.push(this.customPrefix),this.color&&e.config.type.includes(this.color)&&i.push("u-icon__icon--"+this.color),i},iconStyle(){let i={};return i={fontSize:o.addUnit(this.size),lineHeight:o.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:o.addUnit(this.top)},"uicon"!==this.customPrefix&&(i.fontFamily=this.customPrefix),this.color&&!e.config.type.includes(this.color)&&(i.color=this.color),i},isImg(){return-1!==this.name.indexOf("/")},imgStyle(){let i={};return i.width=this.width?o.addUnit(this.width):o.addUnit(this.size),i.height=this.height?o.addUnit(this.height):o.addUnit(this.size),i},icon(){return"uicon"!==this.customPrefix?e.config.customIcons[this.name]||this.name:i.icons["uicon-"+this.name]||this.name}},methods:{addStyle:o.addStyle,addUnit:o.addUnit,clickHandler(i){this.$emit("click",this.index,i),this.stop&&this.preventEvent(i)}}};const d=c._export_sfc(r,[["render",function(i,t,e,s,n,o){return c.e({a:o.isImg},o.isImg?{b:i.name,c:i.imgMode,d:c.s(o.imgStyle),e:c.s(o.addStyle(i.customStyle))}:{f:c.t(o.icon),g:c.n(o.uClasses),h:c.s(o.iconStyle),i:c.s(o.addStyle(i.customStyle)),j:i.hoverClass},{k:""!==i.label},""!==i.label?{l:c.t(i.label),m:i.labelColor,n:o.addUnit(i.labelSize),o:"right"==i.labelPos?o.addUnit(i.space):0,p:"bottom"==i.labelPos?o.addUnit(i.space):0,q:"left"==i.labelPos?o.addUnit(i.space):0,r:"top"==i.labelPos?o.addUnit(i.space):0}:{},{s:c.o(((...i)=>o.clickHandler&&o.clickHandler(...i))),t:c.n("u-icon--"+i.labelPos)})}],["__scopeId","data-v-b20449e2"]]);wx.createComponent(d);
