<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaArticleMapper">
    <resultMap type="HongdaArticle" id="HongdaArticleResult">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="summary" column="summary"/>
        <result property="coverImageUrl" column="cover_image_url"/>
        <result property="source" column="source"/>
        <result property="content" column="content"/>
        <result property="status" column="status"/>
        <result property="publishTime" column="publish_time"/>
        <result property="viewCount" column="view_count"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="region" column="region"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="tags" column="tags"/>
    </resultMap>

    <sql id="selectHongdaArticleVo">
        select a.id,
               a.title,
               a.summary,
               a.cover_image_url,
               a.source,
               a.content,
               a.status,
               a.publish_time,
               a.view_count,
               a.sort_order,
               -- [新增] 增加 region 字段查询
               a.region,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               COALESCE(
                       (SELECT JSON_ARRAYAGG(
                                       JSON_OBJECT('id', t.id, 'name', t.name)
                               )
                        FROM hongda_article_tag_relation atr
                                 JOIN hongda_tag t ON atr.tag_id = t.id
                        WHERE atr.article_id = a.id),
                       '[]'
               ) as tags
        from hongda_article a
    </sql>


    <select id="selectHongdaArticleList" parameterType="com.hongda.content.domain.HongdaArticle" resultMap="HongdaArticleResult">
        <include refid="selectHongdaArticleVo"/>
        <where>
            <if test="title != null and title != ''">
                and a.title like concat('%', #{title}, '%')
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>

            <if test="params.region != null and params.region != ''">
                and a.region = #{params.region}
            </if>

            <if test="params.beginPublishTime != null and params.beginPublishTime != ''">
                and a.publish_time &gt;= #{params.beginPublishTime}
            </if>

            <if test="tagIds != null and tagIds.size > 0">
                and a.id IN (
                SELECT DISTINCT atr.article_id FROM hongda_article_tag_relation atr
                WHERE atr.tag_id IN
                <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                )
            </if>
        </where>

        <if test="orderByColumn != null and orderByColumn != ''">
            ORDER BY ${orderByColumn} ${isAsc}
        </if>
    </select>

    <select id="selectHongdaArticleById" parameterType="Long" resultMap="HongdaArticleResult">
        <include refid="selectHongdaArticleVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHongdaArticle" parameterType="HongdaArticle"
            useGeneratedKeys="true" keyProperty="id">
        insert into hongda_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="summary != null">summary,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="source != null">source,</if>
            <if test="content != null">content,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="summary != null">#{summary},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="source != null">#{source},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">updateTime,</if>
        </trim>
    </insert>

    <update id="updateHongdaArticle" parameterType="HongdaArticle">
        update hongda_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="source != null">source = #{source},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaArticleById" parameterType="Long">
        delete
        from hongda_article
        where id = #{id}
    </delete>

    <delete id="deleteHongdaArticleByIds" parameterType="String">
        delete from hongda_article where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTagIdsByArticleId" parameterType="Long" resultType="Long">
        select tag_id
        from hongda_article_tag_relation
        where article_id = #{articleId}
    </select>

    <insert id="batchArticleTag">
        insert into hongda_article_tag_relation(article_id, tag_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.articleId},#{item.tagId})
        </foreach>
    </insert>

    <delete id="deleteArticleTagByArticleId" parameterType="Long">
        delete
        from hongda_article_tag_relation
        where article_id = #{articleId}
    </delete>

    <!-- 将下面的 <update> 标签添加到您现有的 HongdaArticleMapper.xml 文件中 -->
    <update id="incrementViewCount">
        update hongda_article
        set view_count = view_count + 1
        where id = #{articleId}
    </update>

    <select id="selectPublishedArticleListForMiniProgram" parameterType="com.hongda.content.domain.HongdaArticle" resultMap="HongdaArticleResult">
        <include refid="selectHongdaArticleVo"/>
        <where>
            a.status = '1'
            <if test="title != null and title != ''">
                and a.title like concat('%', #{title}, '%')
            </if>

            <if test="region != null and region != ''">
                and a.region = #{region}
            </if>

            <if test="tagIds != null and tagIds.size > 0">
                and a.id IN (
                SELECT DISTINCT atr.article_id FROM hongda_article_tag_relation atr
                WHERE atr.tag_id IN
                <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                )
            </if>
        </where>
        <if test="orderByColumn != null and orderByColumn != ''">
            ORDER BY ${orderByColumn} ${isAsc}
        </if>
    </select>

    <select id="selectPublishedArticleByIdForMiniProgram" parameterType="Long" resultMap="HongdaArticleResult">
        <include refid="selectHongdaArticleVo"/>
        where a.id = #{id} and a.status = '1'
    </select>
</mapper>