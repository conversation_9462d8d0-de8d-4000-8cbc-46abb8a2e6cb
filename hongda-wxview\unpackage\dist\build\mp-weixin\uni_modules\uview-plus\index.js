"use strict";const i=require("../../common/vendor.js"),e=require("./libs/mixin/mixin.js"),o=require("./libs/mixin/mpMixin.js"),r=require("./libs/util/route.js"),n=require("./libs/function/colorGradient.js"),t=require("./libs/function/test.js"),s=require("./libs/function/debounce.js"),l=require("./libs/function/throttle.js"),c=require("./libs/function/calc.js"),u=require("./libs/function/index.js"),x=require("./libs/config/config.js"),p=require("./libs/config/props.js"),a=require("./libs/config/zIndex.js"),d=require("./libs/config/color.js"),b=require("./libs/function/platform.js"),f=require("./libs/function/http.js");function g(i){u.index.shallowMerge(x.config,i.config||{}),u.index.shallowMerge(p.props,i.props||{}),u.index.shallowMerge(d.color,i.color||{}),u.index.shallowMerge(a.zIndex,i.zIndex||{})}u.index.setConfig=g;const m={route:r.route,date:u.index.timeFormat,colorGradient:n.colorGradient.colorGradient,hexToRgb:n.colorGradient.hexToRgb,rgbToHex:n.colorGradient.rgbToHex,colorToRgba:n.colorGradient.colorToRgba,test:t.test,type:["primary","success","error","warning","info"],http:f.http,config:x.config,zIndex:a.zIndex,debounce:s.debounce,throttle:l.throttle,calc:c.calc,mixin:e.mixin,mpMixin:o.mpMixin,props:p.props,...u.index,color:d.color,platform:b.platform},j={install:(o,r="")=>{if(r){i.index.upuiParams=r;let e=r();e.httpIns&&e.httpIns(f.http),e.options&&g(e.options)}i.index.$u=m,o.config.globalProperties.$u=m,o.mixin(e.mixin)}};exports.uviewPlus=j;
