<template>
  <view class="event-card" @click="$emit('click', event)">
    <view class="card-left">
      <image :src="getFullImageUrl(event.coverImageUrl)" mode="aspectFill" class="event-image" :lazy-load="true"></image>
      <view v-if="event.registrationStatus !== undefined" :class="['status-tag', getRegistrationStatusClass(event.registrationStatus)]">
        {{ formatRegistrationStatus(event.registrationStatus) }}
      </view>
    </view>

    <view class="card-right">
      <text class="event-title">{{ event.title }}</text>

      <view class="event-info-row">
        <view class="time-location-item">
          <image class="event-info-icon" :src="listTimeIconUrl" mode="aspectFit"></image>
          <text class="info-text">{{ formatEventDate(event.startTime) }}</text>
        </view>
        <view class="time-location-item">
          <image class="event-info-icon" :src="listLocationIconUrl" mode="aspectFit"></image>
          <text class="info-text">{{ formatEventLocation(event) }}</text>
        </view>
      </view>

      <view class="event-info remaining-spots">
        <text class="spots-count">
          剩余名额: {{ calculateRemainingSpots(event.maxParticipants, event.registeredCount) }}
        </text>
      </view>
    </view>
  </view>
  
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js'
import { formatEventDate } from '@/utils/date.js'
import { getFullImageUrl } from '@/utils/image.js'
import { formatEventLocation } from '@/utils/location.js'

const props = defineProps({
  event: { type: Object, required: true }
})

// 静态资源 URL（不再使用本地兜底）
const listTimeIconUrl = ref('')
const listLocationIconUrl = ref('')

// 组件挂载时读取静态资源配置
onMounted(() => {
  const assets = uni.getStorageSync('staticAssets')
  
  listTimeIconUrl.value = assets?.list_time || ''
  listLocationIconUrl.value = assets?.list_location || ''
})

// formatEventLocation 函数已从 @/utils/location.js 导入

/**
 * 格式化报名状态文本
 */
const formatRegistrationStatus = (status) => {
  switch (status) {
    case 0: return '即将开始'
    case 1: return '报名中'
    case 2: return '报名截止'
    default: return '未知'
  }
}

/**
 * 获取报名状态样式类
 */
const getRegistrationStatusClass = (status) => {
  switch (status) {
    case 0: return 'not-started'  // 未开始
    case 1: return 'open'         // 报名中
    case 2: return 'ended'        // 已截止
    default: return 'unknown'
  }
}
</script>

<style lang="scss" scoped>
.event-card {
  width: 100%;
  height: 272rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  border: none;
  border-top: 2rpx solid #EEEEEE;
  border-bottom: 2rpx solid #EEEEEE;
  margin-bottom: 0rpx;
  padding: 24rpx 24rpx;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
}

.card-left {
  position: relative;
  width: 336rpx;
  height: 192rpx;
  flex-shrink: 0;
  margin-top: 16rpx;
  margin-bottom: 16rpx; 
  border-radius: 16rpx;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 16rpx;
}

.status-tag {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  width: 96rpx;
  height: 44rpx;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 6rpx 12rpx;

  color: #23232A;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: 500;
  font-size: 22rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.ended {
    background: #9B9A9A;
    color: #FFFFFF;
    width: 116rpx; // 报名截止文字较长，增加宽度
  }
  
  &.not-started {
    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
    color: #23232A;
    width: 116rpx; // 即将开始文字较长，增加宽度
  }
  
  &.open {
    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
    color: #23232A;
    // 报名中文字较短，保持默认宽度96rpx
  }
}

.card-right {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.event-title {
  width: 346rpx;
  height: 80rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.event-info-row {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 24rpx !important;
  margin-bottom: 18rpx !important;
  flex-wrap: nowrap !important;
}

.time-location-item {
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
  flex-shrink: 0 !important;
}

.event-info-icon {
  width: 32rpx !important;
  height: 32rpx !important;
  flex-shrink: 0 !important;
}

.info-text {
  width: 176rpx !important;
  height: 32rpx !important;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;
  font-weight: normal !important;
  font-size: 22rpx !important;
  color: #9B9A9A !important;
  text-align: left !important;
  font-style: normal !important;
  text-transform: none !important;
  line-height: 32rpx !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.remaining-spots {
  width: 154rpx;
  height: 40rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FB8620;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;

  .spots-count {
    width: 100%;
    height: 36rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
    font-weight: normal;
    font-size: 20rpx;
    color: #FB8620;
    text-align: center;
    font-style: normal;
    text-transform: none;
    line-height: 36rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>



