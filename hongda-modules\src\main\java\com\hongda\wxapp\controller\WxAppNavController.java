package com.hongda.wxapp.controller;

import com.hongda.common.annotation.Anonymous;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaNav;
import com.hongda.platform.service.IHongdaNavService;
import com.hongda.wxapp.domain.vo.NavVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Anonymous
@RestController
@RequestMapping("/api/v1/nav")
public class WxAppNavController extends BaseController {

    @Autowired
    private IHongdaNavService hongdaNavService;

    // Tab Bar 页面列表，用于判断跳转方式
    private static final List<String> TAB_BAR_PAGES = Arrays.asList(
            "/pages/index/index",
            "/pages/article/index",
            "/pages/event/index",
            "/pages/country/index",
            "/pages/profile/index"
    );

    @GetMapping("/list")
    public AjaxResult getNavListByPosition(@RequestParam("positionCode") String positionCode) {
        HongdaNav queryParams = new HongdaNav();
        queryParams.setPositionCode(positionCode);
        queryParams.setStatus(1); // 只查询已启用的导航项

        List<HongdaNav> navList = hongdaNavService.selectHongdaNavList(queryParams);

        List<NavVO> voList = navList.stream().map(nav -> {
            NavVO vo = new NavVO();
            BeanUtils.copyProperties(nav, vo);

            String linkType = nav.getLinkType();
            String linkTarget = nav.getLinkTarget();

            if (linkType == null || linkTarget == null) {
                return vo;
            }

            switch (linkType) {
                case "ARTICLE":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_article/detail?id=" + linkTarget);
                    break;
                case "EVENT":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_event/detail?id=" + linkTarget);
                    break;
                case "COUNTRY":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_country/detail?id=" + linkTarget);
                    break;
                case "PARK":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_other/park_detail?id=" + linkTarget);
                    break;
                case "CUSTOM_PAGE":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_other/custom_page?id=" + linkTarget);
                    break;
                // [新增] 增加对国别政策详情页的处理
                case "COUNTRY_POLICY":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_country/policy_detail?id=" + linkTarget);
                    break;
                case "PAGE":
                    if (TAB_BAR_PAGES.contains(linkTarget.split("\\?")[0])) {
                        vo.setLinkType("TAB_PAGE");
                    } else {
                        vo.setLinkType("INTERNAL_PAGE");
                    }
                    vo.setLinkTarget(linkTarget);
                    break;
                case "EXTERNAL":
                    vo.setLinkType("EXTERNAL_LINK");
                    vo.setLinkTarget(linkTarget);
                    break;
                case "NONE":
                    break;
            }
            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(voList);
    }
}