"use strict";const e=require("../../common/vendor.js"),t=require("../../api/content/countryPolicy.js"),n=require("../../utils/config.js"),o=require("../../utils/mpHtmlStyles.js");if(!Array){(e.resolveComponent("DetailSkeleton")+e.resolveComponent("uni-icons")+e.resolveComponent("u-empty"))()}Math||((()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js")+l+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js"))();const l=()=>"../../uni_modules/mp-html/components/mp-html/mp-html.js",a={__name:"policy_detail",setup(l){const a=e.index.upx2px(10),u=e.ref(0),i=e.ref(0),r=e.ref(0),s=()=>{e.index.navigateBack({delta:1})},c=n.IMAGE_BASE_URL,p=e.ref(null),v=e.ref(!0);return e.onLoad((async n=>{if((()=>{try{const t=e.index.getMenuButtonBoundingClientRect();u.value=t.top,i.value=t.height,r.value=t.bottom+a}catch(t){const n=e.index.getSystemInfoSync();u.value=n.statusBarHeight||20,i.value=44,r.value=u.value+i.value+a}})(),!n.id)return e.index.showToast({title:"参数错误",icon:"none"}),void(v.value=!1);try{const e=await t.getCountryPolicyArticle(n.id);if(200!==e.code||!e.data)throw new Error(e.msg||"文章不存在或已被删除");p.value=e.data}catch(o){console.error("获取国别政策文章失败:",o),p.value=null}finally{v.value=!1}})),(t,n)=>e.e({a:v.value},v.value?{}:p.value?{c:u.value+"px",d:e.p({type:"left",color:"#000000",size:"22"}),e:e.o(s),f:i.value+"px",g:e.unref(a)+"px",h:r.value+"px",i:e.t(p.value.title),j:e.t(p.value.createTime),k:e.p({content:p.value.content,domain:e.unref(c),"tag-style":e.unref(o.tagStyle),"container-style":e.unref(o.containerStyle),"preview-img":!0,"lazy-load":!0}),l:r.value+"px"}:{m:e.p({mode:"list",text:"文章不存在或已被删除"})},{b:p.value})}},u=e._export_sfc(a,[["__scopeId","data-v-0a7bfb4b"]]);wx.createPage(u);
