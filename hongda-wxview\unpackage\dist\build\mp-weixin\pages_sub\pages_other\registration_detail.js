"use strict";const e=require("../../common/vendor.js"),o=require("../../api/data/registration.js");if(!Array){(e.resolveComponent("u-navbar")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-button"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-navbar/u-navbar.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-button/u-button.js"))();const a={__name:"registration_detail",setup(a){const t=e.ref(!0),n=e.ref(""),r=e.ref(""),l=e.ref([]),i=e.ref(null),s=e.ref(null);e.onLoad((async o=>{if(n.value=o.eventId,r.value=decodeURIComponent(o.title||"活动详情"),!n.value)return e.index.showToast({title:"参数错误",icon:"error"}),void setTimeout((()=>{e.index.navigateBack()}),1500);await u()}));const u=async()=>{try{t.value=!0,console.log("开始加载报名详情，eventId:",n.value,"类型:",typeof n.value);const[e,r]=await Promise.all([o.getFormDefinitionApi(n.value),o.getMyRegistrationDetailApi(n.value)]);if(console.log("表单定义响应:",e),console.log("报名详情响应:",r),200!==e.code||!e.data)throw console.warn("获取表单定义失败:",e),new Error("获取表单定义失败");{let o;if("string"==typeof e.data)try{o=JSON.parse(e.data),console.log("解析后的表单定义:",o)}catch(a){throw console.error("解析表单定义失败:",a),new Error("表单配置格式错误")}else o=e.data;const t=o.fields||[];l.value=Array.isArray(t)?t:[],console.log("最终表单配置:",l.value)}if(200===r.code&&r.data){console.log("报名数据结构:",r.data);let e={};if(r.data.formData)if("string"==typeof r.data.formData)try{e=JSON.parse(r.data.formData),console.log("解析后的表单数据:",e)}catch(a){console.error("解析表单数据失败:",a),e={}}else e=r.data.formData||{};i.value=e,s.value=r.data,console.log("最终显示数据:",{submittedData:i.value,registrationInfo:s.value})}else{if(404!==r.code)throw console.warn("获取报名详情失败:",r),new Error(r.msg||"获取报名详情失败");console.log("用户未报名，显示空状态"),l.value=[],i.value=null}}catch(r){console.error("加载报名详情失败:",r),e.index.showToast({title:r.message||"加载失败",icon:"error"})}finally{t.value=!1}},c=(e,o)=>{if(!o||!e.options)return"未选择";const a=e.options.find((e=>e.value===o));return a?a.label:o},d=(e,o)=>{if(!o||!Array.isArray(o)||0===o.length)return"未选择";if(!e.options)return o.join(", ");return o.map((o=>{const a=e.options.find((e=>e.value===o));return a?a.label:o})).join(", ")},v=()=>{e.index.navigateTo({url:`/pages/event/registration?id=${n.value}&title=${encodeURIComponent(r.value)}`})};return(o,a)=>e.e({a:e.p({title:"报名详情",safeAreaInsetTop:!0,autoBack:!0}),b:e.t(r.value),c:t.value},t.value?{d:e.p({mode:"spinner",size:"40",color:"#007AFF"})}:l.value.length>0&&i.value?{f:e.f(l.value,((o,a,t)=>e.e({a:e.t(o.label),b:o.required},(o.required,{}),{c:"select"===o.type||"radio"===o.type},"select"===o.type||"radio"===o.type?{d:e.t(c(o,i.value[o.field]))}:"checkbox"===o.type?{f:e.t(d(o,i.value[o.field]))}:"textarea"===o.type?{h:e.t(i.value[o.field]||"未填写")}:{i:e.t(i.value[o.field]||"未填写")},{e:"checkbox"===o.type,g:"textarea"===o.type,j:o.field})))}:{g:e.o(v),h:e.p({type:"primary",size:"normal"})},{e:l.value.length>0&&i.value})}},t=e._export_sfc(a,[["__scopeId","data-v-a29e9131"]]);wx.createPage(t);
