"use strict";const e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),t=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),r=require("../../../../common/vendor.js"),n={name:"u-status-bar",mixins:[i.mpMixin,t.mixin,e.props],data:()=>({isH5:!1}),created(){},emits:["update:height"],computed:{style(){const e={};let i=s.getWindowInfo().statusBarHeight;return this.$emit("update:height",i),0==i?this.isH5=!0:e.height=s.addUnit(i,"px"),e.backgroundColor=this.bgColor,s.deepMerge(e,s.addStyle(this.customStyle))}}};const o=r._export_sfc(n,[["render",function(e,i,t,s,n,o){return{a:r.s(o.style),b:r.n(n.isH5&&"u-safe-area-inset-top")}}],["__scopeId","data-v-a75b2999"]]);wx.createComponent(o);
