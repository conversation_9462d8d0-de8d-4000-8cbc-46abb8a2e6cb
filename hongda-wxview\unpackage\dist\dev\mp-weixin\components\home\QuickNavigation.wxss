/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-213218ad {
  height: 100%;
  background-color: #f4f4f4;
}
.nav-container.data-v-213218ad {
  width: 100%;
  background-color: #ffffff;
  padding: 24rpx 0rpx;
  box-sizing: border-box;
}
.nav-grid.data-v-213218ad {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  /* [修改] 将行间距（文字与下方图标的距离）设置为 24rpx */
  row-gap: 24rpx;
  /* [修改] 将列间距恢复到一个更协调的值 */
}
.nav-item.data-v-213218ad {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* [修改] 将图标与对应文字的间距设置为 18rpx */
  gap: 18rpx;
  transition: transform 0.2s ease;
}
.nav-item.data-v-213218ad:active {
  transform: scale(0.95);
}
.nav-icon-wrapper.data-v-213218ad {
  /* 图标容器宽高仍为 80rpx */
  width: 80rpx;
  height: 80rpx;
  border-radius: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f8fafc;
}
.nav-icon.data-v-213218ad {
  width: 100%;
  height: 100%;
}
.nav-text.data-v-213218ad {
  font-size: 24rpx;
  color: #23232A;
  text-align: center;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}