"use strict";const i=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),r=require("../../libs/mixin/mixin.js"),o=require("../../libs/function/index.js"),e=require("../../../../common/vendor.js"),d={name:"u-image",mixins:[t.mpMixin,r.mixin,i.props],data(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{},show:!1}},watch:{src:{immediate:!0,handler(i){i?(this.isError=!1,this.loading=!0):this.isError=!0}}},computed:{transStyle(){let i={};return this.loading||this.isError||"100%"==this.width||"heightFix"!=this.mode?i.width=o.addUnit(this.width):i.width="fit-content",this.loading||this.isError||"100%"==this.height||"widthFix"!=this.mode?i.height=o.addUnit(this.height):i.height="fit-content",i},wrapStyle(){let i={};return this.loading||this.isError||"100%"==this.width||"heightFix"!=this.mode?i.width=o.addUnit(this.width):i.width="fit-content",this.loading||this.isError||"100%"==this.height||"widthFix"!=this.mode?i.height=o.addUnit(this.height):i.height="fit-content",i.borderRadius="circle"==this.shape?"10000px":o.addUnit(this.radius),i.overflow=this.radius>0?"hidden":"visible",o.deepMerge(i,o.addStyle(this.customStyle))}},mounted(){this.show=!0},emits:["click","error","load"],methods:{addUnit:o.addUnit,onClick(i){this.$emit("click",i)},onErrorHandler(i){this.loading=!1,this.isError=!0,this.$emit("error",i)},onLoadHandler(i){this.loading=!1,this.isError=!1,this.$emit("load",i),this.removeBgColor()},removeBgColor(){}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-transition"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-transition/u-transition.js"))();const s=e._export_sfc(d,[["render",function(i,t,r,o,d,s){return e.e({a:!d.isError},d.isError?{}:{b:i.src,c:i.mode,d:e.o(((...i)=>s.onErrorHandler&&s.onErrorHandler(...i))),e:e.o(((...i)=>s.onLoadHandler&&s.onLoadHandler(...i))),f:i.showMenuByLongpress,g:i.lazyLoad,h:s.addUnit(i.width),i:s.addUnit(i.height),j:"circle"==i.shape?"10000px":s.addUnit(i.radius)},{k:i.showLoading&&d.loading},i.showLoading&&d.loading?{l:e.p({name:i.loadingIcon}),m:"circle"==i.shape?"50%":s.addUnit(i.radius),n:this.bgColor,o:s.addUnit(i.width),p:s.addUnit(i.height)}:{},{q:i.showError&&d.isError&&!d.loading},i.showError&&d.isError&&!d.loading?{r:e.p({name:i.errorIcon}),s:"circle"==i.shape?"50%":s.addUnit(i.radius),t:this.bgColor,v:s.addUnit(i.width),w:s.addUnit(i.height)}:{},{x:e.o(((...i)=>s.onClick&&s.onClick(...i))),y:e.s(s.wrapStyle),z:e.s(d.backgroundStyle),A:e.s(s.transStyle),B:e.p({mode:"fade",show:d.show,duration:i.fade?1e3:0})})}],["__scopeId","data-v-b88d844f"]]);wx.createComponent(s);
