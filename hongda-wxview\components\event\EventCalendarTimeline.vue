<template>
  <view class="calendar-timeline-wrapper">
    <view class="vertical-timeline">
        <view class="corner-notch" :style="{ left: notchLeft }" />
      <view v-for="(group, index) in groups" :key="group.date" class="date-section">
        <view class="date-header">
          <view class="timeline-dot"></view>
          <text class="time-text">{{ group.formattedDate }}</text>
          <text class="weekday-text">{{ group.dayOfWeek }}</text>
        </view>

        <view class="line-connector" v-if="index < groups.length - 1">
          <view class="timeline-line"></view>
        </view>

        <view class="events-container">
          <view
            v-for="event in group.events"
            :key="event.id"
            class="compact-event-card"
            @click="$emit('click-item', event)"
          >
            <image :src="getFullImageUrl(event.iconUrl)" class="event-avatar" mode="aspectFill" />
            <view class="event-content">
              <text class="event-title-compact">{{ event.title }}</text>
              <view class="location-group">
                <view class="separator-line"></view>
                <view class="event-location-compact">
                  <image class="location-icon" :src="goldenLocationIconUrl" mode="aspectFit" />
                  <text class="location-text">{{ formatEventLocation(event) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="hasMore" class="no-more-divider" @click="$emit('view-more')">
      <text class="no-more-text">查看更多</text>
    </view>
  </view>
  <view class="timeline-bottom-spacer"></view>
</template>
  
  <script setup>
  import { ref, onMounted } from 'vue'
  import { getFullImageUrl } from '@/utils/image.js'
  
  defineProps({
    groups: { type: Array, required: true },
    hasMore: { type: Boolean, default: false },
    notchLeft: { type: String, default: '60rpx' }
  })
  
  // 静态资源 URL（不再使用本地兜底）
  const goldenLocationIconUrl = ref('')
  
  // 组件挂载时读取静态资源配置
  onMounted(() => {
    const assets = uni.getStorageSync('staticAssets')
    
    if (!assets) return
    goldenLocationIconUrl.value = assets['golden-location'] || assets['golden_location'] || ''
  })
  
  const formatEventLocation = (event) => {
    if (event.city && event.city.trim()) {
      return event.city.trim().replace(/市$/, '')
    }
    return '待定'
  }
  </script>
  
  <style lang="scss" scoped>
.calendar-timeline-wrapper {
  width: 100%;
}
  .vertical-timeline {
    width: calc(100% - 48rpx); /* 使用自适应宽度，避免固定宽度导致的溢出 */
    max-width: 702rpx; /* 设置最大宽度 */
    background: #F0F2F3;
    border-radius: 32rpx;
    margin: 0 24rpx;
    margin-top: 37rpx; 
    padding: 16rpx 0 20rpx 0;
    box-sizing: border-box;
    position: relative;
    min-height: 200rpx; 
    overflow: visible;
  }

  /* 左上角凸起的角 */
  .corner-notch {
    position: absolute;
    top: -16rpx; 
    left: 60rpx; 
    width: 0;
    height: 0;
    border-left: 14rpx solid transparent;
    border-right: 14rpx solid transparent;
    border-bottom: 16rpx solid #F0F2F3; 
    z-index: 103; 
  }
  
    .date-section {
    position: relative;
    padding-left: 72rpx;

    &:first-child {
      margin-top: 0;
    }

    &:not(:last-child) {
      margin-bottom: 24rpx;
    }
  }
  
  .date-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32rpx;
  
    .timeline-dot {
      position: absolute;
      left: 24rpx;
      top: 13rpx;
      width: 18rpx;
      height: 18rpx;
      background: #FFFFFF;
      border: 2rpx solid #023F98;
      border-radius: 50%;
      z-index: 2;
    }
  
    .time-text {
      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
      font-size: 32rpx;
      color: #023F98;
      line-height: 44rpx;
      font-weight: normal;
    }
  
    .weekday-text {
      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
      font-size: 22rpx;
      color: #66666E;
      line-height: 44rpx;
      font-weight: normal;
      margin-left: 15rpx;
    }
  }
  
  .line-connector {
    position: absolute;
    left: 32rpx;
    top: 44rpx;
    bottom: -197rpx;
    width: 2rpx;
  
    .timeline-line {
      height: 100%;
      width: 100%;
      background: #023F98;
    }
  }
  
  .date-section:last-child .line-connector .timeline-line {
    display: none;
  }
  
  .events-container {
    .compact-event-card {
      width: 590rpx;
      height: 100rpx;
      background: #FFFFFF;
      border: 2rpx solid #023F98;
      border-radius: 16rpx;
      padding: 0 24rpx;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      gap: 12rpx;
      transition: transform 0.2s ease;
      box-sizing: border-box;
  
      &:last-child { margin-bottom: 0; }
      &:active { transform: scale(0.98); }
  
      .event-avatar {
        width: 52rpx;
        height: 52rpx;
        border-radius: 50%;
        flex-shrink: 0;
        background-color: #f5f5f5;
      }
  
      .event-content {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        justify-content: flex-start;
  
        .event-title-compact {
          font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
          font-size: 28rpx;
          color: #23232A;
          font-weight: normal;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.2;
          margin-right: 24rpx;
        }
  
        .location-group {
          display: flex;
          align-items: center;
          flex-shrink: 0;
        }
  
        .separator-line {
          width: 2rpx;
          height: 40rpx;
          background: #FA841C;
          opacity: 0.3;
          flex-shrink: 0;
          margin-right: 24rpx;
        }
  
        .event-location-compact {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          gap: 4rpx;
  
          .location-icon {
            width: 32rpx;
            height: 32rpx;
            flex-shrink: 0;
          }
  
          .location-text {
            font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
            font-size: 22rpx;
            color: #452D03;
            font-weight: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
          }
        }
      }
    }
  }
  
  .no-more-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 0;
    margin-top: 60rpx;
  }

  .no-more-text {
    width: 120rpx;
    height: 34rpx;
    line-height: 34rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
    font-weight: normal;
    font-size: 24rpx;
    color: #9B9A9A;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  /* 底部占位 */
  .timeline-bottom-spacer {
    height: 220rpx; 
  }
  </style>