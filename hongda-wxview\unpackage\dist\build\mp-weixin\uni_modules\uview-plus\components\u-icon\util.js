"use strict";const o=require("../../../../common/vendor.js"),c=require("../../libs/config/config.js");let n={loadFont:function(o){let c,n=!1;return function(...i){return n||(c=o.apply(this,i),n=!0),c}}((()=>(o.index.loadFontFace({global:!0,family:"uicon-iconfont",source:'url("'+c.config.iconUrl+'")',success(){},fail(){}}),c.config.customIcon.family&&o.index.loadFontFace({global:!0,family:c.config.customIcon.family,source:'url("'+c.config.customIcon.url+'")',success(){},fail(){}}),!0)))};exports.fontUtil=n;
