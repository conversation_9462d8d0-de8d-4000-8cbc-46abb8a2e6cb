"use strict";const e=require("../../libs/vue.js"),p=require("../../libs/config/props.js"),r=e.defineMixin({props:{modelValue:{type:Array,default:()=>[]},hasInput:{type:Boolean,default:!1},inputProps:{type:Object,default:()=>({})},disabled:{type:Boolean,default:()=>p.props.picker.disabled},disabledColor:{type:String,default:()=>p.props.picker.disabledColor},placeholder:{type:String,default:()=>p.props.picker.placeholder},show:{type:<PERSON>olean,default:()=>p.props.picker.show},popupMode:{type:String,default:()=>p.props.picker.popupMode},showToolbar:{type:Boolean,default:()=>p.props.picker.showToolbar},title:{type:String,default:()=>p.props.picker.title},columns:{type:Array,default:()=>p.props.picker.columns},loading:{type:Boolean,default:()=>p.props.picker.loading},itemHeight:{type:[String,Number],default:()=>p.props.picker.itemHeight},cancelText:{type:String,default:()=>p.props.picker.cancelText},confirmText:{type:String,default:()=>p.props.picker.confirmText},cancelColor:{type:String,default:()=>p.props.picker.cancelColor},confirmColor:{type:String,default:()=>p.props.picker.confirmColor},visibleItemCount:{type:[String,Number],default:()=>p.props.picker.visibleItemCount},keyName:{type:String,default:()=>p.props.picker.keyName},valueName:{type:String,default:()=>p.props.picker.valueName},closeOnClickOverlay:{type:Boolean,default:()=>p.props.picker.closeOnClickOverlay},defaultIndex:{type:Array,default:()=>p.props.picker.defaultIndex},immediateChange:{type:Boolean,default:()=>p.props.picker.immediateChange},toolbarRightSlot:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:()=>p.props.picker.zIndex},bgColor:{type:String,default:()=>p.props.picker.bgColor},round:{type:[Boolean,String,Number],default:()=>p.props.picker.round},duration:{type:[String,Number],default:()=>p.props.picker.duration},overlayOpacity:{type:[Number,String],default:()=>p.props.picker.overlayOpacity}}});exports.props=r;
