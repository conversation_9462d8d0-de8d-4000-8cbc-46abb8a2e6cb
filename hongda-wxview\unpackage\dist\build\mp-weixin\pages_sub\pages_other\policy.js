"use strict";const e=require("../../common/vendor.js"),o=require("./api/data/policy.js");if(!Array){(e.resolveComponent("up-navbar")+e.resolveComponent("u-loading-icon")+e.resolveComponent("mp-html"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-navbar/u-navbar.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/mp-html/components/mp-html/mp-html.js"))();const a=Object.assign({name:"PolicyPage"},{__name:"policy",setup(a){const l=e.ref(!0),n=e.ref(!1),t=e.ref(""),r=e.ref(null),u=e.ref(""),i=e.ref("协议详情");e.onLoad((e=>{console.log("协议页面加载，参数:",e),e.type?(u.value=e.type,"user_agreement"===e.type?i.value="用户协议":"privacy_policy"===e.type&&(i.value="隐私政策"),s()):(n.value=!0,t.value="缺少协议类型参数",l.value=!1)}));const s=async()=>{console.log("开始加载协议内容，类型:",u.value),l.value=!0,n.value=!1,t.value="";try{const e=await o.getLatestPolicyApi(u.value);if(console.log("协议内容加载响应:",e),!e||200!==e.code||!e.data)throw new Error(e.msg||"获取协议内容失败");r.value=e.data,e.data.title&&(i.value=e.data.title),console.log("协议内容加载成功")}catch(a){console.error("协议内容加载失败:",a),n.value=!0,t.value=a.message||"网络请求失败，请检查网络连接",e.index.showToast({title:t.value,icon:"none",duration:3e3})}finally{l.value=!1}};return(o,a)=>e.e({a:e.p({title:i.value,autoBack:!0,safeAreaInsetTop:!0,fixed:!0,placeholder:!0,bgColor:"transparent",zIndex:99,leftIconColor:"#333333",titleStyle:{fontFamily:"Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif",fontWeight:"normal",fontSize:"32rpx",color:"#000000",lineHeight:"44rpx"}}),b:l.value},l.value?{c:e.p({mode:"spinner",color:"#023F98",size:60})}:n.value?{e:e.t(t.value),f:e.o(s)}:r.value?{h:e.p({content:r.value.contentHtml,"lazy-load":!0,selectable:!0,"show-img-menu":!1,"container-style":"font-size: 28rpx; line-height: 1.8; color: #111111; padding: 24rpx; background-color: #FFFFFF; word-break: break-word;"})}:{},{d:n.value,g:r.value})}}),l=e._export_sfc(a,[["__scopeId","data-v-9a655554"]]);wx.createPage(l);
