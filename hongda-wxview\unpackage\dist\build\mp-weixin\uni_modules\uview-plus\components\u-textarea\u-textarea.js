"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),n=require("../../libs/mixin/mixin.js"),i=require("../../libs/function/index.js"),a=require("../../../../common/vendor.js"),r={name:"u-textarea",mixins:[t.mpMixin,n.mixin,e.props],data:()=>({innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:e=>e}),created(){},watch:{modelValue:{immediate:!0,handler(e,t){this.innerValue=e,this.firstChange=!1,this.changeFromInner=!1}}},computed:{fieldStyle(){let e={};return e.height=i.addUnit(this.height),this.autoHeight&&(e.height="auto",e.minHeight=i.addUnit(this.height)),e},textareaClass(){let e=[],{border:t,disabled:n}=this;return"surround"===t&&(e=e.concat(["u-border","u-textarea--radius"])),"bottom"===t&&(e=e.concat(["u-border-bottom","u-textarea--no-radius"])),n&&e.push("u-textarea--disabled"),e.join(" ")},textareaStyle(){return i.deepMerge({},i.addStyle(this.customStyle))}},emits:["update:modelValue","linechange","focus","blur","change","confirm","keyboardheightchange"],methods:{addStyle:i.addStyle,addUnit:i.addUnit,setFormatter(e){this.innerFormatter=e},onFocus(e){this.$emit("focus",e)},onBlur(e){this.$emit("blur",e),i.formValidate(this,"blur")},onLinechange(e){this.$emit("linechange",e)},onInput(e){let{value:t=""}=e.detail||{};const n=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick((()=>{this.innerValue=n,this.valueChange()}))},valueChange(){const e=this.innerValue;this.$nextTick((()=>{this.$emit("update:modelValue",e),this.changeFromInner=!0,this.$emit("change",e),i.formValidate(this,"change")}))},onConfirm(e){this.$emit("confirm",e)},onKeyboardheightchange(e){this.$emit("keyboardheightchange",e)}}};const o=a._export_sfc(r,[["render",function(e,t,n,i,r,o){return a.e({a:r.innerValue,b:a.s(o.fieldStyle),c:e.placeholder,d:o.addStyle(e.placeholderStyle,"string"==typeof e.placeholderStyle?"string":"object"),e:e.placeholderClass,f:e.disabled,g:e.focus,h:e.autoHeight,i:e.fixed,j:e.cursorSpacing,k:e.cursor,l:e.showConfirmBar,m:e.selectionStart,n:e.selectionEnd,o:e.adjustPosition,p:e.disableDefaultPadding,q:e.holdKeyboard,r:e.maxlength,s:e.confirmType,t:e.ignoreCompositionEvent,v:a.o(((...e)=>o.onFocus&&o.onFocus(...e))),w:a.o(((...e)=>o.onBlur&&o.onBlur(...e))),x:a.o(((...e)=>o.onLinechange&&o.onLinechange(...e))),y:a.o(((...e)=>o.onInput&&o.onInput(...e))),z:a.o(((...e)=>o.onConfirm&&o.onConfirm(...e))),A:a.o(((...e)=>o.onKeyboardheightchange&&o.onKeyboardheightchange(...e))),B:e.count},e.count?{C:a.t(r.innerValue.length),D:a.t(e.maxlength),E:e.disabled?"transparent":"#fff"}:{},{F:a.n(o.textareaClass),G:a.s(o.textareaStyle)})}],["__scopeId","data-v-d8442fe6"]]);wx.createComponent(o);
