"use strict";const t=require("../../../../common/vendor.js"),e=require("../vue.js"),i=require("../function/index.js"),n=require("../function/test.js"),r=require("../util/route.js"),s=e.defineMixin({props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},created(){this.$u.getRect=this.$uGetRect},computed:{$u:()=>i.deepMerge(t.index.$u,{props:void 0,http:void 0,mixin:void 0}),bem:()=>function(t,e,i){const n=`u-${t}--`,r={};return e&&e.map((t=>{r[n+this[t]]=!0})),i&&i.map((t=>{this[t]?r[n+t]=this[t]:delete r[n+t]})),Object.keys(r)}},methods:{openPage(t="url"){const e=this[t];e&&r.route({type:this.linkType,url:e})},navTo(t="",e="navigateTo"){r.route({type:this.linkType,url:t})},$uGetRect(e,i){return new Promise((n=>{t.index.createSelectorQuery().in(this)[i?"selectAll":"select"](e).boundingClientRect((t=>{i&&Array.isArray(t)&&t.length&&n(t),!i&&t&&n(t)})).exec()}))},getParentData(t=""){this.parent||(this.parent={}),this.parent=i.$parent.call(this,t),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((t=>{this.parentData[t]=this.parent[t]}))},preventEvent(t){t&&"function"==typeof t.stopPropagation&&t.stopPropagation()},noop(t){this.preventEvent(t)}},onReachBottom(){t.index.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&n.test.array(this.parent.children)){const t=this.parent.children;t.map(((e,i)=>{e===this&&t.splice(i,1)}))}}});exports.mixin=s;
