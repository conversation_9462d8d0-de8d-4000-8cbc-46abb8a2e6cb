<template>
  <view v-if="loading" class="loading-container">
    <uni-load-more status="loading"/>
  </view>

  <view v-else-if="country" class="page-container">
    <view class="fixed-header" :style="{ height: headerHeight + 'px' }">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="custom-nav-bar" :style="{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }">
        <view class="nav-back-button" @click="goBack">
          <uni-icons type="left" color="#000000" size="22"></uni-icons>
        </view>
        <view class="nav-title">{{ country.nameCn || '国别详情' }}</view>
      </view>
    </view>

    <scroll-view scroll-y class="scrollable-content" :style="{ paddingTop: headerHeight + 'px' }">
      <view class="cover-section">
        <image class="cover-image" :src="country.detailsCoverUrl" mode="aspectFill"/>
        <view class="cover-overlay">
          <view class="country-name-cn">{{ country.nameCn }}</view>
          <view class="country-name-en">{{ country.nameEn }}</view>
        </view>
      </view>

      <view class="tabs-wrapper">
        <scroll-view class="tabs" scroll-x="true" :show-scrollbar="false">
          <view
              v-for="(tab, index) in tabs"
              :key="tab.id"
              class="tab-item"
              :class="{ active: activeTab === index }"
              @click="onTabClick(index)"
              :style="activeTab === index ? activeTabStyle : {}"
          >
            <image class="tab-icon" :src="activeTab === index ? tab.activeIcon : tab.icon"></image>
            <text class="tab-text">{{ tab.name }}</text>
          </view>
        </scroll-view>
      </view>

      <view class="content-wrapper">
        <view v-if="!isPolicyTabActive">
          <view v-show="activeTab === 0" class="content-panel">
            <view class="section-card">
              <view class="section-title">国家简介</view>
              <view class="plain-text-content introduction-text">
                <text selectable user-select>{{ country.introduction }}</text>
              </view>
            </view>

            <view class="section-card" v-if="basicInfoList.length > 0">
              <view class="section-title">基本信息</view>
              <view class="info-grid-container basic-info-card" :style="basicInfoCardStyle">
                <view class="info-pair-row" v-for="(pair, pairIndex) in pairedBasicInfoList" :key="pairIndex">
                  <view class="info-row key-row">
                    <view class="info-column">{{ pair[0]?.key }}</view>
                    <view class="info-column">{{ pair[1]?.key }}</view>
                  </view>
                  <view class="info-row value-row">
                    <view class="info-column">{{ pair[0]?.value }}</view>
                    <view class="info-column">{{ pair[1]?.value }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view v-show="activeTab === 4" class="content-panel">
            <view
                class="park-card"
                v-for="(park, index) in country.industrialParks"
                :key="park.id"
                @click="goToParkDetail(park.id)"
            >
              <view class="park-number-badge">{{ index + 1 }}</view>

              <view class="park-card-image-wrapper">
                <u-image
                    :src="park.coverImageUrl"
                    width="100%"
                    height="240rpx"
                    :fade="true"
                    :lazy-load="true"
                ></u-image>
              </view>
              <view class="park-card-content">
                <view class="park-name">{{ park.name }}</view>
                <view class="park-info-item">
                  <image class="park-info-icon" :src="assets.icon_park_location || '/static/icons/位置icon金@2x.png'" mode="aspectFit"></image>
                  <text>{{ park.location }}</text>
                </view>
                <view class="park-info-item">
                  <image class="park-info-icon" :src="assets.icon_park_industries || '/static/icons/企业浅金@2x.png'" mode="aspectFit"></image>
                  <text>{{ park.industries }}</text>
                </view>
                <view class="park-info-item">
                  <image class="park-info-icon" :src="assets.icon_park_features || '/static/icons/亮点浅金@2x.png'" mode="aspectFit"></image>
                  <text>{{ park.features }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view v-else class="policy-page-container">
          <view class="policy-title-header">
            <h1 class="policy-title">{{ currentPolicyName }}</h1>
          </view>
          <view class="policy-main-content">
            <view class="policy-intro-wrapper">
              <view class="plain-text-content policy-text">
                <text selectable user-select>{{ currentPolicyContent }}</text>
              </view>
            </view>
            <ContentModule
                :key="currentPolicyType"
                :country-id="country.id"
                :policy-type="currentPolicyType"
            />
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>


<script setup>
import {onLoad, onBackPress} from '@dcloudio/uni-app'
import {computed, ref} from 'vue';
import {getCountryDetail} from '@/api/content/country.js';
import ContentModule from '@/components/home/<USER>';

// --- 自定义导航栏相关逻辑 ---
const navBarPaddingBottomRpx = 20;
const navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);
const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const headerHeight = ref(0);

const getNavBarInfo = () => {
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = menuButtonInfo.top;
    navBarHeight.value = menuButtonInfo.height;
    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
  } catch(e) {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    navBarHeight.value = 44;
    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
  }
};

const goBack = () => {
  uni.switchTab({
    url: '/pages/country/index',
    fail: () => uni.reLaunch({ url: '/pages/country/index' })
  });
};

// --- 页面核心数据和状态 ---
const country = ref(null);
const loading = ref(true);
const activeTab = ref(0);
const assets = ref({});

const basicInfoCardStyle = computed(() => ({
  backgroundImage: assets.value.bg_basic_info_card ? `url('${assets.value.bg_basic_info_card}')` : 'none'
}));

const activeTabStyle = computed(() => ({
  backgroundImage: assets.value.bg_tab_active ? `url('${assets.value.bg_tab_active}')` : 'none'
}));

// 【已修改】这里现在是标签页的静态配置
const tabsConfig = ref([
  { id: 'basic', name: '基本信息', iconKey: 'icon_tab_basic_normal', activeIconKey: 'icon_tab_basic_active' },
  { id: 'investment', name: '招商政策', iconKey: 'icon_tab_investment_normal', activeIconKey: 'icon_tab_investment_active' },
  { id: 'customs', name: '海关政策', iconKey: 'icon_tab_customs_normal', activeIconKey: 'icon_tab_customs_active' },
  { id: 'tax', name: '税务政策', iconKey: 'icon_tab_tax_normal', activeIconKey: 'icon_tab_tax_active' },
  { id: 'parks', name: '工业园区', iconKey: 'icon_tab_parks_normal', activeIconKey: 'icon_tab_parks_active' },
]);

// 【新增】一个计算属性，用于动态构建带有正确图标URL的标签页列表
const tabs = computed(() => {
  return tabsConfig.value.map(tab => ({
    id: tab.id,
    name: tab.name,
    icon: assets.value[tab.iconKey] || tab.fallbackIcon,
    activeIcon: assets.value[tab.activeIconKey] || tab.fallbackActiveIcon
  }));
});

const basicInfoList = computed(() => {
  if (country.value && Array.isArray(country.value.basicInfoJson)) {
    return country.value.basicInfoJson;
  }
  return [];
});

const pairedBasicInfoList = computed(() => {
  const result = [];
  const list = basicInfoList.value;
  for (let i = 0; i < list.length; i += 2) {
    const pair = [list[i]];
    if (list[i + 1]) pair.push(list[i + 1]);
    result.push(pair);
  }
  return result;
});

const onTabClick = (index) => {
  activeTab.value = index;
};

const isPolicyTabActive = computed(() => {
  const policyIds = ['investment', 'customs', 'tax'];
  return policyIds.includes(tabs.value[activeTab.value]?.id);
});

const currentPolicyType = computed(() => tabs.value[activeTab.value]?.id);
const currentPolicyName = computed(() => tabs.value[activeTab.value]?.name);

const currentPolicyContent = computed(() => {
  if (!country.value) return '';
  switch (currentPolicyType.value) {
    case 'investment': return country.value.investmentPolicy;
    case 'customs': return country.value.customsPolicy;
    case 'tax': return country.value.taxPolicy;
    default: return '';
  }
});

const goToParkDetail = (parkId) => {
  uni.navigateTo({
    url: `/pages_sub/pages_other/park_detail?id=${parkId}`
  });
};

onLoad(async (options) => {
  getNavBarInfo();
  assets.value = uni.getStorageSync('staticAssets') || {};

  if (!options.id) {
    uni.showToast({title: '参数错误', icon: 'none'});
    uni.navigateBack();
    return;
  }

  if (options.tab) {
    const initialTabIndex = tabs.value.findIndex(t => t.id === options.tab);
    if (initialTabIndex !== -1) activeTab.value = initialTabIndex;
  }

  try {
    const res = await getCountryDetail(options.id);
    // 【已修改】不再需要为图片URL拼接baseUrl
    // 后端的AOP切面已经处理好了完整的URL
    country.value = res.data;
  } catch (error) {
    console.error('获取详情失败:', error);
    uni.showToast({title: error.message || '加载失败', icon: 'none'});
  } finally {
    loading.value = false;
  }
});

onBackPress(() => {
  uni.reLaunch({ url: '/pages/country/index' });
  return true;
});
</script>

<style lang="scss" scoped>
/* --- 页面布局及自定义导航栏样式 --- */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFFFFF;
}
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}
.scrollable-content {
  flex: 1;
  height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.status-bar {
  width: 100%;
}
.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box;
}
.nav-back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
}
.loading-container {
  padding-top: 200rpx;
}
.cover-section {
  position: relative;
  height: 400rpx;
}
.cover-section .cover-image {
  width: 100%;
  height: 100%;
}
.cover-section .cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  color: #fff;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
}
.cover-section .cover-overlay .country-name-cn {
  font-size: 48rpx;
  font-weight: bold;
}
.cover-section .cover-overlay .country-name-en {
  font-size: 32rpx;
  opacity: 0.9;
}
.tabs-wrapper {
  margin: 30rpx 30rpx 0;
  background-color: #F4F4F4;
  border-radius: 16rpx;
  padding: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tabs {
  display: flex;
  white-space: nowrap;
}

.tabs::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
.tab-item {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
}
.tab-item:last-child {
  margin-right: 0;
}
.tab-item .tab-icon {
  width: 40rpx;
  height: 40rpx;
}
.tab-item .tab-text {
  font-size: 28rpx;
  color: #9B9A9A;
}
.tab-item.active {
  background-size: cover;
  background-position: center;
}
.tab-item.active .tab-text {
  color: #23232A;
  font-weight: bold;
}
.content-wrapper {
  padding: 24rpx;
}
.section-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding-bottom: 15rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.plain-text-content {
  font-size: 28rpx;
  line-height: 1.7;
  white-space: pre-wrap;
}
.introduction-text {
  color: #66666E;
}
.policy-text {
  color: #23232A;
}
.basic-info-card {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 20rpx;
}
.info-grid-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.info-row {
  display: flex;
  width: 100%;
}
.info-column {
  flex: 1;
  width: 50%;
  padding-right: 20rpx;
  box-sizing: border-box;
}
.key-row .info-column {
  font-size: 24rpx;
  color: #66666E;
}
.value-row {
  margin-top: 8rpx;
}
.value-row .info-column {
  font-size: 28rpx;
  color: #23232A;
  font-weight: 500;
}
.policy-title-header {
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
  background: linear-gradient(to bottom, #CEDEF5, #F0F2F3) center;
  border-bottom: 2rpx solid #DCDFE6;
}
.policy-title-header .policy-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #023F98;
}
.policy-main-content {
  background-color: #F2F4FA;
  border-radius: 0 0 16rpx 16rpx;
  padding: 30rpx;
}
.policy-intro-wrapper {
  margin-bottom: 30rpx;
}
.park-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  overflow: hidden;
  /* [修改] 增加相对定位，作为序号标记的定位父级 */
  position: relative;
}
.park-card:active {
  transform: scale(0.98);
}
.park-card .park-card-image-wrapper {
  width: 100%;
  height: 240rpx;
  background-color: #f0f2f5;
}
.park-card .park-card-content {
  padding: 30rpx;
}
.park-card .park-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}
.park-card .park-info-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.park-card .park-info-item:last-child {
  margin-bottom: 0;
}
.park-card .park-info-icon {
  width: 26rpx;
  height: 26rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* [新增] 园区卡片左上角序号标记的样式 */
.park-number-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 2; /* 确保在图片上方 */
  width: 48rpx;
  height: 48rpx;
  background-color: #FFBF51;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #23232A;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2); /* 增加一点阴影使其更突出 */
}
</style>