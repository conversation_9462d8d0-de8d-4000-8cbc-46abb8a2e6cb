/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-fd1fc04a {
  height: 100%;
  background-color: #f4f4f4;
}

/* 所有样式保持不变 */
.data-v-fd1fc04a:root {
  --radius-small: 8rpx;
  --radius-medium: 16rpx;
  --radius-large: 24rpx;
  --radius-xl: 32rpx;
  --separator-color: #E5E7EB;
  --content-padding: 32rpx;
}
.page-container.data-v-fd1fc04a {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #FFFFFF;
}
.header-section.data-v-fd1fc04a {
  position: relative;
  flex-shrink: 0;
  z-index: 10;
  /* 【修改】移除 background 属性 */
  background-color: #f0f2f5;
  /* 添加一个基础背景色，防止图片加载慢时白屏 */
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-bottom: 24rpx;
}
.custom-nav-bar.data-v-fd1fc04a {
  padding: 0 var(--content-padding) 32rpx;
}
.custom-nav-bar .status-bar.data-v-fd1fc04a {
  height: var(--status-bar-height);
}
.custom-nav-bar .nav-title.data-v-fd1fc04a {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 184rpx;
  box-sizing: border-box;
  padding-top: var(--status-bar-height);
  font-size: 34rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.filter-bar.data-v-fd1fc04a {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.sort-button.data-v-fd1fc04a,
.filter-button.data-v-fd1fc04a {
  font-family: "Alibaba PuHuiTi 3.0-55 Regular", sans-serif;
  font-size: 28rpx;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  gap: 8rpx;
  position: relative;
  transition: opacity 0.2s ease;
}
.sort-button.is-active.data-v-fd1fc04a,
.filter-button.is-active.data-v-fd1fc04a {
  opacity: 0.8;
}
.filter-button .active-dot.data-v-fd1fc04a {
  position: absolute;
  top: -4rpx;
  right: -16rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #ef4444;
  border-radius: 50%;
  border: 2rpx solid #0F4CA7;
}
.search-box.data-v-fd1fc04a {
  flex: 1;
  height: 64rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-left: auto;
}
.search-box.data-v-fd1fc04a .uni-easyinput__content {
  background: transparent !important;
  border: none !important;
}
.search-box.data-v-fd1fc04a .uni-easyinput__placeholder {
  color: #9B9A9A !important;
}
.dropdown-wrapper.data-v-fd1fc04a {
  position: fixed;
  z-index: 999;
  top: 326rpx;
  left: 0;
  right: 0;
}
.dropdown-mask.data-v-fd1fc04a {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease;
}
.dropdown-panel.data-v-fd1fc04a {
  position: absolute;
  top: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: var(--radius-large);
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-20px);
  pointer-events: none;
  transition: transform 0.25s ease, opacity 0.25s ease;
}
.dropdown-panel.show.data-v-fd1fc04a {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}
.sort-panel.data-v-fd1fc04a {
  padding: 16rpx 0;
}
.sort-panel .sort-option.data-v-fd1fc04a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 32rpx;
  font-size: 28rpx;
  color: #333333;
  transition: all 0.2s ease;
}
.sort-panel .sort-option.active.data-v-fd1fc04a {
  color: #023F98;
  font-weight: 500;
  background-color: rgba(42, 97, 241, 0.05);
}
.filter-panel.data-v-fd1fc04a {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}
.filter-panel .filter-scroll.data-v-fd1fc04a {
  flex: 1;
  padding: 32rpx;
}
.filter-panel .panel-section.data-v-fd1fc04a {
  margin-bottom: 48rpx;
}
.filter-panel .panel-section.data-v-fd1fc04a:last-child {
  margin-bottom: 24rpx;
}
.filter-panel .panel-section .section-title.data-v-fd1fc04a {
  font-weight: 500;
  font-size: 30rpx;
  color: #23232A;
  margin-bottom: 28rpx;
  display: block;
}
.filter-panel .panel-section .panel-options.data-v-fd1fc04a {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.filter-panel .panel-section .option-btn.data-v-fd1fc04a {
  width: 200rpx;
  height: 68rpx;
  background: #F2F4FA;
  border-radius: var(--radius-small);
  font-size: 28rpx;
  color: #66666E;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  transition: all 0.2s ease;
}
.filter-panel .panel-section .option-btn.active.data-v-fd1fc04a {
  background: rgba(42, 97, 241, 0.1);
  color: #023F98;
  font-weight: 500;
}
.filter-panel .panel-footer.data-v-fd1fc04a {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #F0F2F5;
}
.filter-panel .panel-footer .footer-btn.data-v-fd1fc04a {
  flex: 1;
  height: 80rpx;
  margin: 0;
  font-size: 30rpx;
  border-radius: var(--radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.filter-panel .panel-footer .footer-btn.reset.data-v-fd1fc04a {
  background-color: #F2F4FA;
  color: #66666E;
}
.filter-panel .panel-footer .footer-btn.confirm.data-v-fd1fc04a {
  background: #0F4CA7;
  color: #FFFFFF;
}
.content-section.data-v-fd1fc04a {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #FFFFFF;
  margin-top: -24rpx;
  border-top-left-radius: var(--radius-large);
  border-top-right-radius: var(--radius-large);
  position: relative;
  z-index: 20;
}
.tabs-container.data-v-fd1fc04a {
  flex-shrink: 0;
  width: 100%;
  background: #ffffff;
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
  position: relative;
}
.tabs-container.data-v-fd1fc04a:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: var(--content-padding);
  right: var(--content-padding);
  height: 1rpx;
  background-color: var(--separator-color);
}
.tabs-container.data-v-fd1fc04a .u-tabs {
  padding: 12rpx 0;
}
.article-list-scroll.data-v-fd1fc04a {
  flex: 1;
  height: 0;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: calc(160rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
}
.article-list.data-v-fd1fc04a {
  background: #FFFFFF;
}
.article-card.data-v-fd1fc04a {
  display: flex;
  gap: 30rpx;
  padding: 32rpx var(--content-padding);
  background: #ffffff;
  position: relative;
  /* --- [START] MODIFIED STYLES --- */
  /* --- [END] MODIFIED STYLES --- */
}
.article-card.data-v-fd1fc04a:not(:last-child) {
  border-bottom: 1rpx solid var(--separator-color);
}
.article-card .card-content.data-v-fd1fc04a {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.article-card .card-title.data-v-fd1fc04a {
  width: 346rpx;
  height: 80rpx;
  font-family: "Alibaba PuHuiTi 3.0", sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justified;
  line-height: 1.5;
  /* Keeping line-height for proper spacing */
  font-style: normal;
  text-transform: none;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: auto;
}
.article-card .card-meta.data-v-fd1fc04a {
  display: flex;
  align-items: center;
  gap: 20rpx;
  /* Changed from 32rpx to 20rpx */
  margin-top: 24rpx;
  font-size: 24rpx;
  color: #9B9A9A;
}
.article-card .card-tags.data-v-fd1fc04a {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}
.article-card .tag-item.data-v-fd1fc04a {
  width: 112rpx;
  height: 40rpx;
  background: #FFFFFF;
  border-radius: 4rpx;
  border: 1rpx solid #023F98;
  color: #023F98;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.article-card .card-cover.data-v-fd1fc04a {
  flex-shrink: 0;
  width: 336rpx;
  height: 190rpx;
  border-radius: var(--radius-medium);
  overflow: hidden;
}
.image-loading.data-v-fd1fc04a,
.image-error.data-v-fd1fc04a {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: var(--radius-medium);
}
.loading-text.data-v-fd1fc04a,
.error-text.data-v-fd1fc04a {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 8rpx;
}
.data-v-fd1fc04a .u-loadmore__content__text {
  font-size: 24rpx !important;
  color: #9B9A9A !important;
  line-height: 34rpx !important;
}