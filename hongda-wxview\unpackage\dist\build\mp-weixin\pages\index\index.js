"use strict";const e=require("../../common/vendor.js"),o=require("../../api/platform/ad.js");Math||(a+n+t+l+r+s+u+v+c)();const a=()=>"../../components/home/<USER>",n=()=>"../../components/home/<USER>",t=()=>"../../components/home/<USER>",l=()=>"../../components/home/<USER>",s=()=>"../../components/home/<USER>",u=()=>"../../components/home/<USER>",r=()=>"../../components/home/<USER>",c=()=>"../../components/common/PopupAdComponent.js",v=()=>"../../components/layout/CustomTabBar.js",i={__name:"index",setup(a){const n=e.ref(88),t=e=>{e&&e>0&&(n.value=e)},l=e.ref(null),s=e.ref(!1),u=e.ref(""),r=e.ref(!1),c=e.ref([]),v=e.ref(0),i=e.ref(null);let m=!1;const p=()=>{l.value&&l.value.loadMore&&l.value.loadMore()},d=e=>{s.value=!!e},h=()=>{v.value<c.value.length?(i.value=c.value[v.value],r.value=!0):(r.value=!1,i.value=null)},g=()=>{r.value=!1,v.value++,v.value>=c.value.length?m=!0:setTimeout((()=>{h()}),300)},f=()=>{e.index.navigateTo({url:"/pages_sub/pages_profile/contact"})};return e.onLoad((()=>{console.log("首页 onLoad - 页面首次加载")})),e.onShow((()=>{console.log("首页 onShow - 页面显示"),e.index.hideTabBar(),(async()=>{try{if(m)return;const e=await o.getAdListByPositionApi("SPLASH_SCREEN",{pageSize:10});e&&e.data&&e.data.length>0?(c.value=e.data,v.value=0,h()):m=!0}catch(e){console.error("获取弹窗广告失败:",e.message||e),m=!0}})();const a=e.index.getStorageSync("staticAssets");u.value=(null==a?void 0:a.fab_customer_service_icon)||""})),(o,a)=>e.e({a:e.o(t),b:e.sr(l,"b5805e6f-6",{k:"activityRef"}),c:e.o(d),d:s.value},(s.value,{}),{e:n.value+"px",f:e.o(p),g:e.p({current:0}),h:u.value,i:e.o(f),j:r.value&&i.value},r.value&&i.value?{k:e.o(g),l:e.p({show:r.value,"ad-data":i.value})}:{})}},m=e._export_sfc(i,[["__scopeId","data-v-b5805e6f"]]);wx.createPage(m);
