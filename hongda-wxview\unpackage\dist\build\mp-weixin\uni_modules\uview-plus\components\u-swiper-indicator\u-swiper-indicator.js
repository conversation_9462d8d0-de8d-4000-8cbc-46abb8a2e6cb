"use strict";const i=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),e=require("../../libs/mixin/mixin.js"),n=require("../../libs/function/index.js"),r=require("../../../../common/vendor.js"),o={name:"u-swiper-indicator",mixins:[t.mpMixin,e.mixin,i.props],data:()=>({lineWidth:22}),computed:{lineStyle(){let i={};return i.width=n.addUnit(this.lineWidth),i.transform=`translateX(${n.addUnit(this.current*this.lineWidth)})`,i.backgroundColor=this.indicatorActiveColor,i},dotStyle(){return i=>{let t={};return t.backgroundColor=i===this.current?this.indicatorActiveColor:this.indicatorInactiveColor,t}}},methods:{addUnit:n.addUnit}};const d=r._export_sfc(o,[["render",function(i,t,e,n,o,d){return r.e({a:"line"===i.indicatorMode},"line"===i.indicatorMode?{b:r.s(d.lineStyle),c:r.n(`u-swiper-indicator__wrapper--${i.indicatorMode}`),d:d.addUnit(o.lineWidth*i.length),e:i.indicatorInactiveColor}:{},{f:"dot"===i.indicatorMode},"dot"===i.indicatorMode?{g:r.f(i.length,((t,e,n)=>({a:e,b:r.n(e===i.current&&"u-swiper-indicator__wrapper__dot--active"),c:r.s(d.dotStyle(e))})))}:{})}],["__scopeId","data-v-d5703429"]]);wx.createComponent(d);
