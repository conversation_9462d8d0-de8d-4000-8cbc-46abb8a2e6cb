"use strict";const t=require("../../libs/vue.js"),a=require("../../libs/config/props.js"),r=require("../../libs/function/test.js"),e=t.defineMixin({props:{src:{type:String,default:()=>a.props.avatar.src},shape:{type:String,default:()=>a.props.avatar.shape},size:{type:[String,Number],default:()=>a.props.avatar.size},mode:{type:String,default:()=>a.props.avatar.mode},text:{type:String,default:()=>a.props.avatar.text},bgColor:{type:String,default:()=>a.props.avatar.bgColor},color:{type:String,default:()=>a.props.avatar.color},fontSize:{type:[String,Number],default:()=>a.props.avatar.fontSize},icon:{type:String,default:()=>a.props.avatar.icon},mpAvatar:{type:Boolean,default:()=>a.props.avatar.mpAvatar},randomBgColor:{type:Boolean,default:()=>a.props.avatar.randomBgColor},defaultUrl:{type:String,default:()=>a.props.avatar.defaultUrl},colorIndex:{type:[String,Number],validator:t=>r.test.range(t,[0,19])||""===t,default:()=>a.props.avatar.colorIndex},name:{type:String,default:()=>a.props.avatar.name}}});exports.props=e;
