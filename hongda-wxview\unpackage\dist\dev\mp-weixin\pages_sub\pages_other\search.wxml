<view class="page-container data-v-a5d8397f"><view class="custom-header data-v-a5d8397f"><view class="status-bar data-v-a5d8397f"></view><view class="nav-bar data-v-a5d8397f" style="{{c}}"><u-icon wx:if="{{b}}" class="data-v-a5d8397f" bindclick="{{a}}" u-i="a5d8397f-0" bind:__l="__l" u-p="{{b}}"></u-icon><view class="page-title-container data-v-a5d8397f"><text class="page-title data-v-a5d8397f">红大出海</text></view></view><view class="search-section data-v-a5d8397f"><view class="search-input-wrapper data-v-a5d8397f"><u-icon wx:if="{{d}}" class="data-v-a5d8397f" u-i="a5d8397f-1" bind:__l="__l" u-p="{{d}}"></u-icon><input class="search-input data-v-a5d8397f" placeholder="搜索活动、资讯" confirm-type="search" bindconfirm="{{e}}" value="{{f}}" bindinput="{{g}}"/><u-icon wx:if="{{h}}" class="data-v-a5d8397f" bindclick="{{i}}" u-i="a5d8397f-2" bind:__l="__l" u-p="{{j}}"></u-icon></view><text wx:if="{{k}}" class="cancel-btn data-v-a5d8397f" bindtap="{{l}}">取消</text></view></view><view class="tabs-container data-v-a5d8397f"><view class="custom-tabs data-v-a5d8397f"><view wx:for="{{m}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', 'data-v-a5d8397f', tab.c && 'active']}}" bindtap="{{tab.d}}" style="{{tab.e}}">{{tab.a}}</view></view></view><scroll-view scroll-y class="result-list-scroll data-v-a5d8397f" bindscrolltolower="{{y}}"><view wx:if="{{n}}" class="loading-container data-v-a5d8397f"><u-loading-icon wx:if="{{o}}" class="data-v-a5d8397f" u-i="a5d8397f-3" bind:__l="__l" u-p="{{o}}"></u-loading-icon></view><view wx:else class="data-v-a5d8397f"><view wx:if="{{p}}" class="empty-state-container data-v-a5d8397f"><u-empty wx:if="{{q}}" class="data-v-a5d8397f" u-i="a5d8397f-4" bind:__l="__l" u-p="{{r}}"></u-empty><u-empty wx:else class="data-v-a5d8397f" u-i="a5d8397f-5" bind:__l="__l" u-p="{{s||''}}"></u-empty></view><view wx:else class="result-list data-v-a5d8397f"><view wx:for="{{t}}" wx:for-item="item" wx:key="j" class="event-card data-v-a5d8397f" bindtap="{{item.k}}"><view class="card-left data-v-a5d8397f"><image class="event-image data-v-a5d8397f" src="{{item.a}}" mode="aspectFill" lazy-load="{{true}}"></image><view wx:if="{{item.b}}" class="{{['data-v-a5d8397f', 'status-tag', item.d]}}">{{item.c}}</view></view><view class="card-right data-v-a5d8397f"><text class="event-title data-v-a5d8397f">{{item.e}}</text><view class="event-info-row data-v-a5d8397f"><view class="time-location-item data-v-a5d8397f"><image class="event-info-icon data-v-a5d8397f" src="{{v}}" mode="aspectFit"></image><text class="info-text data-v-a5d8397f">{{item.f}}</text></view><view class="time-location-item data-v-a5d8397f"><image class="event-info-icon data-v-a5d8397f" src="{{w}}" mode="aspectFit"></image><text class="info-text data-v-a5d8397f">{{item.g}}</text></view></view><view class="event-info remaining-spots data-v-a5d8397f"><text class="spots-count data-v-a5d8397f">{{item.h}}: {{item.i}}</text></view></view></view><u-loadmore wx:if="{{x}}" class="data-v-a5d8397f" u-i="a5d8397f-6" bind:__l="__l" u-p="{{x}}"/></view></view></scroll-view></view>