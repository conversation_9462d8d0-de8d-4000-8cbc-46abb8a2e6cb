"use strict";const o=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),e=o.defineMixin({props:{show:{type:Boolean,default:()=>t.props.toolbar.show},cancelText:{type:String,default:()=>t.props.toolbar.cancelText},confirmText:{type:String,default:()=>t.props.toolbar.confirmText},cancelColor:{type:String,default:()=>t.props.toolbar.cancelColor},confirmColor:{type:String,default:()=>t.props.toolbar.confirmColor},title:{type:String,default:()=>t.props.toolbar.title},rightSlot:{type:Boolean,default:!1}}});exports.props=e;
