"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),o=require("../../../../common/vendor.js"),n={name:"u-empty",mixins:[t.mpMixin,i.mixin,e.props],data:()=>({icons:{car:"购物车为空",page:"页面不存在",search:"没有搜索结果",address:"没有收货地址",wifi:"没有WiFi",order:"订单为空",coupon:"没有优惠券",favor:"暂无收藏",permission:"无权限",history:"无历史记录",news:"无新闻列表",message:"消息列表为空",list:"列表为空",data:"数据为空",comment:"暂无评论"}}),computed:{emptyStyle(){const e={};return e.marginTop=s.addUnit(this.marginTop),s.deepMerge(s.addStyle(this.customStyle),e)},textStyle(){const e={};return e.color=this.textColor,e.fontSize=s.addUnit(this.textSize),e},isSrc(){return this.icon.indexOf("/")>=0}},methods:{addUnit:s.addUnit}};if(!Array){o.resolveComponent("u-icon")()}Math;const r=o._export_sfc(n,[["render",function(e,t,i,s,n,r){return o.e({a:e.show},e.show?o.e({b:!r.isSrc},r.isSrc?{d:r.addUnit(e.width),e:r.addUnit(e.height),f:e.icon}:{c:o.p({name:"message"===e.mode?"chat":`empty-${e.mode}`,size:e.iconSize,color:e.iconColor,"margin-top":"14"})},{g:o.t(e.text?e.text:n.icons[e.mode]),h:o.s(r.textStyle),i:e.$slots.default||e.$slots.$default},(e.$slots.default||e.$slots.$default,{}),{j:o.s(r.emptyStyle)}):{})}],["__scopeId","data-v-99f284dd"]]);wx.createComponent(r);
