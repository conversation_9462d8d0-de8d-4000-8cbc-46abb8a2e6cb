"use strict";
const common_vendor = require("../../common/vendor.js");
const GAP_BETWEEN_SEARCH_AND_CAPSULE = 10;
const SEARCH_BAR_HEIGHT_ADJUSTMENT = 0;
const _sfc_main = {
  __name: "HeaderComponent",
  emits: ["height-calculated"],
  setup(__props, { emit: __emit }) {
    const HEADER_HORIZONTAL_PADDING = common_vendor.index.upx2px(24);
    const HEADER_EXTRA_VERTICAL_PADDING = common_vendor.index.upx2px(0);
    const assets = common_vendor.ref(common_vendor.index.getStorageSync("staticAssets") || {});
    const searchIconUrl = common_vendor.computed(() => {
      return assets.value.icon_home_search || "";
    });
    const headerStyle = common_vendor.ref({});
    const searchBarStyle = common_vendor.ref({});
    const emit = __emit;
    common_vendor.onMounted(() => {
      const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const headerContentHeight = (menuButtonInfo.top - systemInfo.statusBarHeight) * 2 + menuButtonInfo.height + HEADER_EXTRA_VERTICAL_PADDING * 2;
      const searchBarWidth = menuButtonInfo.left - HEADER_HORIZONTAL_PADDING - GAP_BETWEEN_SEARCH_AND_CAPSULE;
      const searchBarHeight = menuButtonInfo.height + SEARCH_BAR_HEIGHT_ADJUSTMENT;
      const totalHeaderHeight = headerContentHeight + systemInfo.statusBarHeight;
      headerStyle.value = {
        height: `${totalHeaderHeight}px`,
        paddingTop: `${systemInfo.statusBarHeight}px`
      };
      searchBarStyle.value = {
        width: `${searchBarWidth}px`,
        height: `${searchBarHeight}px`
      };
      emit("height-calculated", totalHeaderHeight);
    });
    const handleSearchClick = () => {
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/search"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: searchIconUrl.value
      }, searchIconUrl.value ? {
        b: searchIconUrl.value
      } : {}, {
        c: common_vendor.s(searchBarStyle.value),
        d: common_vendor.o(handleSearchClick),
        e: common_vendor.s(headerStyle.value)
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7a6d2e7b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
