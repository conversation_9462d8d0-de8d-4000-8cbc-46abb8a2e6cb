/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-ba5a4f4a {
  height: 100%;
  background-color: #f4f4f4;
}
.calendar-timeline-wrapper.data-v-ba5a4f4a {
  width: 100%;
}
.vertical-timeline.data-v-ba5a4f4a {
  width: calc(100% - 48rpx);
  /* 使用自适应宽度，避免固定宽度导致的溢出 */
  max-width: 702rpx;
  /* 设置最大宽度 */
  background: #F0F2F3;
  border-radius: 32rpx;
  margin: 0 24rpx;
  margin-top: 37rpx;
  padding: 16rpx 0 20rpx 0;
  box-sizing: border-box;
  position: relative;
  min-height: 200rpx;
  overflow: visible;
}

/* 左上角凸起的角 */
.corner-notch.data-v-ba5a4f4a {
  position: absolute;
  top: -16rpx;
  left: 60rpx;
  width: 0;
  height: 0;
  border-left: 14rpx solid transparent;
  border-right: 14rpx solid transparent;
  border-bottom: 16rpx solid #F0F2F3;
  z-index: 103;
}
.date-section.data-v-ba5a4f4a {
  position: relative;
  padding-left: 72rpx;
}
.date-section.data-v-ba5a4f4a:first-child {
  margin-top: 0;
}
.date-section.data-v-ba5a4f4a:not(:last-child) {
  margin-bottom: 24rpx;
}
.date-header.data-v-ba5a4f4a {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}
.date-header .timeline-dot.data-v-ba5a4f4a {
  position: absolute;
  left: 24rpx;
  top: 13rpx;
  width: 18rpx;
  height: 18rpx;
  background: #FFFFFF;
  border: 2rpx solid #023F98;
  border-radius: 50%;
  z-index: 2;
}
.date-header .time-text.data-v-ba5a4f4a {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-size: 32rpx;
  color: #023F98;
  line-height: 44rpx;
  font-weight: normal;
}
.date-header .weekday-text.data-v-ba5a4f4a {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-size: 22rpx;
  color: #66666E;
  line-height: 44rpx;
  font-weight: normal;
  margin-left: 15rpx;
}
.line-connector.data-v-ba5a4f4a {
  position: absolute;
  left: 32rpx;
  top: 44rpx;
  bottom: -197rpx;
  width: 2rpx;
}
.line-connector .timeline-line.data-v-ba5a4f4a {
  height: 100%;
  width: 100%;
  background: #023F98;
}
.date-section:last-child .line-connector .timeline-line.data-v-ba5a4f4a {
  display: none;
}
.events-container .compact-event-card.data-v-ba5a4f4a {
  width: 590rpx;
  height: 100rpx;
  background: #FFFFFF;
  border: 2rpx solid #023F98;
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  transition: transform 0.2s ease;
  box-sizing: border-box;
}
.events-container .compact-event-card.data-v-ba5a4f4a:last-child {
  margin-bottom: 0;
}
.events-container .compact-event-card.data-v-ba5a4f4a:active {
  transform: scale(0.98);
}
.events-container .compact-event-card .event-avatar.data-v-ba5a4f4a {
  width: 52rpx;
  height: 52rpx;
  border-radius: 50%;
  flex-shrink: 0;
  background-color: #f5f5f5;
}
.events-container .compact-event-card .event-content.data-v-ba5a4f4a {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.events-container .compact-event-card .event-content .event-title-compact.data-v-ba5a4f4a {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-size: 28rpx;
  color: #23232A;
  font-weight: normal;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-right: 24rpx;
}
.events-container .compact-event-card .event-content .location-group.data-v-ba5a4f4a {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.events-container .compact-event-card .event-content .separator-line.data-v-ba5a4f4a {
  width: 2rpx;
  height: 40rpx;
  background: #FA841C;
  opacity: 0.3;
  flex-shrink: 0;
  margin-right: 24rpx;
}
.events-container .compact-event-card .event-content .event-location-compact.data-v-ba5a4f4a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  gap: 4rpx;
}
.events-container .compact-event-card .event-content .event-location-compact .location-icon.data-v-ba5a4f4a {
  width: 32rpx;
  height: 32rpx;
  flex-shrink: 0;
}
.events-container .compact-event-card .event-content .event-location-compact .location-text.data-v-ba5a4f4a {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-size: 22rpx;
  color: #452D03;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}
.no-more-divider.data-v-ba5a4f4a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  margin-top: 60rpx;
}
.no-more-text.data-v-ba5a4f4a {
  width: 120rpx;
  height: 34rpx;
  line-height: 34rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 24rpx;
  color: #9B9A9A;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

/* 底部占位 */
.timeline-bottom-spacer.data-v-ba5a4f4a {
  height: 220rpx;
}