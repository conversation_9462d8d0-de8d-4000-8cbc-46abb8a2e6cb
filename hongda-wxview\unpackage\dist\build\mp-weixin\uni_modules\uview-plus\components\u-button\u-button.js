"use strict";const e=require("../../libs/mixin/button.js"),i=require("../../libs/mixin/openType.js"),o=require("../../libs/mixin/mpMixin.js"),t=require("../../libs/mixin/mixin.js"),r=require("./props.js"),n=require("../../libs/function/index.js"),s=require("../../libs/function/throttle.js"),a=require("../../libs/config/color.js"),l=require("../../../../common/vendor.js"),h={name:"u-button",mixins:[o.mpMixin,t.mixin,e.buttonMixin,i.openType,r.props],data:()=>({}),computed:{bemClass(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor(){return this.plain?this.color?this.color:a.color[`u-${this.type}`]:"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor(){let e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle(){let e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize(){let e=14,{size:i}=this;return"large"===i&&(e=16),"normal"===i&&(e=14),"small"===i&&(e=12),"mini"===i&&(e=10),e}},emits:["click","getphonenumber","getuserinfo","error","opensetting","launchapp","agreeprivacyauthorization"],methods:{addStyle:n.addStyle,clickHandler(e){this.disabled||this.loading||s.throttle((()=>{this.$emit("click",e)}),this.throttleTime),this.stop&&this.preventEvent(e)},getphonenumber(e){this.$emit("getphonenumber",e)},getuserinfo(e){this.$emit("getuserinfo",e)},error(e){this.$emit("error",e)},opensetting(e){this.$emit("opensetting",e)},launchapp(e){this.$emit("launchapp",e)},agreeprivacyauthorization(e){this.$emit("agreeprivacyauthorization",e)}}};if(!Array){(l.resolveComponent("u-loading-icon")+l.resolveComponent("u-icon"))()}Math||((()=>"../u-loading-icon/u-loading-icon.js")+(()=>"../u-icon/u-icon.js"))();const c=l._export_sfc(h,[["render",function(e,i,o,t,r,n){return l.e({a:e.loading},e.loading?{b:l.p({mode:e.loadingMode,size:1.15*e.loadingSize,color:n.loadingColor}),c:l.t(e.loadingText||e.text),d:l.s({fontSize:n.textSize+"px"})}:l.e({e:e.icon},e.icon?{f:l.p({name:e.icon,color:n.iconColorCom,size:1.35*n.textSize,customStyle:{marginRight:"2px"}})}:{},{g:l.t(e.text),h:l.s({fontSize:n.textSize+"px"})}),{i:Number(e.hoverStartTime),j:Number(e.hoverStayTime),k:e.formType,l:e.openType,m:e.appParameter,n:e.hoverStopPropagation,o:e.sendMessageTitle,p:e.sendMessagePath,q:e.lang,r:e.dataName,s:e.sessionFrom,t:e.sendMessageImg,v:e.showMessageCard,w:l.o(((...e)=>n.getphonenumber&&n.getphonenumber(...e))),x:l.o(((...e)=>n.getuserinfo&&n.getuserinfo(...e))),y:l.o(((...e)=>n.error&&n.error(...e))),z:l.o(((...e)=>n.opensetting&&n.opensetting(...e))),A:l.o(((...e)=>n.launchapp&&n.launchapp(...e))),B:l.o(((...e)=>n.agreeprivacyauthorization&&n.agreeprivacyauthorization(...e))),C:e.disabled||e.loading?"":"u-button--active",D:l.s(n.baseColor),E:l.s(n.addStyle(e.customStyle)),F:l.o(((...e)=>n.clickHandler&&n.clickHandler(...e))),G:l.n(n.bemClass)})}],["__scopeId","data-v-10269e3f"]]);wx.createComponent(c);
