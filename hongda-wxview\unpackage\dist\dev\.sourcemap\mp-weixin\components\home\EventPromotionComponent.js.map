{"version": 3, "file": "EventPromotionComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9FdmVudFByb21vdGlvbkNvbXBvbmVudC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"promo-container\">\t\t\r\n\t\t<!-- 骨架屏 - 加载时显示 -->\r\n\t\t<view v-if=\"isLoading\" class=\"skeleton-wrapper\">\r\n\t\t\t<u-skeleton\r\n\t\t\t\t:loading=\"true\"\r\n\t\t\t\t:animate=\"true\"\r\n\t\t\t\t:rows=\"4\"\r\n\t\t\t\t:title=\"true\"\r\n\t\t\t\ttitleWidth=\"60%\"\r\n\t\t\t\trowsWidth=\"['100%', '40%', '40%', '100%']\"\r\n\t\t\t\trowsHeight=\"['180px', '20px', '20px', '40px']\"\r\n\t\t\t></u-skeleton>\r\n\t\t</view>\r\n\r\n\t\t<view v-else-if=\"promoDataList.length > 0\" class=\"promo-card-wrapper\">\r\n\t\t\t<!-- 轮播区域 -->\r\n\t\t\t<swiper \r\n\t\t\t\tref=\"swiperRef\"\r\n\t\t\t\tclass=\"promo-swiper\"\r\n\t\t\t\t:indicator-dots=\"false\"\r\n\t\t\t\t:autoplay=\"true\"\r\n\t\t\t\t:interval=\"4000\"\r\n\t\t\t\t:duration=\"500\"\r\n\t\t\t\t:circular=\"true\"\r\n\t\t\t\t:current=\"currentIndex\"\r\n\t\t\t\t@change=\"onSwiperChange\"\r\n\t\t\t>\r\n\t\t\t\t<swiper-item \r\n\t\t\t\t\tv-for=\"(item, index) in promoDataList\" \r\n\t\t\t\t\t:key=\"item.id || index\"\r\n\t\t\t\t\tclass=\"swiper-item\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"promo-card\" @click=\"handlePromoClick(item)\">\r\n\t\t\t\t\t\t<!-- 1. 顶部标题 -->\r\n\t\t\t\t\t\t<view class=\"promo-header\">\r\n\t\t\t\t\t\t\t<image v-if=\"item.iconUrl\" :src=\"getFullImageUrl(item.iconUrl)\" class=\"promo-icon\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<text class=\"promo-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 2. 中间主图 -->\r\n\t\t\t\t\t<view class=\"image-container\">\r\n\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\tclass=\"promo-main-image\" \r\n\t\t\t\t\t\t\t:src=\"item.image\" \r\n\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t:lazy-load=\"true\"\r\n\t\t\t\t\t\t\t@error=\"onImageError\"\r\n\t\t\t\t\t\t\t@load=\"onImageLoad\"\r\n\t\t\t\t\t\t></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 3. 简介信息 -->\r\n\t\t\t\t\t\t\t<view class=\"promo-description\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.descriptionLine1\" class=\"desc-item\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"desc-icon\" :src=\"flameIconUrl\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.descriptionLine1 }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.descriptionLine2\" class=\"desc-item\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"desc-icon\" :src=\"thumbUpIconUrl\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.descriptionLine2 }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 4. 底部按钮 -->\r\n\t\t\t\t\t\t<view class=\"promo-footer\">\r\n\t\t\t\t\t\t\t<up-button \r\n\t\t\t\t\t\t\t\ttype=\"primary\" \r\n\t\t\t\t\t\t\t\tshape=\"square\" \r\n\t\t\t\t\t\t\t\ttext=\"立即报名\"\r\n\t\t\t\t\t\t\t\tsize=\"large\"\r\n                            @click=\"handlePromoClick(item)\"\r\n\t\t\t\t\t\t\t\t:customStyle=\"{ \r\n\t\t\t\t\t\t\t\t\tbackgroundColor: '#023F98',\r\n\t\t\t\t\t\t\t\t\theight: '68rpx',\r\n\t\t\t\t\t\t\t\t\twidth: '654rpx',\r\n\t\t\t\t\t\t\t\t\tborderRadius: '8rpx',\r\n\t\t\t\t\t\t\t\t\tmargin: '0 auto',\r\n\t\t\t\t\t\t\t\t\tborder: 'none'\r\n\t\t\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t></up-button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t\r\n\t\t\t<!-- 自定义指示器-->\r\n\t\t\t<view v-if=\"promoDataList.length > 1\" class=\"custom-indicators\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(item, index) in promoDataList\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\tclass=\"indicator-dot\"\r\n\t\t\t\t\t:class=\"{ 'active': currentIndex === index }\"\r\n\t\t\t\t\t@click=\"switchToSlide(index)\"\r\n\t\t\t\t></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-else class=\"no-data-tip\" style=\"padding: 40rpx; text-align: center; color: #999;\">\r\n\t\t\t<text>暂无推广活动</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { getPromotionEventListApi } from '@/api/data/event.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\n\r\n// 静态资源 URL（仅暗号读取）\r\nconst flameIconUrl = ref('')\r\nconst thumbUpIconUrl = ref('')\r\n\r\n// --- 响应式状态 ---\r\nconst promoDataList = ref([]); \r\nconst isLoading = ref(true);\r\nconst currentIndex = ref(0); \r\n\r\n/**\r\n * 获取推广活动数据\r\n */\r\nconst fetchPromoData = async () => {\r\n\ttry {\r\n\t\t// 调用新的活动推广API\r\n\t\tconst response = await getPromotionEventListApi({\r\n\t\t\tpageSize: 10\r\n\t\t});\r\n\t\t\r\n\t\tif (response && response.code === 200) {\r\n\t\t\t// 兼容两种数据格式：{rows: []} 和 {data: {rows: []}}\r\n\t\t\tlet dataArray = null;\r\n\t\t\tif (response.rows && Array.isArray(response.rows)) {\r\n\t\t\t\tdataArray = response.rows;\r\n\t\t\t} else if (response.data && response.data.rows && Array.isArray(response.data.rows)) {\r\n\t\t\t\tdataArray = response.data.rows;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (dataArray && Array.isArray(dataArray) && dataArray.length > 0) {\r\n\t\t\t\t// 处理活动推广数据\r\n\t\t\t\tpromoDataList.value = dataArray.map(event => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tid: event.id,\r\n\t\t\t\t\t\ttitle: event.promotionTitle || event.title, // 优先使用推广标题\r\n\t\t\t\t\t\timage: getFullImageUrl(event.promotionImageUrl || event.coverImageUrl), // 优先使用推广图片\r\n\t\t\t\t\t\ticonUrl: event.iconUrl, \r\n\t\t\t\t\t\tlinkUrl: `/pages_sub/pages_event/detail?id=${event.id}`, // 直接跳转到活动详情\r\n\t\t\t\t\t\t// 使用活动的简介和卖点字段，如果为空则提供默认文案\r\n\t\t\t\t\t\tdescriptionLine1: event.summary || '官方认证，品质保证',\r\n\t\t\t\t\t\tdescriptionLine2: event.sellPoint || '干货满满，不容错过'\r\n\t\t\t\t\t};\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('获取推广活动成功，数量:', promoDataList.value.length);\r\n\t\t\t} else {\r\n\t\t\t\tpromoDataList.value = [];\r\n\t\t\t\tconsole.warn('暂无推广活动数据');\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tpromoDataList.value = [];\r\n\t\t\tconsole.warn('获取推广活动失败，响应码:', response?.code);\r\n\t\t}\r\n\t\t\r\n\t} catch (error) {\r\n\t\tconsole.error('获取推广数据失败:', error.message);\r\n\t\tpromoDataList.value = [];\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 轮播切换事件处理\r\n */\r\nconst onSwiperChange = (e) => {\r\n\tcurrentIndex.value = e.detail.current;\r\n};\r\n\r\n/**\r\n * 点击指示器切换轮播\r\n */\r\nconst switchToSlide = (index) => {\r\n\tcurrentIndex.value = index;\r\n\tconsole.log('点击指示器切换到索引:', index);\r\n};\r\n\r\n/**\r\n * 处理卡片点击事件\r\n */\r\n/**\r\n * 处理卡片点击事件 - 简化版\r\n */\r\nconst handlePromoClick = (promoItem) => {\r\n    if (!promoItem || !promoItem.id) {\r\n        console.warn('推广卡片数据异常');\r\n        return;\r\n    }\r\n\r\n    // 直接跳转到活动详情页\r\n    uni.navigateTo({\r\n        url: `/pages_sub/pages_event/detail?id=${promoItem.id}`\r\n    });\r\n};\r\n\r\n/**\r\n * 图片加载成功事件\r\n */\r\nconst onImageLoad = (e) => {\r\n\tconsole.log('图片加载成功');\r\n};\r\n\r\n/**\r\n * 图片加载失败事件\r\n */\r\nconst onImageError = (e) => {\r\n\tconsole.error('图片加载失败:', e);\r\n};\r\n\r\n// --- 生命周期 ---\r\nonMounted(() => {\r\n\t// 读取静态资源配置\r\n\ttry {\r\n    const assets = uni.getStorageSync('staticAssets')\r\n    flameIconUrl.value = assets?.['flame-icon'] || ''\r\n    thumbUpIconUrl.value = assets?.['thumb-up-icon'] || ''\r\n\t} catch (e) {}\r\n\r\n\tfetchPromoData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.promo-container {\r\n\twidth: 702rpx;\r\n    height: auto; \r\n\tbackground: linear-gradient(180deg, rgba(2,63,152,0.1) 0%, rgba(2,63,152,0) 100%);\r\n\tborder-radius: 32rpx 32rpx 32rpx 32rpx;\r\n    margin: 24rpx 24rpx 0 24rpx; \r\n}\r\n\r\n.skeleton-wrapper {\r\n\tpadding: 24rpx;\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\theight: 100%;\r\n}\r\n\r\n// 轮播卡片容器\r\n.promo-card-wrapper {\r\n\tborder-radius: 16rpx 16rpx 0 0;\r\n\tbox-shadow: none;\r\n    height: auto;\r\n    padding-bottom: 16rpx;\r\n}\r\n// 轮播组件样式\r\n.promo-swiper {\r\n\twidth: 100%;\r\n    height: 560rpx; \r\n    position: relative;\r\n    z-index: 2; \r\n}\r\n\r\n.swiper-item {\r\n\twidth: 100%;\r\n\theight: 100%; \r\n}\r\n\r\n.promo-card {\r\n\tpadding: 24rpx;\r\n\twidth: 100%;\r\n\theight: 100%; \r\n\tbox-sizing: border-box;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.promo-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n\tflex-shrink: 0;\r\n\t\r\n\t.promo-icon {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 12rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t\r\n\t.promo-title {\r\n\t\twidth: 552rpx;\r\n\t\theight: 44rpx;\r\n\t\tfont-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n\t\tfont-weight: normal;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #23232A;\r\n\t\ttext-align: left;\r\n\t\tfont-style: normal;\r\n\t\ttext-transform: none;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\tflex: 1;\r\n\t\tline-height: 44rpx;\r\n\t}\r\n}\r\n\r\n.promo-main-image {\r\n\twidth: 100%;\r\n\tmax-height: 200rpx; \r\n\tborder-radius: 12rpx;\r\n\tdisplay: block;\r\n\tobject-fit: cover; \r\n\tflex-shrink: 0; \r\n}\r\n\r\n.image-container {\r\n\tbackground: #FFFFFF; \r\n\tborder-radius: 16rpx; \r\n\tpadding: 24rpx; \r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08); \r\n}\r\n\r\n.promo-description {\r\n\tmargin: 24rpx 0;\r\n\tflex: 1;\r\n\t\r\n\t.desc-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start; \r\n\t\tmargin-bottom: 16rpx;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.desc-icon {\r\n\t    width: 36rpx;\r\n\t    height: 36rpx;\r\n\t    flex-shrink: 0; \r\n\t}\r\n\t\r\n\t.desc-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #606266;\r\n\t\tmargin-left: 12rpx;\r\n\t\tline-height: 1.4;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n      line-clamp: 2; \r\n\t\t-webkit-line-clamp: 2; \r\n\t\toverflow: hidden;\r\n\t}\r\n}\r\n\r\n.promo-footer {\r\n\tflex-shrink: 0; \r\n\tmargin-top: auto; \r\n  position: relative;\r\n  z-index: 2;\r\n    margin-bottom: 24rpx; \r\n}\r\n\r\n// 自定义指示器样式 - 位于立即报名按钮下方\r\n.custom-indicators {\r\n    display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin-top: 0;\r\n        padding: 0;\r\n        gap: 12rpx;\r\n        position: relative;\r\n        z-index: 1;\r\n        pointer-events: none;\r\n}\r\n\r\n.indicator-dot {\r\n    width: 18rpx;\r\n        height: 8rpx;\r\n        border-radius: 4rpx;\r\n    \tbackground-color: #e4e7ed; \r\n    \ttransition: background-color 0.3s ease;\r\n        pointer-events: auto;\r\n        flex-shrink: 0;\r\n\t\r\n\t/* 激活状态 */\r\n\t&.active {\r\n\t\tbackground-color: #004085;\r\n\t}\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "getPromotionEventListApi", "getFullImageUrl", "uni", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;AA6GA,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAG7B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAK1B,UAAM,iBAAiB,YAAY;AAClC,UAAI;AAEH,cAAM,WAAW,MAAMC,wCAAyB;AAAA,UAC/C,UAAU;AAAA,QACb,CAAG;AAED,YAAI,YAAY,SAAS,SAAS,KAAK;AAEtC,cAAI,YAAY;AAChB,cAAI,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,GAAG;AAClD,wBAAY,SAAS;AAAA,UACrB,WAAU,SAAS,QAAQ,SAAS,KAAK,QAAQ,MAAM,QAAQ,SAAS,KAAK,IAAI,GAAG;AACpF,wBAAY,SAAS,KAAK;AAAA,UAC1B;AAED,cAAI,aAAa,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,GAAG;AAElE,0BAAc,QAAQ,UAAU,IAAI,WAAS;AAC5C,qBAAO;AAAA,gBACN,IAAI,MAAM;AAAA,gBACV,OAAO,MAAM,kBAAkB,MAAM;AAAA;AAAA,gBACrC,OAAOC,YAAAA,gBAAgB,MAAM,qBAAqB,MAAM,aAAa;AAAA;AAAA,gBACrE,SAAS,MAAM;AAAA,gBACf,SAAS,oCAAoC,MAAM,EAAE;AAAA;AAAA;AAAA,gBAErD,kBAAkB,MAAM,WAAW;AAAA,gBACnC,kBAAkB,MAAM,aAAa;AAAA,cAC3C;AAAA,YACA,CAAK;AAEDC,gCAAA,MAAA,OAAA,sDAAY,gBAAgB,cAAc,MAAM,MAAM;AAAA,UAC1D,OAAU;AACN,0BAAc,QAAQ;AACtBA,0BAAAA,MAAa,MAAA,QAAA,sDAAA,UAAU;AAAA,UACvB;AAAA,QACJ,OAAS;AACN,wBAAc,QAAQ;AACtBA,wBAAA,MAAA,MAAA,QAAA,sDAAa,iBAAiB,qCAAU,IAAI;AAAA,QAC5C;AAAA,MAED,SAAQ,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,sDAAc,aAAa,MAAM,OAAO;AACxC,sBAAc,QAAQ;MACxB,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,iBAAiB,CAAC,MAAM;AAC7B,mBAAa,QAAQ,EAAE,OAAO;AAAA,IAC/B;AAKA,UAAM,gBAAgB,CAAC,UAAU;AAChC,mBAAa,QAAQ;AACrBA,oBAAA,MAAA,MAAA,OAAA,sDAAY,eAAe,KAAK;AAAA,IACjC;AAQA,UAAM,mBAAmB,CAAC,cAAc;AACpC,UAAI,CAAC,aAAa,CAAC,UAAU,IAAI;AAC7BA,sBAAAA,0EAAa,UAAU;AACvB;AAAA,MACH;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,oCAAoC,UAAU,EAAE;AAAA,MAC7D,CAAK;AAAA,IACL;AAKA,UAAM,cAAc,CAAC,MAAM;AAC1BA,oBAAAA,MAAA,MAAA,OAAA,sDAAY,QAAQ;AAAA,IACrB;AAKA,UAAM,eAAe,CAAC,MAAM;AAC3BA,oBAAc,MAAA,MAAA,SAAA,sDAAA,WAAW,CAAC;AAAA,IAC3B;AAGAC,kBAAAA,UAAU,MAAM;AAEf,UAAI;AACD,cAAM,SAASD,cAAAA,MAAI,eAAe,cAAc;AAChD,qBAAa,SAAQ,iCAAS,kBAAiB;AAC/C,uBAAe,SAAQ,iCAAS,qBAAoB;AAAA,MACxD,SAAU,GAAG;AAAA,MAAE;AAEd;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjOD,GAAG,gBAAgB,SAAS;"}