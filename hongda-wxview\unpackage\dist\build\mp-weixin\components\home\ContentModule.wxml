<view class="content-module-container data-v-0d5e5c99"><view class="sub-tabs-container data-v-0d5e5c99"><scroll-view class="sub-tabs-wrapper data-v-0d5e5c99" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{a}}" wx:for-item="tag" wx:key="b" class="{{['sub-tab-item', 'data-v-0d5e5c99', tag.c && 'active']}}" bindtap="{{tag.d}}">{{tag.a}}</view></scroll-view><view class="divider data-v-0d5e5c99"></view></view><view class="article-list-container data-v-0d5e5c99"><view wx:if="{{b}}" class="loading-state data-v-0d5e5c99"><u-loading-icon wx:if="{{c}}" class="data-v-0d5e5c99" u-i="0d5e5c99-0" bind:__l="__l" u-p="{{c}}"></u-loading-icon></view><view wx:elif="{{d}}" class="article-list data-v-0d5e5c99"><view wx:for="{{e}}" wx:for-item="article" wx:key="c" class="article-item data-v-0d5e5c99" bindtap="{{article.d}}"><view class="dot data-v-0d5e5c99"></view><text class="article-title data-v-0d5e5c99">{{article.a}}</text><u-icon wx:if="{{f}}" class="data-v-0d5e5c99" u-i="{{article.b}}" bind:__l="__l" u-p="{{f}}"></u-icon></view></view><view wx:else class="empty-state data-v-0d5e5c99"><u-empty wx:if="{{g}}" class="data-v-0d5e5c99" u-i="0d5e5c99-2" bind:__l="__l" u-p="{{g}}"></u-empty></view></view></view>