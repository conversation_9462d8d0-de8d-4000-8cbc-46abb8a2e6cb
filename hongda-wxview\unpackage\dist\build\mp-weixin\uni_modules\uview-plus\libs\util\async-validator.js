"use strict";const e=/%[sdj%]/g;let r=function(){};function t(e){if(!e||!e.length)return null;const r={};return e.forEach((e=>{const{field:t}=e;r[t]=r[t]||[],r[t].push(e)})),r}function n(){for(var r=arguments.length,t=new Array(r),n=0;n<r;n++)t[n]=arguments[n];let i=1;const s=t[0],a=t.length;if("function"==typeof s)return s.apply(null,t.slice(1));if("string"==typeof s){let r=String(s).replace(e,(e=>{if("%%"===e)return"%";if(i>=a)return e;switch(e){case"%s":return String(t[i++]);case"%d":return Number(t[i++]);case"%j":try{return JSON.stringify(t[i++])}catch(r){return"[Circular]"}break;default:return e}}));for(let e=t[i];i<a;e=t[++i])r+=` ${e}`;return r}return s}function i(e,r){return null==e||(!("array"!==r||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}(r)||"string"!=typeof e||e))}function s(e,r,t){let n=0;const i=e.length;!function s(a){if(a&&a.length)return void t(a);const u=n;n+=1,u<i?r(e[u],s):t([])}([])}function a(e,r,n,i){if(r.first){const r=new Promise(((r,a)=>{const u=function(e){const r=[];return Object.keys(e).forEach((t=>{r.push.apply(r,e[t])})),r}(e);s(u,n,(function(e){return i(e),e.length?a({errors:e,fields:t(e)}):r()}))}));return r.catch((e=>e)),r}let a=r.firstFields||[];!0===a&&(a=Object.keys(e));const u=Object.keys(e),o=u.length;let f=0;const l=[],c=new Promise(((r,c)=>{const d=function(e){if(l.push.apply(l,e),f++,f===o)return i(l),l.length?c({errors:l,fields:t(l)}):r()};u.length||(i(l),r()),u.forEach((r=>{const t=e[r];-1!==a.indexOf(r)?s(t,n,d):function(e,r,t){const n=[];let i=0;const s=e.length;function a(e){n.push.apply(n,e),i++,i===s&&t(n)}e.forEach((e=>{r(e,a)}))}(t,n,d)}))}));return c.catch((e=>e)),c}function u(e){return function(r){return r&&r.message?(r.field=r.field||e.fullField,r):{message:"function"==typeof r?r():r,field:r.field||e.fullField}}}function o(e,r){if(r)for(const t in r)if(r.hasOwnProperty(t)){const n=r[t];"object"==typeof n&&"object"==typeof e[t]?e[t]={...e[t],...n}:e[t]=n}return e}function f(e,r,t,s,a,u){!e.required||t.hasOwnProperty(e.field)&&!i(r,u||e.type)||s.push(n(a.messages.required,e.fullField))}"undefined"!=typeof process&&process.env;const l={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i};var c={integer:function(e){return/^(-)?\d+$/.test(e)},float:function(e){return/^(-)?\d+(\.\d+)?$/.test(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(r){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear},number:function(e){return!isNaN(e)&&"number"==typeof+e},object:function(e){return"object"==typeof e&&!c.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&!!e.match(l.email)&&e.length<255},url:function(e){return"string"==typeof e&&!!e.match(l.url)},hex:function(e){return"string"==typeof e&&!!e.match(l.hex)}};const d={required:f,whitespace:function(e,r,t,i,s){(/^\s+$/.test(r)||""===r)&&i.push(n(s.messages.whitespace,e.fullField))},type:function(e,r,t,i,s){if(e.required&&void 0===r)return void f(e,r,t,i,s);const a=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(a)>-1?c[a](r)||i.push(n(s.messages.types[a],e.fullField,e.type)):a&&typeof r!==e.type&&i.push(n(s.messages.types[a],e.fullField,e.type))},range:function(e,r,t,i,s){const a="number"==typeof e.len,u="number"==typeof e.min,o="number"==typeof e.max,f=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;let l=r,c=null;const d="number"==typeof r,p="string"==typeof r,y=Array.isArray(r);if(d?c="number":p?c="string":y&&(c="array"),!c)return!1;y&&(l=r.length),p&&(l=r.replace(f,"_").length),a?l!==e.len&&i.push(n(s.messages[c].len,e.fullField,e.len)):u&&!o&&l<e.min?i.push(n(s.messages[c].min,e.fullField,e.min)):o&&!u&&l>e.max?i.push(n(s.messages[c].max,e.fullField,e.max)):u&&o&&(l<e.min||l>e.max)&&i.push(n(s.messages[c].range,e.fullField,e.min,e.max))},enum:function(e,r,t,i,s){e.enum=Array.isArray(e.enum)?e.enum:[],-1===e.enum.indexOf(r)&&i.push(n(s.messages.enum,e.fullField,e.enum.join(", ")))},pattern:function(e,r,t,i,s){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(r)||i.push(n(s.messages.pattern.mismatch,e.fullField,r,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(r)||i.push(n(s.messages.pattern.mismatch,e.fullField,r,e.pattern))}}};function p(e,r,t,n,s){const a=e.type,u=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r,a)&&!e.required)return t();d.required(e,r,n,u,s,a),i(r,a)||d.type(e,r,n,u,s)}t(u)}const y={string:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r,"string")&&!e.required)return t();d.required(e,r,n,a,s,"string"),i(r,"string")||(d.type(e,r,n,a,s),d.range(e,r,n,a,s),d.pattern(e,r,n,a,s),!0===e.whitespace&&d.whitespace(e,r,n,a,s))}t(a)},method:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&d.type(e,r,n,a,s)}t(a)},number:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(""===r&&(r=void 0),i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&(d.type(e,r,n,a,s),d.range(e,r,n,a,s))}t(a)},boolean:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&d.type(e,r,n,a,s)}t(a)},regexp:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),i(r)||d.type(e,r,n,a,s)}t(a)},integer:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&(d.type(e,r,n,a,s),d.range(e,r,n,a,s))}t(a)},float:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&(d.type(e,r,n,a,s),d.range(e,r,n,a,s))}t(a)},array:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r,"array")&&!e.required)return t();d.required(e,r,n,a,s,"array"),i(r,"array")||(d.type(e,r,n,a,s),d.range(e,r,n,a,s))}t(a)},object:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&d.type(e,r,n,a,s)}t(a)},enum:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s),void 0!==r&&d.enum(e,r,n,a,s)}t(a)},pattern:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r,"string")&&!e.required)return t();d.required(e,r,n,a,s),i(r,"string")||d.pattern(e,r,n,a,s)}t(a)},date:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();if(d.required(e,r,n,a,s),!i(r)){let t;t="number"==typeof r?new Date(r):r,d.type(e,t,n,a,s),t&&d.range(e,t.getTime(),n,a,s)}}t(a)},url:p,hex:p,email:p,required:function(e,r,t,n,i){const s=[],a=Array.isArray(r)?"array":typeof r;d.required(e,r,n,s,i,a),t(s)},any:function(e,r,t,n,s){const a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(i(r)&&!e.required)return t();d.required(e,r,n,a,s)}t(a)}};function h(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){const e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}const g=h();function m(e){this.rules=null,this._messages=g,this.define(e)}m.prototype={messages:function(e){return e&&(this._messages=o(h(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");let r,t;for(r in this.rules={},e)e.hasOwnProperty(r)&&(t=e[r],this.rules[r]=Array.isArray(t)?t:[t])},validate:function(e,r,i){const s=this;void 0===r&&(r={}),void 0===i&&(i=function(){});let f,l,c=e,d=r,p=i;if("function"==typeof d&&(p=d,d={}),!this.rules||0===Object.keys(this.rules).length)return p&&p(),Promise.resolve();if(d.messages){let e=this.messages();e===g&&(e=h()),o(e,d.messages),d.messages=e}else d.messages=this.messages();const y={};(d.keys||Object.keys(this.rules)).forEach((r=>{f=s.rules[r],l=c[r],f.forEach((t=>{let n=t;"function"==typeof n.transform&&(c===e&&(c={...c}),l=c[r]=n.transform(l)),n="function"==typeof n?{validator:n}:{...n},n.validator=s.getValidationMethod(n),n.field=r,n.fullField=n.fullField||r,n.type=s.getType(n),n.validator&&(y[r]=y[r]||[],y[r].push({rule:n,value:l,source:c,field:r}))}))}));const q={};return a(y,d,((e,r)=>{const{rule:t}=e;let i,s=!("object"!==t.type&&"array"!==t.type||"object"!=typeof t.fields&&"object"!=typeof t.defaultField);function a(e,r){return{...r,fullField:`${t.fullField}.${e}`}}function o(i){void 0===i&&(i=[]);let o=i;if(Array.isArray(o)||(o=[o]),!d.suppressWarning&&o.length&&m.warning("async-validator:",o),o.length&&t.message&&(o=[].concat(t.message)),o=o.map(u(t)),d.first&&o.length)return q[t.field]=1,r(o);if(s){if(t.required&&!e.value)return o=t.message?[].concat(t.message).map(u(t)):d.error?[d.error(t,n(d.messages.required,t.field))]:[],r(o);let i={};if(t.defaultField)for(const r in e.value)e.value.hasOwnProperty(r)&&(i[r]=t.defaultField);i={...i,...e.rule.fields};for(const e in i)if(i.hasOwnProperty(e)){const r=Array.isArray(i[e])?i[e]:[i[e]];i[e]=r.map(a.bind(null,e))}const s=new m(i);s.messages(d.messages),e.rule.options&&(e.rule.options.messages=d.messages,e.rule.options.error=d.error),s.validate(e.value,e.rule.options||d,(e=>{const t=[];o&&o.length&&t.push.apply(t,o),e&&e.length&&t.push.apply(t,e),r(t.length?t:null)}))}else r(o)}s=s&&(t.required||!t.required&&e.value),t.field=e.field,t.asyncValidator?i=t.asyncValidator(t,e.value,o,e.source,d):t.validator&&(i=t.validator(t,e.value,o,e.source,d),!0===i?o():!1===i?o(t.message||`${t.field} fails`):i instanceof Array?o(i):i instanceof Error&&o(i.message)),i&&i.then&&i.then((()=>o()),(e=>o(e)))}),(e=>{!function(e){let r,n=[],i={};function s(e){if(Array.isArray(e)){let r;n=(r=n).concat.apply(r,e)}else n.push(e)}for(r=0;r<e.length;r++)s(e[r]);n.length?i=t(n):(n=null,i=null),p(n,i)}(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!y.hasOwnProperty(e.type))throw new Error(n("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"==typeof e.validator)return e.validator;const r=Object.keys(e),t=r.indexOf("message");return-1!==t&&r.splice(t,1),1===r.length&&"required"===r[0]?y.required:y[this.getType(e)]||!1}},m.register=function(e,r){if("function"!=typeof r)throw new Error("Cannot register a validator by type, validator is not a function");y[e]=r},m.warning=r,m.messages=g,exports.Schema=m;
