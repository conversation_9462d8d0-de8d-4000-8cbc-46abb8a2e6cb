"use strict";const e=require("../../../../common/vendor.js"),t=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),r=require("../../libs/mixin/mixin.js"),n=require("../../libs/function/index.js"),o=require("../../libs/function/test.js"),d={name:"u-swiper",mixins:[i.mpMixin,r.mixin,t.props],data:()=>({currentIndex:0}),watch:{current(e,t){e!==t&&(this.currentIndex=e)}},emits:["click","change","update:current"],computed:{itemStyle(){return e=>{const t={};return this.nextMargin&&this.previousMargin&&(t.borderRadius=n.addUnit(this.radius),e!==this.currentIndex&&(t.transform="scale(0.92)")),t}}},methods:{addStyle:n.addStyle,addUnit:n.addUnit,testObject:o.test.object,testImage:o.test.image,getItemType(e){return"string"==typeof e?o.test.video(this.getSource(e))?"video":"image":"object"==typeof e&&this.keyName?e.type?"image"===e.type?"image":"video"===e.type?"video":"image":o.test.video(this.getSource(e))?"video":"image":void 0},getSource(e){return"string"==typeof e?e:"object"==typeof e&&this.keyName?e[this.keyName]:""},change(e){const{current:t}=e.detail;this.pauseVideo(this.currentIndex),this.currentIndex=t,this.$emit("update:current",this.currentIndex),this.$emit("change",e.detail)},pauseVideo(t){const i=this.getSource(this.list[t]);if(o.test.video(i)){e.index.createVideoContext(`video-${t}`,this).pause()}},getPoster:e=>"object"==typeof e&&e.poster?e.poster:"",clickHandler(e){this.$emit("click",e)}}};if(!Array){(e.resolveComponent("up-loading-icon")+e.resolveComponent("up-swiper-indicator"))()}Math||((()=>"../u-loading-icon/u-loading-icon.js")+(()=>"../u-swiper-indicator/u-swiper-indicator.js"))();const s=e._export_sfc(d,[["render",function(t,i,r,n,o,d){return e.e({a:t.loading},t.loading?{b:e.p({mode:"circle"})}:{c:e.f(t.list,((i,r,n)=>e.e({a:"image"===d.getItemType(i)},"image"===d.getItemType(i)?{b:d.getSource(i),c:t.imgMode,d:e.o((e=>d.clickHandler(r)),r),e:d.addUnit(t.height),f:d.addUnit(t.radius)}:{},{g:"video"===d.getItemType(i)},"video"===d.getItemType(i)?{h:`video-${r}`,i:d.getSource(i),j:d.getPoster(i),k:t.showTitle&&d.testObject(i)&&i.title?i.title:"",l:d.addUnit(t.height),m:e.o((e=>d.clickHandler(r)),r)}:{},{n:t.showTitle&&d.testObject(i)&&i.title&&d.testImage(d.getSource(i))},t.showTitle&&d.testObject(i)&&i.title&&d.testImage(d.getSource(i))?{o:e.t(i.title)}:{},{p:e.s(d.itemStyle(r)),q:"d-"+n,r:e.r("d",{item:i,index:r},n),s:r}))),d:d.addUnit(t.height),e:e.o(((...e)=>d.change&&d.change(...e))),f:t.circular,g:t.interval,h:t.duration,i:t.autoplay,j:t.current,k:t.currentItemId,l:d.addUnit(t.previousMargin),m:d.addUnit(t.nextMargin),n:t.acceleration,o:t.list.length>0?t.displayMultipleItems:0,p:t.easingFunction},{q:!t.loading&&t.indicator&&!t.showTitle},t.loading||!t.indicator||t.showTitle?{}:{r:e.p({indicatorActiveColor:t.indicatorActiveColor,indicatorInactiveColor:t.indicatorInactiveColor,length:t.list.length,current:o.currentIndex,indicatorMode:t.indicatorMode})},{s:e.s(d.addStyle(t.indicatorStyle)),t:t.bgColor,v:d.addUnit(t.height),w:d.addUnit(t.radius)})}],["__scopeId","data-v-b0cec7bf"]]);wx.createComponent(s);
