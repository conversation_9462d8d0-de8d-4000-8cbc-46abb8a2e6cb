"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/article.js"),l=require("../../api/content/tag.js"),o=require("../../utils/image.js"),t=require("../../api/content/region.js");if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("uni-easyinput")+e.resolveComponent("u-tabs")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-image")+e.resolveComponent("u-empty")+e.resolveComponent("u-loadmore"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-image/u-image.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js")+n)();const n=()=>"../../components/layout/CustomTabBar.js",r={__name:"index",setup(n){const r=e.ref([]),u=e.ref([{id:null,name:"全部"}]),i=e.ref(0),s=e.ref("loadmore"),m=e.ref(""),v=e.ref(null),c=e.ref({}),d=e.reactive({pageNum:1,pageSize:10,title:null,tagIds:null,orderByColumn:"sort_order",isAsc:"asc",status:"1","params[beginPublishTime]":null,"params[endPublishTime]":null,"params[region]":null}),g=e.reactive({sort:"default",region:"all",time:"all"}),p=e.reactive({sort:"default",region:"all",time:"all"}),f=[{name:"默认排序",value:"default"},{name:"最新发布",value:"publish_time"},{name:"热度排序",value:"view_count"}],h=e.ref([]),y=[{name:"不限时间",value:"all"},{name:"最近一周",value:"week"},{name:"最近一月",value:"month"},{name:"最近一年",value:"year"}],w=e.computed((()=>{const e=c.value.bg_article_header;return e?{backgroundImage:`url('${e}')`}:{}})),b=e.computed((()=>{const e=f.find((e=>e.value===p.sort));return e?e.name:"默认排序"})),_=e.computed((()=>"all"!==p.region||"all"!==p.time)),F=e.computed((()=>{if(!_.value)return"筛选";let e=0;return"all"!==p.region&&e++,"all"!==p.time&&e++,`筛选(${e})`})),T=e=>{v.value===e?v.value=null:(g.sort=p.sort,g.region=p.region,g.time=p.time,v.value=e)},x=()=>{v.value=null},S=()=>{p.region="all",p.time="all",g.region="all",g.time="all",A(),x(),C(!0)},j=()=>{p.region=g.region,p.time=g.time,A(),x(),C(!0)},A=()=>{d["params[region]"]="all"===p.region?null:p.region;const e=new Date;let a=null;if("all"!==p.time){const l=new Date;"week"===p.time?l.setDate(e.getDate()-7):"month"===p.time?l.setMonth(e.getMonth()-1):"year"===p.time&&l.setFullYear(e.getFullYear()-1),a=`${l.getFullYear()}-${String(l.getMonth()+1).padStart(2,"0")}-${String(l.getDate()).padStart(2,"0")}`}d["params[beginPublishTime]"]=a},C=async(l=!1)=>{if(l||"nomore"!==s.value){l&&(d.pageNum=1,r.value=[]),s.value="loading";try{const o=await a.getArticleList(d),t=o.rows.map((e=>{let a=[];if(e.tags&&"string"==typeof e.tags)try{a=JSON.parse(e.tags)}catch(l){console.error("解析文章标签JSON失败:",e.id,e.tags,l)}return{...e,parsedTags:Array.isArray(a)?a:[]}}));l?r.value=t:r.value.push(...t),o.rows.length<d.pageSize||r.value.length>=o.total?s.value="nomore":s.value="loadmore"}catch(o){s.value="loadmore",console.error("加载文章列表失败:",o),e.index.showToast({title:"加载失败",icon:"none"})}finally{e.index.stopPullDownRefresh()}}},z=async()=>{try{const e=await l.listAllTag(),a=Array.isArray(e.data)?e.data:[];u.value=[{id:null,name:"全部"},...a]}catch(e){console.error("加载标签列表失败:",e)}},k=async()=>{try{const e=(await t.listAllRegion()).data.map((e=>({name:e.name,value:e.code})));h.value=[{name:"全部地区",value:"all"},...e]}catch(e){console.error("加载地区列表失败:",e),h.value=[{name:"全部地区",value:"all"}]}},D=e=>{i.value=e.index,d.tagIds=null===e.id?null:String(e.id),C(!0)},B=e=>{m.value=e,e&&""!==e.trim()||P()},I=()=>{const e=m.value.trim();d.title=e||null,C(!0)},P=()=>{m.value="",d.title=null,C(!0),setTimeout((()=>e.index.hideKeyboard()),300)},q=()=>{},N=()=>{},$=()=>{"loadmore"===s.value&&(d.pageNum++,C())};return e.onMounted((()=>{c.value=e.index.getStorageSync("staticAssets")||{},(async()=>{try{await Promise.all([z(),k(),C(!0)])}catch(a){console.error("页面初始化失败:",a),e.index.showToast({title:"页面加载失败",icon:"none"})}})()})),e.onShow((()=>{e.index.hideTabBar()})),e.onPullDownRefresh((()=>{C(!0)})),e.onReachBottom((()=>{$()})),(a,l)=>e.e({a:e.t(b.value),b:e.p({name:"arrow-down-fill",color:"#FFFFFF",size:"10"}),c:"sort"===v.value?1:"",d:e.o((e=>T("sort"))),e:e.t(F.value),f:e.p({name:"arrow-down-fill",color:"#FFFFFF",size:"10"}),g:_.value},(_.value,{}),{h:"filter"===v.value||_.value?1:"",i:e.o((e=>T("filter"))),j:e.o(I),k:e.o(P),l:e.o(B),m:e.o((e=>m.value=e)),n:e.p({"suffix-icon":"search",placeholder:"搜索资讯",clearable:!0,modelValue:m.value}),o:e.s(w.value),p:u.value.length>1},u.value.length>1?{q:e.o(D),r:e.p({list:u.value,current:i.value,keyName:"name",lineColor:"#023F98",lineHeight:"6",lineWidth:"auto","show-scrollbar":!1,activeStyle:{color:"#023F98",fontSize:"32rpx",fontWeight:"bold"},inactiveStyle:{color:"#9B9A9A",fontSize:"32rpx",fontWeight:"normal"}})}:{},{s:e.f(r.value,((a,l,t)=>{return e.e({a:"a375fa30-5-"+t+",a375fa30-4-"+t,b:"a375fa30-6-"+t+",a375fa30-4-"+t,c:e.o(N,a.id),d:e.o(q,a.id),e:"a375fa30-4-"+t,f:e.p({src:e.unref(o.getFullImageUrl)(a.coverImageUrl),width:"100%",height:"190rpx",radius:"16",fade:!0,"lazy-load":!0}),g:e.t(a.title),h:a.parsedTags&&a.parsedTags.length>0},a.parsedTags&&a.parsedTags.length>0?{i:e.f(a.parsedTags,((a,l,o)=>({a:e.t(a.name),b:a.id})))}:{},{j:e.t(a.source),k:e.t((n=a.publishTime,n?n.split(" ")[0]:"")),l:a.id,m:e.o((l=>{return o=a.id,void e.index.navigateTo({url:`/pages_sub/pages_article/detail?id=${o}`});var o}),a.id)});var n})),t:e.p({color:"#667eea",size:"20"}),v:e.p({name:"photo-off",color:"#9ca3af",size:"24"}),w:"nomore"===s.value&&0===r.value.length},"nomore"===s.value&&0===r.value.length?{x:e.p({mode:"news",text:"暂无资讯",marginTop:"100"})}:{y:e.p({status:s.value,line:"true"})},{z:e.o($),A:v.value},v.value?e.e({B:e.o(x),C:"sort"===v.value},"sort"===v.value?{D:e.f(f,((a,l,o)=>e.e({a:e.t(a.name),b:g.sort===a.value},g.sort===a.value?{c:"a375fa30-9-"+o,d:e.p({name:"checkmark",color:"#023F98",size:"18"})}:{},{e:a.value,f:g.sort===a.value?1:"",g:e.o((e=>(e=>{p.sort=e.value,d.orderByColumn="default"===e.value?"sort_order":e.value,d.isAsc="default"===e.value?"asc":"desc",x(),C(!0)})(a)),a.value)})))}:{},{E:"filter"===v.value},"filter"===v.value?{F:e.f(h.value,((a,l,o)=>({a:e.t(a.name),b:a.value,c:g.region===a.value?1:"",d:e.o((e=>g.region=a.value),a.value)}))),G:e.f(y,((a,l,o)=>({a:e.t(a.name),b:a.value,c:g.time===a.value?1:"",d:e.o((e=>g.time=a.value),a.value)}))),H:e.o(S),I:e.o(j)}:{},{J:v.value?1:""}):{},{K:e.p({current:1})})}},u=e._export_sfc(r,[["__scopeId","data-v-a375fa30"]]);wx.createPage(u);
