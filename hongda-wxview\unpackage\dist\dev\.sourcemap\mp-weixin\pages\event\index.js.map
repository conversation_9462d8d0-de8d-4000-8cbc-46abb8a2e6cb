{"version": 3, "file": "index.js", "sources": ["pages/event/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZXZlbnQvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"event-list-page\">\n    <!-- 1. 头部背景和导航栏区域 -->\n    <view class=\"header-wrapper\">\n      <!-- 背景图片 - 完全覆盖状态栏 -->\n      <image class=\"header-bg\" :src=\"eventBgUrl\" mode=\"aspectFill\"></image>\n\n      <!-- 自定义导航栏 -->\n      <view class=\"custom-navbar\">\n        <view class=\"navbar-title\">\n          <text class=\"title-text\">热门活动列表</text>\n        </view>\n      </view>\n\n      <!-- 第一行 (Subsection + Search Bar) -->\n      <view class=\"top-controls\">\n        <up-subsection :list=\"['列表', '日历']\" :current=\"currentTab\" @change=\"tabChange\" mode=\"subsection\"\n                       activeColor=\"#f56c6c\"></up-subsection>\n\n        <view class=\"search-wrapper\">\n          <CustomSearchBox\n              v-model=\"searchKeyword\"\n              placeholder=\"搜索活动\"\n              @search=\"onSearch\"\n              @input=\"debouncedSearch\"\n          ></CustomSearchBox>\n        </view>\n      </view>\n    </view>\n\n    <!-- 2.1 列表视图的筛选栏 (移到scroll-view外部) -->\n    <view v-if=\"currentTab === 0\" class=\"filter-bar sticky-filter-bar\">\n      <!-- 筛选按钮组 -->\n      <view class=\"filter-main-buttons\">\n        <!-- 综合排序按钮 -->\n        <view class=\"filter-button\" @click=\"toggleSortPanel\">\n          <text class=\"filter-text\">{{ getCurrentSortTitleList }}</text>\n          <up-icon name=\"arrow-down\" size=\"14\" color=\"#666\" :class=\"{ 'rotate-180': showSortPanel }\"></up-icon>\n        </view>\n        <!-- 热门地区按钮 -->\n        <view class=\"filter-button\" @click=\"toggleLocationPanel\">\n          <text class=\"filter-text\">{{ getCurrentLocationTitleList }}</text>\n          <up-icon name=\"arrow-down\" size=\"14\" color=\"#666\" :class=\"{ 'rotate-180': showLocationPanel }\"></up-icon>\n        </view>\n        <!-- 全部时间按钮 -->\n        <view class=\"filter-button\" @click=\"toggleTimePanel\">\n          <text class=\"filter-text\">{{ getCurrentTimeTitleList }}</text>\n          <up-icon name=\"arrow-down\" size=\"14\" color=\"#666\" :class=\"{ 'rotate-180': showTimePanel }\"></up-icon>\n        </view>\n        <!-- 全部状态按钮 -->\n        <view class=\"filter-button\" @click=\"toggleStatusPanel\">\n          <text class=\"filter-text\">{{ getCurrentStatusTitleList }}</text>\n          <up-icon name=\"arrow-down\" size=\"14\" color=\"#666\" :class=\"{ 'rotate-180': showStatusPanel }\"></up-icon>\n        </view>\n      </view>\n\n      <!-- 综合排序面板 -->\n      <view v-if=\"showSortPanel\" class=\"filter-panel\">\n        <text class=\"section-title\">排序</text>\n        <view class=\"option-grid\">\n          <view\n              v-for=\"option in options1\"\n              :key=\"option.value\"\n              :class=\"['option-item', { 'active': tempFiltersList.sortBy === option.value }]\"\n              @click=\"selectSortOption(option.value)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n        <!-- 重置和完成按钮 -->\n        <view class=\"filter-buttons\">\n          <view class=\"filter-btn reset-btn\" @click=\"resetSortFilter\">\n            <text class=\"btn-text\">重置</text>\n          </view>\n          <view class=\"filter-btn complete-btn\" @click=\"completeSortFilter\">\n            <text class=\"btn-text\">完成</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 地区筛选面板 -->\n      <view v-if=\"showLocationPanel\" class=\"filter-panel\">\n        <!-- 全部地区 -->\n        <view class=\"option-grid\" style=\"margin-bottom: 20rpx;\">\n          <view\n              :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === allRegionOption.value) }]\"\n              @click=\"selectLocationOption(allRegionOption.value)\"\n          >\n            <text class=\"option-text\">{{ allRegionOption.label }}</text>\n          </view>\n        </view>\n        \n        <text class=\"section-title\">热门地区</text>\n        <view class=\"option-grid\">\n          <view\n              v-for=\"option in hotCities\"\n              :key=\"option.value\"\n              :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === option.value) }]\"\n              @click=\"selectLocationOption(option.value)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n        \n        <!-- 其他地区 -->\n        <view v-if=\"otherCities.length > 0\" style=\"margin-top: 20rpx;\">\n          <text class=\"section-title\">其他地区</text>\n          <view class=\"option-grid\">\n            <view\n                v-for=\"(city, index) in otherCities\"\n                :key=\"100 + index\"\n                :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === (100 + index)) }]\"\n                @click=\"selectLocationOption(100 + index)\"\n            >\n              <text class=\"option-text\">{{ city }}</text>\n            </view>\n          </view>\n        </view>\n        <!-- 重置和完成按钮 -->\n        <view class=\"filter-buttons\">\n          <view class=\"filter-btn reset-btn\" @click=\"resetLocationFilter\">\n            <text class=\"btn-text\">重置</text>\n          </view>\n          <view class=\"filter-btn complete-btn\" @click=\"completeLocationFilter\">\n            <text class=\"btn-text\">完成</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 时间筛选面板 -->\n      <view v-if=\"showTimePanel\" class=\"filter-panel\">\n        <text class=\"section-title\">时间</text>\n        <view class=\"option-grid\">\n          <view\n              v-for=\"option in options3\"\n              :key=\"option.value\"\n              :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.timeRange : tempFiltersList.timeRange) === option.value) }]\"\n              @click=\"selectTimeOption(option.value)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n        <!-- 重置和完成按钮 -->\n        <view class=\"filter-buttons\">\n          <view class=\"filter-btn reset-btn\" @click=\"resetTimeFilter\">\n            <text class=\"btn-text\">重置</text>\n          </view>\n          <view class=\"filter-btn complete-btn\" @click=\"completeTimeFilter\">\n            <text class=\"btn-text\">完成</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 状态筛选面板 -->\n      <view v-if=\"showStatusPanel\" class=\"filter-panel\">\n        <text class=\"section-title\">全部状态</text>\n        <view class=\"option-grid\">\n          <view\n              v-for=\"option in options4\"\n              :key=\"option.value\"\n              :class=\"['option-item', { 'active': tempFiltersList.status === option.value }]\"\n              @click=\"selectStatusOption(option.value)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n        <!-- 重置和完成按钮 -->\n        <view class=\"filter-buttons\">\n          <view class=\"filter-btn reset-btn\" @click=\"resetStatusFilter\">\n            <text class=\"btn-text\">重置</text>\n          </view>\n          <view class=\"filter-btn complete-btn\" @click=\"completeStatusFilter\">\n            <text class=\"btn-text\">完成</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 2.2 活动列表视图 -->\n    <scroll-view v-if=\"currentTab === 0\" scroll-y class=\"event-list-scroll list-scroll-with-filter\"\n                 @scrolltolower=\"onLoadMore\"\n                 refresher-enabled :refresher-triggered=\"isRefreshing\" @refresherrefresh=\"onRefresh\">\n\n      <!-- 空状态 -->\n      <view v-if=\"!isLoading && eventList.length === 0\" class=\"empty-state\">\n        <up-empty mode=\"data\" text=\"暂无活动数据\" textColor=\"#909399\" iconSize=\"120\"></up-empty>\n\n        <!-- 重试按钮 -->\n        <view v-if=\"showRetry\" class=\"retry-container\">\n          <up-button type=\"primary\" size=\"normal\" @click=\"fetchEventList\">\n            重新加载\n          </up-button>\n        </view>\n      </view>\n\n      <!-- 活动卡片列表 -->\n      <EventCard\n        v-for=\"event in eventList\"\n        :key=\"event.id\"\n        :event=\"event\"\n        @click=\"goToDetail(event)\"\n      />\n\n      <!-- 加载更多组件 -->\n      <view class=\"loadmore-wrapper\">\n        <up-loadmore :status=\"loadMoreStatus\" :loading-text=\"'正在加载...'\" :loadmore-text=\"'上拉加载更多'\"\n                     :nomore-text=\"'没有更多了'\"/>\n      </view>\n    </scroll-view>\n\n    <!-- 2.3 日历视图的筛选栏 -->\n    <view v-if=\"currentTab === 1\" class=\"filter-bar calendar-filter-bar sticky-filter-bar\">\n      <!-- 筛选按钮组 - 与列表视图样式保持一致 -->\n      <view class=\"filter-main-buttons\">\n        <!-- 热门地区按钮 -->\n        <view class=\"filter-button\" @click=\"toggleLocationPanel\">\n          <text class=\"filter-text\">{{ getCurrentLocationTitleCalendar }}</text>\n          <up-icon name=\"arrow-down\" size=\"14\" color=\"#666\" :class=\"{ 'rotate-180': showLocationPanel }\"></up-icon>\n        </view>\n        <!-- 全部时间按钮 -->\n        <view class=\"filter-button\" @click=\"toggleTimePanel\">\n          <text class=\"filter-text\">{{ getCurrentTimeTitleCalendar }}</text>\n          <up-icon name=\"arrow-down\" size=\"14\" color=\"#666\" :class=\"{ 'rotate-180': showTimePanel }\"></up-icon>\n        </view>\n        <!-- 空占位按钮 -->\n        <view class=\"filter-button filter-placeholder\">\n          <text class=\"filter-text\"></text>\n        </view>\n        <!-- 空占位按钮 -->\n        <view class=\"filter-button filter-placeholder\">\n          <text class=\"filter-text\"></text>\n        </view>\n      </view>\n\n      <!-- 地区筛选面板 -->\n      <view v-if=\"showLocationPanel\" class=\"filter-panel\">\n        <!-- 全部地区 -->\n        <view class=\"option-grid\" style=\"margin-bottom: 20rpx;\">\n          <view\n              :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === allRegionOption.value) }]\"\n              @click=\"selectLocationOption(allRegionOption.value)\"\n          >\n            <text class=\"option-text\">{{ allRegionOption.label }}</text>\n          </view>\n        </view>\n        \n        <text class=\"section-title\">热门地区</text>\n        <view class=\"option-grid\">\n          <view\n              v-for=\"option in hotCities\"\n              :key=\"option.value\"\n              :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === option.value) }]\"\n              @click=\"selectLocationOption(option.value)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n        \n        <!-- 其他地区 -->\n        <view v-if=\"otherCities.length > 0\" style=\"margin-top: 20rpx;\">\n          <text class=\"section-title\">其他地区</text>\n          <view class=\"option-grid\">\n            <view\n                v-for=\"(city, index) in otherCities\"\n                :key=\"100 + index\"\n                :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === (100 + index)) }]\"\n                @click=\"selectLocationOption(100 + index)\"\n            >\n              <text class=\"option-text\">{{ city }}</text>\n            </view>\n          </view>\n        </view>\n        <!-- 重置和完成按钮 -->\n        <view class=\"filter-buttons\">\n          <view class=\"filter-btn reset-btn\" @click=\"resetLocationFilter\">\n            <text class=\"btn-text\">重置</text>\n          </view>\n          <view class=\"filter-btn complete-btn\" @click=\"completeLocationFilter\">\n            <text class=\"btn-text\">完成</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 时间筛选面板 -->\n      <view v-if=\"showTimePanel\" class=\"filter-panel\">\n        <text class=\"section-title\">时间</text>\n        <view class=\"option-grid\">\n          <view\n              v-for=\"option in options3\"\n              :key=\"option.value\"\n              :class=\"['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.timeRange : tempFiltersList.timeRange) === option.value) }]\"\n              @click=\"selectTimeOption(option.value)\"\n          >\n            <text class=\"option-text\">{{ option.label }}</text>\n          </view>\n        </view>\n        <!-- 重置和完成按钮 -->\n        <view class=\"filter-buttons\">\n          <view class=\"filter-btn reset-btn\" @click=\"resetTimeFilter\">\n            <text class=\"btn-text\">重置</text>\n          </view>\n          <view class=\"filter-btn complete-btn\" @click=\"completeTimeFilter\">\n            <text class=\"btn-text\">完成</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 2.4 日历视图 -->\n    <scroll-view v-if=\"currentTab === 1\" scroll-y class=\"event-list-scroll calendar-view calendar-scroll-with-filter\"\n                 @scrolltolower=\"onLoadMore\" refresher-enabled :refresher-triggered=\"isRefreshing\"\n                 @refresherrefresh=\"onRefresh\">\n\n      <!-- 空状态 -->\n      <view v-if=\"!isLoading && limitedCalendarEvents.length === 0\" class=\"empty-state\">\n        <up-empty mode=\"data\" text=\"暂无活动数据\" textColor=\"#909399\" iconSize=\"120\"></up-empty>\n\n        <!-- 重试按钮 -->\n        <view v-if=\"showRetry\" class=\"retry-container\">\n          <up-button type=\"primary\" size=\"normal\" @click=\"fetchEventList\">\n            重新加载\n          </up-button>\n        </view>\n      </view>\n\n      <EventCalendarTimeline\n        v-else\n        :groups=\"limitedCalendarEvents\"\n        :has-more=\"hasMoreCalendarEvents\"\n        :notch-left=\"calendarNotchLeft\"\n        @click-item=\"goToDetail\"\n        @view-more=\"switchToListView\"\n      />\n\n      <!-- 底部安全间距，防止被自定义TabBar遮挡 -->\n      <view class=\"calendar-bottom-safe\" />\n\n\n    </scroll-view>\n\n    <!-- 筛选面板遮罩层 -->\n    <view \n      v-if=\"showSortPanel || showLocationPanel || showTimePanel || showStatusPanel\" \n      class=\"filter-mask\" \n      @click=\"closeAllPanels\"\n    ></view>\n\n    <!-- 自定义底部导航栏 -->\n    <CustomTabBar :current=\"2\"/>\n  </view>\n</template>\n\n<script setup>\nimport {\n  ref,\n  computed\n} from 'vue';\nimport {\n  onLoad,\n  onReachBottom,\n  onPullDownRefresh,\n  onUnload,\n  onShow\n} from '@dcloudio/uni-app';\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\nimport CustomSearchBox from '@/components/home/<USER>';\nimport EventCard from '@/components/event/EventCard.vue';\nimport EventCalendarTimeline from '@/components/event/EventCalendarTimeline.vue';\nimport {\n  getEventListApi,\n  getCalendarEventsApi,\n  getEventCitiesApi\n} from '@/api/data/event.js';\nimport {\n  formatEventStatus,\n  getStatusClass,\n  calculateRemainingSpots,\n  debounce\n} from '@/utils/tools.js';\nimport { formatEventDate, parseDate } from '@/utils/date.js';\nimport {\n  PAGE_CONFIG\n} from '@/utils/config.js';\nimport {getFullImageUrl} from '@/utils/image.js';\n\n// ==================== 响应式数据定义 ====================\nconst currentTab = ref(0); // 默认显示列表视图\nconst searchKeyword = ref('');\nconst eventList = ref([]);\nconst isLoading = ref(false);\nconst isRefreshing = ref(false);\nconst showRetry = ref(false);\n\n// 静态资源 URL（不再使用本地兜底）\nconst eventBgUrl = ref('');\n\n// 分页相关\nconst pagination = ref({\n  pageNum: 1,\n  pageSize: PAGE_CONFIG.DEFAULT_PAGE_SIZE,\n  total: 0,\n  hasMore: true\n});\n\n// 已应用的筛选条件（列表与日历视图分离）\nconst appliedFiltersList = ref({\n  sortBy: 1,\n  location: 1,\n  timeRange: 1,\n  status: 1\n});\nconst appliedFiltersCalendar = ref({\n  location: 1,\n  timeRange: 1\n});\n\n// 临时筛选条件（用于UI显示，未应用到数据；列表与日历视图分离）\nconst tempFiltersList = ref({\n  sortBy: 1,\n  location: 1,\n  timeRange: 1,\n  status: 1\n});\nconst tempFiltersCalendar = ref({\n  location: 1,\n  timeRange: 1\n});\n\n// 控制各个筛选面板显示\nconst showSortPanel = ref(false);\nconst showLocationPanel = ref(false);\nconst showTimePanel = ref(false);\nconst showStatusPanel = ref(false);\n  // 日历视图 corner-notch 位置：默认指向\"热门地区\"60rpx\n  const calendarNotchLeft = ref('60rpx');\n\n// 日历视图显示控制 \n\n// 下拉选项配置\nconst options1 = ref([{\n  label: '综合排序',\n  value: 1\n},\n  {\n    label: '最新发布',\n    value: 2\n\n  },\n  {\n    label: '最近开始',\n    value: 3\n  }\n]);\n\n// 地区选项配置\nconst allRegionOption = ref({ label: '全部地区', value: 1 });\n\n// 热门城市配置（不包含全部地区）\nconst hotCities = ref([\n  { label: '北京', value: 2 },\n  { label: '上海', value: 3 },\n  { label: '广州', value: 4 },\n  { label: '深圳', value: 5 }\n]);\n\n// 其他城市数据（从API获取）\nconst otherCities = ref([]);\n\n// 合并的地区选项\nconst options2 = computed(() => [\n  allRegionOption.value,\n  ...hotCities.value,\n  ...otherCities.value.map((city, index) => ({\n    label: city,\n    value: 100 + index // 避免与热门城市ID冲突\n  }))\n]);\n\nconst options3 = ref([{\n  label: '全部时间',\n  value: 1\n},\n  {\n    label: '1周内',\n    value: 2\n  },\n  {\n    label: '1月内',\n    value: 3\n  },\n  {\n    label: '1年内',\n    value: 4\n  }\n]);\n\nconst options4 = ref([{\n  label: '全部状态',\n  value: 1\n},\n  {\n    label: '即将开始',\n    value: 2\n  },\n  {\n    label: '报名中',\n    value: 3\n  },\n  {\n    label: '报名截止',\n    value: 4\n  }\n]);\n\n// ==================== 计算属性 ====================\nconst loadMoreStatus = computed(() => {\n  if (isLoading.value) return 'loading';\n  if (!pagination.value.hasMore) return 'nomore';\n  return 'more';\n});\n\n/**\n * 按日期分组活动数据 - 用于日历视图\n */\nconst groupedEvents = computed(() => {\n  if (!eventList.value || eventList.value.length === 0) {\n    return [];\n  }\n  const groups = new Map();\n  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n  eventList.value.forEach(event => {\n    const eventDate = parseDate(event.startTime);\n    // 💥 关键修改：处理不同年份的日期显示\n    const year = eventDate.getFullYear();\n    const currentYear = new Date().getFullYear();\n    const month = String(eventDate.getMonth() + 1).padStart(2, '0');\n    const day = String(eventDate.getDate()).padStart(2, '0');\n\n    let displayDate;\n    if (year !== currentYear) {\n      displayDate = `${year}年${month}月${day}日`;\n    } else {\n      displayDate = `${month}.${day}`;\n    }\n\n    // 避免使用 toISOString 造成的时区偏移，改用本地日期计算 key\n    const monthLocal = String(eventDate.getMonth() + 1).padStart(2, '0');\n    const dayLocal = String(eventDate.getDate()).padStart(2, '0');\n    const dateKey = `${eventDate.getFullYear()}-${monthLocal}-${dayLocal}`;\n    if (!groups.has(dateKey)) {\n      groups.set(dateKey, {\n        date: dateKey,\n        formattedDate: displayDate, // 使用新的日期格式\n        dayOfWeek: weekdays[eventDate.getDay()],\n        events: []\n      });\n    }\n    groups.get(dateKey).events.push(event);\n  });\n  return Array.from(groups.values());\n});\n\n/**\n * 限制日历视图显示的活动数量 - 最多显示10个活动\n */\nconst limitedCalendarEvents = computed(() => {\n  // 日历视图始终限制为最多10个活动\n  let totalEvents = 0;\n  const limitedGroups = [];\n\n  for (const group of groupedEvents.value) {\n    if (totalEvents >= 10) break;\n\n    const remainingSlots = 10 - totalEvents;\n    const eventsToShow = group.events.slice(0, remainingSlots);\n\n    limitedGroups.push({\n      ...group,\n      events: eventsToShow\n    });\n\n    totalEvents += eventsToShow.length;\n  }\n\n  return limitedGroups;\n});\n\n/**\n * 检查是否有更多活动需要显示\n */\nconst hasMoreCalendarEvents = computed(() => {\n  const totalEvents = groupedEvents.value.reduce((sum, group) => sum + group.events.length, 0);\n  return totalEvents > 10;\n});\n\n// ==================== 核心方法 ====================\n\n/**\n * 获取活动城市列表\n */\nconst fetchEventCities = async () => {\n  try {\n    const response = await getEventCitiesApi();\n    \n    // 尝试不同的数据提取方式\n    let cityList = null;\n    if (Array.isArray(response)) {\n      cityList = response;\n    } else if (response && Array.isArray(response.data)) {\n      cityList = response.data;\n    } else if (response && response.code === 200 && Array.isArray(response.data)) {\n      cityList = response.data;\n    }\n    \n    console.log('提取的城市列表:', cityList);\n    \n    if (cityList && Array.isArray(cityList)) {\n      // 过滤掉热门城市中已有的城市\n      const hotCityNames = hotCities.value.map(city => city.label);\n      const filteredCities = cityList.filter(city => \n        city && city.trim() && !hotCityNames.includes(city.trim())\n      );\n      \n      otherCities.value = filteredCities;\n    } else {\n      uni.showToast({\n        title: '城市数据格式错误',\n        icon: 'none',\n        duration: 2000\n      });\n    }\n  } catch (error) {\n    console.error('获取城市列表失败:', error);\n    uni.showToast({\n      title: '获取城市列表失败',\n      icon: 'none',\n      duration: 2000\n    });\n  }\n};\n\n/**\n * 格式化活动地点 - 使用统一的地点格式化函数\n * 对于直辖市，会正确显示为\"上海市\"而不是\"上海\"\n */\nconst formatEventLocation = (event) => {\n  // 直辖市列表\n  const MUNICIPALITIES = ['北京', '上海', '天津', '重庆'];\n  \n  if (!event) return '待定';\n  \n  // 优先使用city字段\n  if (event.city && event.city.trim()) {\n    const city = event.city.trim();\n    \n    // 检查是否为直辖市\n    const isMunicipality = MUNICIPALITIES.some(municipality => \n      city.includes(municipality)\n    );\n    \n    if (isMunicipality) {\n      // 对于直辖市，如果有province字段且包含直辖市名称，使用province\n      if (event.province && event.province.trim()) {\n        const province = event.province.trim();\n        const municipalityMatch = MUNICIPALITIES.find(municipality => \n          province.includes(municipality)\n        );\n        if (municipalityMatch) {\n          // 如果province字段已经包含\"市\"，直接返回；否则添加\"市\"\n          return province.endsWith('市') ? province : `${municipalityMatch}市`;\n        }\n      }\n      // 如果province字段不可用，从city字段中提取直辖市名称\n      const municipalityMatch = MUNICIPALITIES.find(municipality => \n        city.includes(municipality)\n      );\n      if (municipalityMatch) {\n        return city.endsWith('市') ? city : `${municipalityMatch}市`;\n      }\n    }\n    \n    // 对于非直辖市，直接返回city字段\n    return city;\n  }\n  \n  // 如果没有city字段，尝试使用province字段\n  if (event.province && event.province.trim()) {\n    const province = event.province.trim();\n    \n    // 检查province是否为直辖市\n    const isMunicipality = MUNICIPALITIES.some(municipality => \n      province.includes(municipality)\n    );\n    \n    if (isMunicipality) {\n      const municipalityMatch = MUNICIPALITIES.find(municipality => \n        province.includes(municipality)\n      );\n      return province.endsWith('市') ? province : `${municipalityMatch}市`;\n    }\n    \n    return province;\n  }\n  \n  return '待定';\n};\n/**\n  * 构建查询参数（按当前视图分别读取筛选条件）\n */\nconst buildQueryParams = (isLoadMore = false) => {\n  const params = {\n    pageNum: isLoadMore ? pagination.value.pageNum : 1,\n    pageSize: pagination.value.pageSize\n  };\n\n  // 搜索关键词\n  if (searchKeyword.value.trim()) {\n    params.title = searchKeyword.value.trim();\n  }\n\n  // 依据当前视图选择对应的筛选集合\n  const filters = currentTab.value === 1 ? appliedFiltersCalendar.value : appliedFiltersList.value;\n\n  // 地区筛选\n  if (filters.location > 1) {\n    // 热门城市映射\n    const hotLocationMap = {\n      2: '北京',\n      3: '上海',\n      4: '广州',\n      5: '深圳'\n    };\n    \n    if (hotLocationMap[filters.location]) {\n      // 热门城市\n      params.location = hotLocationMap[filters.location];\n    } else if (filters.location >= 100) {\n      // 其他城市（从otherCities数组获取）\n      const otherIndex = filters.location - 100;\n      if (otherIndex < otherCities.value.length) {\n        params.location = otherCities.value[otherIndex];\n      }\n    }\n  }\n\n  // 报名状态筛选（仅列表视图）\n  if (currentTab.value === 0 && filters.status > 1) {\n    const registrationStatusMap = {\n      2: 0, // 即将开始 -> registrationStatus: 0\n      3: 1, // 报名中 -> registrationStatus: 1\n      4: 2  // 报名截止 -> registrationStatus: 2\n    };\n\n    if (registrationStatusMap.hasOwnProperty(filters.status)) {\n      params.registrationStatus = registrationStatusMap[filters.status];\n    } else {\n      console.warn('未知的报名状态筛选值:', filters.status);\n    }\n  }\n\n  // 排序设置\n  if (currentTab.value === 1) {\n    // 如果是日历视图，强制按开始时间升序排序\n    params.orderBy = 'startTime';\n    params.isAsc = 'asc';\n  } else {\n    // 列表视图排序逻辑 - 支持综合排序（使用已应用的筛选条件）\n    switch (filters.sortBy) {\n      case 1: // 综合排序\n        params.orderBy = 'comprehensive';\n        break;\n      case 2: // 按时间（最近开始）\n        params.orderBy = 'startTime';\n        params.isAsc = 'asc';\n        console.log('按时间排序: 最近开始优先');\n        break;\n      case 3: // 按最新发布\n        params.orderBy = 'createTime';\n        params.isAsc = 'desc';\n        console.log('按最新发布排序: 最新创建的活动优先');\n        break;\n      default: // 默认按创建时间（最新发布）\n        params.orderBy = 'createTime';\n        params.isAsc = 'desc';\n        console.log('默认排序: 最新发布');\n    }\n  }\n\n  // 时间范围筛选 - 新的时间筛选逻辑（使用已应用的筛选条件）\n  if (filters.timeRange > 1) {\n    const timeRange = buildTimeRangeParams(filters.timeRange);\n    if (timeRange) {\n      Object.assign(params, timeRange);\n    }\n  }\n\n  return params;\n};\n\n/**\n * 构建时间范围筛选参数 - 独立的时间筛选逻辑\n * @param {number} timeRangeValue 时间范围选项值\n * @returns {object|null} 时间筛选参数对象\n */\nconst buildTimeRangeParams = (timeRangeValue) => {\n  const now = new Date();\n  let startTime = null;\n  let endTime = null;\n\n  switch (timeRangeValue) {\n    case 2: // 1周内\n      startTime = now;\n      endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);\n      console.log('时间筛选: 1周内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());\n      break;\n    case 3: // 1月内\n      startTime = now;\n      endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());\n      console.log('时间筛选: 1月内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());\n      break;\n    case 4: // 1年内\n      startTime = now;\n      endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n      console.log('时间筛选: 1年内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());\n      break;\n    default:\n      console.log('时间筛选: 全部时间');\n      return null;\n  }\n\n  return {\n    timeRangeStart: startTime.toISOString(),\n    timeRangeEnd: endTime.toISOString()\n  };\n};\n\n/**\n * 获取活动列表\n */\nconst fetchEventList = async (isLoadMore = false) => {\n  if (isLoading.value) return;\n\n  isLoading.value = true;\n\n  try {\n    const params = buildQueryParams(isLoadMore);\n    console.log('请求参数:', params);\n\n    // 根据当前视图选择不同的API\n    let response;\n    if (currentTab.value === 1) {\n      // 日历视图使用专门的API\n      response = await getCalendarEventsApi(params);\n    } else {\n      // 列表视图使用通用API\n      response = await getEventListApi(params);\n    }\n    const {\n      rows = [], total = 0\n    } = response;\n\n    if (isLoadMore) {\n      eventList.value.push(...rows);\n    } else {\n      eventList.value = rows;\n      pagination.value.pageNum = 1;\n    }\n\n    pagination.value.total = total;\n    pagination.value.hasMore = eventList.value.length < total;\n\n    // 成功加载后隐藏重试按钮\n    showRetry.value = false;\n\n    console.log(`获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);\n\n  } catch (error) {\n    console.error('获取活动列表失败:', error);\n\n    // 根据错误类型提供不同的提示\n    let errorMessage = '获取活动列表失败';\n    if (error.message && error.message.includes('timeout')) {\n      errorMessage = '网络请求超时，请重试';\n    } else if (error.message && error.message.includes('Network')) {\n      errorMessage = '网络连接失败，请检查网络';\n    }\n\n    uni.showToast({\n      title: errorMessage,\n      icon: 'none',\n      duration: 3000\n    });\n\n    // 如果是首次加载失败，显示重试按钮\n    if (!isLoadMore && eventList.value.length === 0) {\n      showRetry.value = true;\n    }\n  } finally {\n    isLoading.value = false;\n    isRefreshing.value = false;\n  }\n};\n\n/**\n * 搜索防抖处理\n */\nconst debouncedSearch = debounce(() => {\n  fetchEventList();\n}, 500);\n\n// ==================== 计算属性 ====================\n/**\n * 获取当前排序方式的显示标题（列表视图）\n */\nconst getCurrentSortTitleList = computed(() => {\n  const currentOption = options1.value.find(option => option.value === appliedFiltersList.value.sortBy);\n  const title = currentOption ? currentOption.label : '综合排序';\n  return title;\n});\n\n/**\n * 获取当前状态筛选的显示标题（列表视图）\n */\nconst getCurrentStatusTitleList = computed(() => {\n  const currentOption = options4.value.find(option => option.value === appliedFiltersList.value.status);\n  const title = currentOption ? currentOption.label : '全部状态';\n  return title;\n});\n\n/**\n * 获取当前地区筛选的显示标题（列表视图）\n */\nconst getCurrentLocationTitleList = computed(() => {\n  const currentOption = options2.value.find(option => option.value === appliedFiltersList.value.location);\n  return currentOption ? currentOption.label : '热门地区';\n});\n\n/**\n * 获取当前时间筛选的显示标题（列表视图）\n */\nconst getCurrentTimeTitleList = computed(() => {\n  const currentOption = options3.value.find(option => option.value === appliedFiltersList.value.timeRange);\n  return currentOption ? currentOption.label : '全部时间';\n});\n\n/**\n * 获取当前地区筛选的显示标题（日历视图）\n */\nconst getCurrentLocationTitleCalendar = computed(() => {\n  const currentOption = options2.value.find(option => option.value === appliedFiltersCalendar.value.location);\n  return currentOption ? currentOption.label : '热门地区';\n});\n\n/**\n * 获取当前时间筛选的显示标题（日历视图）\n */\nconst getCurrentTimeTitleCalendar = computed(() => {\n  const currentOption = options3.value.find(option => option.value === appliedFiltersCalendar.value.timeRange);\n  return currentOption ? currentOption.label : '全部时间';\n});\n\n// ==================== 事件处理方法 ====================\n/**\n * 标签页切换\n */\nconst tabChange = (index) => {\n  currentTab.value = index; // 直接使用 index，因为 u-subsection 的 @change 事件直接返回索引号\n  // 关键：切换后立即重置数据，以保证用户看到即时加载效果\n  eventList.value = [];\n  pagination.value.pageNum = 1;\n  pagination.value.hasMore = true;\n  // 重新调用数据获取函数，此时 buildQueryParams 会根据新的 currentTab 值应用正确的排序\n  fetchEventList();\n};\n\n/**\n * 搜索处理\n */\nconst onSearch = (value) => {\n  searchKeyword.value = value;\n  debouncedSearch();\n};\n\n/**\n * 筛选条件变更\n */\nconst onFilterChange = () => {\n  console.log('筛选条件变更，重置数据并重新加载');\n  // 重置分页状态\n  eventList.value = [];\n  pagination.value.pageNum = 1;\n  pagination.value.hasMore = true;\n  // 重新获取数据\n  fetchEventList();\n};\n\n/**\n * 切换排序面板显示状态\n */\nconst toggleSortPanel = () => {\n  showSortPanel.value = !showSortPanel.value;\n  // 关闭其他面板\n  showLocationPanel.value = false;\n  showTimePanel.value = false;\n  showStatusPanel.value = false;\n};\n\n/**\n * 切换地区面板显示状态\n */\nconst toggleLocationPanel = () => {\n  showLocationPanel.value = !showLocationPanel.value;\n  // 关闭其他面板\n  showSortPanel.value = false;\n  showTimePanel.value = false;\n  showStatusPanel.value = false;\n  // 指向\"热门地区\"\n  calendarNotchLeft.value = '60rpx';\n};\n\n/**\n * 切换时间面板显示状态\n */\nconst toggleTimePanel = () => {\n  showTimePanel.value = !showTimePanel.value;\n  // 关闭其他面板\n  showSortPanel.value = false;\n  showLocationPanel.value = false;\n  showStatusPanel.value = false;\n  // 指向“全部时间”\n  calendarNotchLeft.value = '240rpx';\n};\n\n/**\n * 切换状态面板显示状态\n */\nconst toggleStatusPanel = () => {\n  showStatusPanel.value = !showStatusPanel.value;\n  // 关闭其他面板\n  showSortPanel.value = false;\n  showLocationPanel.value = false;\n  showTimePanel.value = false;\n  // 状态面板打开时保持上一次位置，不做指向变动\n};\n\n/**\n * 选择排序选项（仅更新临时筛选条件，不立即应用）\n */\nconst selectSortOption = (value) => {\n  // 排序只作用于列表视图\n  tempFiltersList.value.sortBy = value;\n  console.log('临时选择排序:', value);\n  // 不关闭面板，不立即应用筛选\n};\n\n/**\n * 选择地区选项（仅更新临时筛选条件，不立即应用）\n */\nconst selectLocationOption = (value) => {\n  if (currentTab.value === 1) {\n    tempFiltersCalendar.value.location = value;\n  } else {\n    tempFiltersList.value.location = value;\n  }\n  console.log('临时选择地区:', value);\n  // 不关闭面板，不立即应用筛选\n};\n\n/**\n * 选择时间选项（仅更新临时筛选条件，不立即应用）\n */\nconst selectTimeOption = (value) => {\n  if (currentTab.value === 1) {\n    tempFiltersCalendar.value.timeRange = value;\n  } else {\n    tempFiltersList.value.timeRange = value;\n  }\n  console.log('临时选择时间:', value);\n  // 不关闭面板，不立即应用筛选\n};\n\n/**\n * 选择状态选项（仅更新临时筛选条件，不立即应用）\n */\nconst selectStatusOption = (value) => {\n  // 状态只作用于列表视图\n  tempFiltersList.value.status = value;\n  console.log('临时选择状态:', value);\n  // 不关闭面板，不立即应用筛选\n};\n\n/**\n * 重置排序筛选（重置为页面初始状态并立即应用）\n */\nconst resetSortFilter = () => {\n  // 重置临时筛选条件为初始值\n  tempFiltersList.value.sortBy = 1;\n  // 立即应用重置后的筛选条件（仅列表）\n  appliedFiltersList.value.sortBy = 1;\n  showSortPanel.value = false;\n  console.log('重置排序筛选为初始状态');\n  onFilterChange();\n};\n\n/**\n * 完成排序筛选（应用临时筛选条件并关闭面板）\n */\nconst completeSortFilter = () => {\n  // 将临时筛选条件应用到已应用筛选条件（仅列表）\n  appliedFiltersList.value.sortBy = tempFiltersList.value.sortBy;\n  showSortPanel.value = false;\n  console.log('应用排序筛选:', tempFiltersList.value.sortBy);\n  onFilterChange();\n};\n\n/**\n * 重置地区筛选（重置为页面初始状态并立即应用）\n */\nconst resetLocationFilter = () => {\n  if (currentTab.value === 1) {\n    tempFiltersCalendar.value.location = 1;\n    appliedFiltersCalendar.value.location = 1;\n  } else {\n    tempFiltersList.value.location = 1;\n    appliedFiltersList.value.location = 1;\n  }\n  showLocationPanel.value = false;\n  console.log('重置地区筛选为初始状态');\n  onFilterChange();\n};\n\n/**\n * 完成地区筛选（应用临时筛选条件并关闭面板）\n */\nconst completeLocationFilter = () => {\n  if (currentTab.value === 1) {\n    appliedFiltersCalendar.value.location = tempFiltersCalendar.value.location;\n  } else {\n    appliedFiltersList.value.location = tempFiltersList.value.location;\n  }\n  showLocationPanel.value = false;\n  console.log('应用地区筛选:', currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location);\n  onFilterChange();\n  // 选择完成后指向\"热门地区\"\n  calendarNotchLeft.value = '60rpx';\n};\n\n/**\n * 重置时间筛选（重置为页面初始状态并立即应用）\n */\nconst resetTimeFilter = () => {\n  if (currentTab.value === 1) {\n    tempFiltersCalendar.value.timeRange = 1;\n    appliedFiltersCalendar.value.timeRange = 1;\n  } else {\n    tempFiltersList.value.timeRange = 1;\n    appliedFiltersList.value.timeRange = 1;\n  }\n  showTimePanel.value = false;\n  console.log('重置时间筛选为初始状态');\n  onFilterChange();\n};\n\n/**\n * 完成时间筛选（应用临时筛选条件并关闭面板）\n */\nconst completeTimeFilter = () => {\n  if (currentTab.value === 1) {\n    appliedFiltersCalendar.value.timeRange = tempFiltersCalendar.value.timeRange;\n  } else {\n    appliedFiltersList.value.timeRange = tempFiltersList.value.timeRange;\n  }\n  showTimePanel.value = false;\n  console.log('应用时间筛选:', currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange);\n  onFilterChange();\n  // 选择完成后仍保持指向\"全部时间\"\n  calendarNotchLeft.value = '240rpx';\n};\n\n/**\n * 重置状态筛选（重置为页面初始状态并立即应用）\n */\nconst resetStatusFilter = () => {\n  // 状态仅列表视图生效\n  tempFiltersList.value.status = 1;\n  appliedFiltersList.value.status = 1;\n  showStatusPanel.value = false;\n  console.log('重置状态筛选为初始状态');\n  onFilterChange();\n};\n\n/**\n * 完成状态筛选（应用临时筛选条件并关闭面板）\n */\nconst completeStatusFilter = () => {\n  // 状态仅列表视图生效\n  appliedFiltersList.value.status = tempFiltersList.value.status;\n  showStatusPanel.value = false;\n  console.log('应用状态筛选:', tempFiltersList.value.status);\n  onFilterChange();\n};\n\n/**\n * 关闭所有筛选面板\n */\nconst closeAllPanels = () => {\n  showSortPanel.value = false;\n  showLocationPanel.value = false;\n  showTimePanel.value = false;\n  showStatusPanel.value = false;\n};\n\n/**\n * 跳转到活动详情\n */\nconst goToDetail = (event) => {\n  uni.navigateTo({\n    url: `/pages_sub/pages_event/detail?id=${event.id}`\n  });\n};\n\n/**\n * 切换到列表视图 - 查看更多活动按钮点击处理\n */\nconst switchToListView = () => {\n  currentTab.value = 0; // 切换到列表视图\n  fetchEventList();\n};\n\n\n\n/**\n * 下拉刷新\n */\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  pagination.value.pageNum = 1;\n  fetchEventList();\n};\n\n/**\n * 上拉加载更多\n */\nconst onLoadMore = () => {\n  if (!pagination.value.hasMore || isLoading.value) return;\n\n  pagination.value.pageNum++;\n  fetchEventList(true);\n};\n\n// ==================== 生命周期 ====================\nonLoad(() => {\n  // 读取静态资源配置\n  const assets = uni.getStorageSync('staticAssets');\n  eventBgUrl.value = assets?.eventbg || '';\n\n  // 获取城市列表和活动列表\n  fetchEventCities();\n  fetchEventList();\n\n  // 【数据实时更新方案】监听全局数据变化事件\n  uni.$on('dataChanged', () => {\n    console.log('活动列表页收到数据变化事件，刷新列表...');\n\n    // 重新获取活动列表数据\n    fetchEventList();\n\n    uni.showToast({\n      title: '列表已更新',\n      icon: 'success',\n      duration: 1500\n    });\n  });\n});\n\n// 页面显示时隐藏原生 tabBar\nonShow(() => {\n  uni.hideTabBar();\n});\n\n// 页面卸载时移除事件监听\nonUnload(() => {\n  uni.$off('dataChanged');\n});\n\nonReachBottom(() => {\n  onLoadMore();\n});\n\nonPullDownRefresh(() => {\n  onRefresh();\n  setTimeout(() => {\n    uni.stopPullDownRefresh();\n  }, 1000);\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.event-list-page {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n\n.header-wrapper {\n  /* 修改为固定定位，滚动时不移动 */\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100; /* 确保头部在最上层 */\n  overflow: hidden;\n  min-height: calc(452rpx + var(--status-bar-height));\n  /* 新增：底部圆角，使其与下方白色区域过渡更自然 */\n  border-bottom-left-radius: 20rpx;\n  border-bottom-right-radius: 20rpx;\n  padding-bottom: 20rpx;\n}\n\n/* 背景图片样式 - 完全覆盖状态栏 */\n.header-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  /* 确保图片按比例缩放，避免变形 */\n  object-fit: cover;\n  /* 图片居中显示 */\n  object-position: center;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  position: absolute;\n  top: 94rpx;\n  left: 0;\n  right: 0;\n  width: 750rpx;\n  height: 88rpx;\n  z-index: 2;\n  border-radius: 0rpx;\n}\n\n.navbar-title {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.title-text {\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 32rpx;\n  color: #FFFFFF;\n  line-height: 44rpx;\n  text-align: center;\n  font-style: normal;\n  text-transform: none;\n}\n\n/* 确保内容在背景图片之上 */\n.top-controls,\n.filter-bar {\n  position: relative;\n  z-index: 2;\n}\n\n.top-controls {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  gap: 32rpx;\n  position: absolute;\n  top: 182rpx;\n  left: 16rpx;\n  right: 24rpx;\n  /* 确保子元素完全水平对齐 */\n  height: 60rpx; /* 调整为适应新的搜索框高度 */\n}\n\n.search-wrapper {\n  width: 446rpx;\n  height: 60rpx; /* 调整为与新的搜索框高度一致 */\n  flex-shrink: 0; /* 防止被压缩 */\n  display: flex;\n  align-items: center;\n  /* 确保与up-subsection组件基线对齐 */\n  vertical-align: middle;\n}\n\n\n.filter-bar {\n  position: relative;\n  background-color: transparent;\n  padding: 0;\n  margin-bottom: 10rpx;\n}\n\n/* 主筛选按钮容器 */\n.filter-main-buttons {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 24rpx;\n}\n\n/* 单个筛选按钮 */\n.filter-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  padding: 8rpx 16rpx;\n  background-color: transparent;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  flex: 1;\n  min-width: 120rpx; /* 设置最小宽度，确保文字有足够空间显示 */\n}\n\n.filter-text {\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n  font-weight: normal;\n  font-size: 26rpx;\n  color: #66666E;\n  line-height: 44rpx;\n  text-align: center;\n  white-space: nowrap;\n  /* 移除overflow和text-overflow属性，防止文字被截断 */\n}\n\n\n/* 箭头旋转动画 */\n.rotate-180 {\n  transform: rotate(180deg);\n  transition: transform 0.3s ease;\n}\n\n/* 筛选面板*/\n.filter-panel {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  width: 750rpx;\n  // height: 446rpx;\n  max-height: 60vh;\n  overflow-y: auto;\n  background: #FFFFFF;\n  border-radius: 0;\n  border: none;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  padding: 32rpx 24rpx;\n  margin: 0 auto;\n  max-height: 1046rpx;\n  overflow-y: auto;\n}\n\n/* 💥 新增：通用的筛选栏吸顶样式 */\n.sticky-filter-bar {\n  position: fixed;\n  /* 头部蓝色区域的高度，可根据实际情况微调 */\n  top: calc(260rpx + var(--status-bar-height));\n  left: 0;\n  right: 0;\n  z-index: 102; /* 必须比 scroll-view (101) 更高 */\n  background: #FFFFFF;\n  box-shadow: none;\n  padding-top: 0rpx;\n  padding-bottom: 0rpx;\n  border-top-left-radius: 32rpx;\n  border-top-right-radius: 32rpx;\n  overflow: visible;\n}\n\n/* 💥 新增：为带筛选栏的scroll-view添加顶部内边距 */\n.list-scroll-with-filter {\n  /* 为列表视图的筛选栏预留空间 */\n  padding-top: 56rpx !important; /* 贴近筛选栏，去除多余空白 */\n}\n\n.calendar-scroll-with-filter {\n  /* 为日历视图的筛选栏预留空间 */\n  padding-top: 56rpx !important; /* 贴近筛选栏，去除多余空白 */\n}\n\n/* 日历视图筛选栏样式 - 与列表视图保持一致 */\n.calendar-filter-bar {\n  /* 占位按钮样式 */\n  .filter-placeholder {\n    pointer-events: none; /* 禁用点击 */\n    opacity: 0; /* 隐藏但保持布局空间 */\n  }\n}\n\n\n/* 筛选分组 */\n.filter-section {\n  margin-bottom: 40rpx;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n/* 分组标题 */\n.section-title {\n  width: 104rpx;\n  height: 44rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 26rpx;\n  color: #23232A;\n  line-height: 44rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n  margin-bottom: 24rpx; /* Keeping this for spacing */\n  display: block; /* Keeping this for layout */\n}\n\n/* 选项网格 */\n.option-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10rpx; /* 进一步减少间距 */\n  justify-content: flex-start; /* 改为左对齐，避免挤压 */\n}\n\n/* 选项项 - 根据设计规格更新 */\n.option-item {\n  width: 162rpx; /* 计算后的合适宽度 */\n  height: 60rpx;\n  background: #F2F4FA; /* 未选中状态的背景色 */\n  border-radius: 8rpx 8rpx 8rpx 8rpx;\n  border: 2rpx solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &.active {\n    background: rgba(42, 97, 241, 0.2); /* 选中状态：蓝色背景 + 20%透明度 */\n    border-color: transparent;\n\n    .option-text {\n      color: #023F98; /* 选中状态的文字颜色 */\n      font-weight: normal;\n    }\n  }\n\n  &:hover {\n    opacity: 0.8;\n  }\n}\n\n.option-text {\n  width: 112rpx;\n  height: 44rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n  font-weight: normal;\n  font-size: 28rpx;\n  color: #66666E; /* 未选中状态的文字颜色 */\n  line-height: 44rpx;\n  text-align: center; /* 修改为居中对齐 */\n  font-style: normal;\n  text-transform: none;\n  white-space: nowrap;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 筛选按钮容器 */\n.filter-buttons {\n  display: flex;\n  justify-content: flex-start;\n  padding: 32rpx 0 24rpx;\n  border-top: 2rpx solid #EEEEEE; /* 添加上边框线 */\n  margin-top: 32rpx; /* 增加上边距 */\n}\n\n/* 筛选按钮基础样式 */\n.filter-btn {\n  width: 340rpx; /* 更新宽度为340rpx */\n  height: 76rpx;\n  border-radius: 8rpx 8rpx 8rpx 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-right: 24rpx;\n  border: none;\n\n  &:last-child {\n    margin-right: 0;\n  }\n}\n\n/* 重置按钮样式 */\n.reset-btn {\n  background: rgba(42, 97, 241, 0.1); /* 蓝色背景 + 10%透明度 */\n}\n\n/* 完成按钮样式 */\n.complete-btn {\n  background: #023F98; /* 深蓝色背景 */\n\n  &:hover {\n    background: #1E4FD9;\n  }\n\n  &:active {\n    background: #1A43C1;\n  }\n}\n\n/* 按钮文字基础样式 */\n.btn-text {\n  width: 56rpx;\n  height: 44rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n  font-weight: normal;\n  font-size: 28rpx;\n  line-height: 44rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n}\n\n/* 重置按钮文字样式 */\n.reset-btn .btn-text {\n  color: #23232A; /* 深灰色文字 */\n}\n\n/* 完成按钮文字样式 */\n.complete-btn .btn-text {\n  color: #FFFFFF; /* 白色文字 */\n}\n\n/* 活动列表滚动区域 - 白色内容容器 (根据蓝湖数据精确设置) */\n.event-list-scroll {\n  /* 将白色背景、圆角、外边距等样式应用到这里 */\n  background-color: #ffffff;\n  border-top-left-radius: 32rpx; /* 与筛选栏圆角一致，过渡更自然 */\n  border-top-right-radius: 32rpx;\n  margin: 0;\n  margin-top: calc(452rpx + var(--status-bar-height) - 212rpx);\n  position: relative;\n  z-index: 101;\n  padding-top: 8rpx; /* 减少顶部留白 */\n\n  flex: 1;\n  box-sizing: border-box;\n  padding-bottom: calc(220rpx + env(safe-area-inset-bottom));\n  height: calc(100vh - 452rpx - var(--status-bar-height) + 212rpx);\n}\n\n/* 日历视图滚动区域  */\n.calendar-scroll {\n  /* 将白色背景、圆角、外边距等样式应用到这里 */\n  background-color: #ffffff;\n  border-top-left-radius: 32rpx;\n  border-top-right-radius: 32rpx;\n  margin: 0;\n  margin-top: -158rpx;\n  position: relative;\n  padding-top: 24rpx;\n\n  /* 左右内边距，让卡片和筛选栏不要贴边 */\n  padding-left: 30rpx;\n  padding-right: 30rpx;\n\n  flex: 1;\n  box-sizing: border-box;\n  padding-bottom: 180rpx; /* 为底部tabBar留空 */\n}\n\n.event-card {\n  width: 100%;\n  height: 272rpx;\n  background: #FFFFFF;\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\n  // border: none;\n  // border-bottom: 1rpx solid #F5F5F5;\n  border: 2rpx solid #EEEEEE;\n  margin-bottom: 0rpx;\n  padding: 24rpx;\n  display: flex;\n  overflow: hidden;\n  box-sizing: border-box;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.card-left {\n  position: relative;\n  width: 336rpx;\n  height: 192rpx;\n  flex-shrink: 0;\n}\n\n.event-image {\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n\n.status-tag {\n  position: absolute;\n  top: 12rpx;\n  left: 12rpx;\n  width: 90rpx;\n  height: 40rpx;\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\n  border-radius: 20rpx 20rpx 20rpx 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n\n  /* 内部文字样式 */\n  color: #23232A;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 22rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n  line-height: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n\n  &.ended {\n    background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);\n  }\n}\n\n.card-right {\n  flex: 1;\n  padding: 16rpx 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.event-title {\n  width: 346rpx;\n  height: 80rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 28rpx;\n  color: #23232A;\n  text-align: justify;\n  font-style: normal;\n  text-transform: none;\n  line-height: 1.4;\n  margin-bottom: 24rpx;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  line-clamp: 2;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.event-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.event-info-row {\n  display: flex !important;\n  align-items: center !important;\n  justify-content: flex-start !important;\n  gap: 24rpx !important;\n  margin-bottom: 18rpx !important;\n  flex-wrap: nowrap !important;\n}\n\n.time-location-item {\n  display: flex !important;\n  align-items: center !important;\n  gap: 8rpx !important;\n  flex-shrink: 0 !important;\n}\n\n.event-info-icon {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  flex-shrink: 0 !important;\n}\n\n.info-text {\n  width: 176rpx !important;\n  height: 32rpx !important;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;\n  font-weight: normal !important;\n  font-size: 22rpx !important;\n  color: #9B9A9A !important;\n  text-align: left !important;\n  font-style: normal !important;\n  text-transform: none !important;\n  line-height: 32rpx !important;\n  overflow: hidden !important;\n  text-overflow: ellipsis !important;\n  white-space: nowrap !important;\n}\n\n.remaining-spots {\n  width: 154rpx;\n  height: 40rpx;\n  border-radius: 4rpx 4rpx 4rpx 4rpx;\n  border: 1rpx solid #FB8620;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n  box-sizing: border-box;\n  overflow: hidden;\n  flex-shrink: 0;\n\n  /**\n   * 剩余名额文字样式\n   */\n  .spots-count {\n    width: 100%;\n    height: 36rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 20rpx;\n    color: #FB8620;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n    line-height: 36rpx;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n}\n\n// 空状态样式\n.empty-state {\n  padding: 100rpx 0;\n  text-align: center;\n}\n\n// 重试按钮容器\n.retry-container {\n  margin-top: 40rpx;\n  display: flex;\n  justify-content: center;\n}\n\n// 加载状态优化\n.event-list-scroll {\n  :deep(.up-loadmore) {\n    padding: 30rpx 0;\n  }\n}\n\n/* 为加载更多组件的包裹容器提供上下内边距 */\n.loadmore-wrapper {\n  padding-top: 40rpx;\n  padding-bottom: 20rpx;\n}\n\n// 搜索框优化 - 自定义高度设置\n.search-wrapper {\n  :deep(.up-search) {\n    width: 446rpx;\n    height: 40rpx;\n    background: #FFFFFF;\n    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);\n    border-radius: 20rpx 20rpx 20rpx 20rpx;\n\n    .u-search__content {\n      width: 100%;\n      height: 100%;\n      background: #FFFFFF;\n      border-radius: 20rpx 20rpx 20rpx 20rpx;\n      box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);\n      display: flex;\n      align-items: center;\n    }\n\n    .u-search__content__icon {\n      color: #c0c4cc;\n    }\n\n    .u-search__content__input {\n      color: #303133;\n    }\n  }\n}\n\n// 响应式优化\n@media screen and (max-width: 750rpx) {\n  .top-controls {\n    flex-direction: column;\n    gap: 16rpx;\n\n    .search-wrapper {\n      width: 446rpx;\n      align-self: center;\n    }\n  }\n}\n\n/* up-subsection */\n:deep(.u-subsection) {\n  width: 224rpx !important;\n  height: 60rpx !important;\n  background: #FFFFFF !important;\n  border-radius: 30rpx 30rpx 30rpx 30rpx !important;\n  border: none !important;\n  box-shadow: none !important;\n  overflow: hidden !important;\n  position: relative !important; \n  display: flex !important;\n  align-items: center !important;\n  vertical-align: middle !important;\n}\n\n:deep(.u-subsection__item:not(:first-child)) {\n  border-left: none !important;\n}\n\n:deep(.u-subsection__item) {\n  border: none !important;\n  padding: 0 !important;\n  background: transparent !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  width: 50% !important;\n  position: relative !important;\n  z-index: 2 !important;\n}\n\n:deep(.u-subsection__item__text) {\n  width: 64rpx !important;\n  height: 44rpx !important;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif !important;\n  font-weight: normal !important;\n  font-size: 32rpx !important;\n  color: #23232A !important;\n  text-align: center !important;\n  font-style: normal !important;\n  text-transform: none !important;\n  white-space: nowrap !important;\n  line-height: 44rpx !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n:deep(.u-subsection__item--active .u-subsection__item__text) {\n  color: #23232A !important; \n  font-weight: normal !important; \n}\n\n/* 移动色块样式 - 确保对称居中且不被覆盖 */\n:deep(.u-subsection__bar) {\n  width: 96rpx !important;\n  height: 60rpx !important;\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%) !important;\n  border-radius: 30rpx !important;\n  transition: all 0.3s ease !important;\n  position: absolute !important;\n  top: 0 !important;\n  left: 0 !important;\n  z-index: 1 !important;\n  box-sizing: border-box !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  transform-origin: center center !important;\n}\n\n//  日历视图基础样式 \n.calendar-view {\n  background-color: #FFFFFF;\n  padding: 0;\n  padding-bottom: 0;\n  box-sizing: border-box;\n}\n\n/* 筛选面板遮罩层样式 */\n.filter-mask {\n  position: fixed;\n  top: calc(452rpx + var(--status-bar-height) - 212rpx + 56rpx);\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 101;\n}\n\n/* 搜索框样式  */\n.search-wrapper {\n  /* 强制覆盖父容器的样式限制 */\n  height: 60rpx !important; \n  display: flex !important;\n  align-items: center !important;\n  flex-shrink: 0 !important;\n}\n\n\n.search-wrapper :deep(.u-search) {\n  height: 60rpx !important; \n  width: 446rpx !important; \n  border: none !important;\n}\n\n.search-wrapper :deep(.u-search__content) {\n  height: 60rpx !important; \n  padding: 0 20rpx !important;\n  background: #FFFFFF !important; \n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05) !important;\n  border-radius: 30rpx !important;\n}\n\n.search-wrapper :deep(.u-search__input-wrap) {\n  height: 60rpx !important;\n  padding: 0 !important;\n}\n\n.search-wrapper :deep(.u-search__input) {\n  height: 60rpx !important;\n  line-height: 60rpx !important;\n  font-size: 28rpx !important; \n  color: #333333 !important; \n  background-color: transparent !important;\n}\n\n.search-wrapper :deep(.u-search__input::placeholder) {\n  color: #999999 !important; \n  font-size: 28rpx !important; \n}\n\n.search-wrapper :deep(.u-search__action) {\n  height: 60rpx !important;\n  padding: 0 8rpx !important;\n}\n\n.search-wrapper :deep(.u-search__action-text) {\n  font-size: 28rpx !important;\n  color: #333333 !important;\n}\n\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/event/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "PAGE_CONFIG", "computed", "parseDate", "getEventCitiesApi", "uni", "getCalendarEventsApi", "getEventListApi", "debounce", "onLoad", "onShow", "onUnload", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA4WA,MAAM,eAAe,MAAW;AAChC,MAAM,kBAAkB,MAAW;AACnC,MAAM,YAAY,MAAW;AAC7B,MAAM,wBAAwB,MAAW;;;;AAmBzC,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,SAAS;AAAA,MACT,UAAUC,aAAW,YAAC;AAAA,MACtB,OAAO;AAAA,MACP,SAAS;AAAA,IACX,CAAC;AAGD,UAAM,qBAAqBD,cAAAA,IAAI;AAAA,MAC7B,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,yBAAyBA,cAAAA,IAAI;AAAA,MACjC,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,sBAAsBA,cAAAA,IAAI;AAAA,MAC9B,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,oBAAoBA,cAAAA,IAAI,KAAK;AACnC,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAE/B,UAAM,oBAAoBA,kBAAI,OAAO;AAKvC,UAAM,WAAWA,cAAG,IAAC;AAAA,MAAC;AAAA,QACpB,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACE;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MAER;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAG,IAAC,EAAE,OAAO,QAAQ,OAAO,EAAC,CAAE;AAGvD,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,IAC3B,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,WAAWE,cAAQ,SAAC,MAAM;AAAA,MAC9B,gBAAgB;AAAA,MAChB,GAAG,UAAU;AAAA,MACb,GAAG,YAAY,MAAM,IAAI,CAAC,MAAM,WAAW;AAAA,QACzC,OAAO;AAAA,QACP,OAAO,MAAM;AAAA;AAAA,MACjB,EAAI;AAAA,IACJ,CAAC;AAED,UAAM,WAAWF,cAAG,IAAC;AAAA,MAAC;AAAA,QACpB,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACE;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,WAAWA,cAAG,IAAC;AAAA,MAAC;AAAA,QACpB,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACE;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBE,cAAQ,SAAC,MAAM;AACpC,UAAI,UAAU;AAAO,eAAO;AAC5B,UAAI,CAAC,WAAW,MAAM;AAAS,eAAO;AACtC,aAAO;AAAA,IACT,CAAC;AAKD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,CAAC,UAAU,SAAS,UAAU,MAAM,WAAW,GAAG;AACpD,eAAO;MACR;AACD,YAAM,SAAS,oBAAI;AACnB,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,gBAAU,MAAM,QAAQ,WAAS;AAC/B,cAAM,YAAYC,WAAAA,UAAU,MAAM,SAAS;AAE3C,cAAM,OAAO,UAAU;AACvB,cAAM,eAAc,oBAAI,KAAM,GAAC,YAAW;AAC1C,cAAM,QAAQ,OAAO,UAAU,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AAC9D,cAAM,MAAM,OAAO,UAAU,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAEvD,YAAI;AACJ,YAAI,SAAS,aAAa;AACxB,wBAAc,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAC3C,OAAW;AACL,wBAAc,GAAG,KAAK,IAAI,GAAG;AAAA,QAC9B;AAGD,cAAM,aAAa,OAAO,UAAU,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACnE,cAAM,WAAW,OAAO,UAAU,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAC5D,cAAM,UAAU,GAAG,UAAU,YAAW,CAAE,IAAI,UAAU,IAAI,QAAQ;AACpE,YAAI,CAAC,OAAO,IAAI,OAAO,GAAG;AACxB,iBAAO,IAAI,SAAS;AAAA,YAClB,MAAM;AAAA,YACN,eAAe;AAAA;AAAA,YACf,WAAW,SAAS,UAAU,QAAQ;AAAA,YACtC,QAAQ,CAAE;AAAA,UAClB,CAAO;AAAA,QACF;AACD,eAAO,IAAI,OAAO,EAAE,OAAO,KAAK,KAAK;AAAA,MACzC,CAAG;AACD,aAAO,MAAM,KAAK,OAAO,OAAQ,CAAA;AAAA,IACnC,CAAC;AAKD,UAAM,wBAAwBD,cAAQ,SAAC,MAAM;AAE3C,UAAI,cAAc;AAClB,YAAM,gBAAgB,CAAA;AAEtB,iBAAW,SAAS,cAAc,OAAO;AACvC,YAAI,eAAe;AAAI;AAEvB,cAAM,iBAAiB,KAAK;AAC5B,cAAM,eAAe,MAAM,OAAO,MAAM,GAAG,cAAc;AAEzD,sBAAc,KAAK;AAAA,UACjB,GAAG;AAAA,UACH,QAAQ;AAAA,QACd,CAAK;AAED,uBAAe,aAAa;AAAA,MAC7B;AAED,aAAO;AAAA,IACT,CAAC;AAKD,UAAM,wBAAwBA,cAAQ,SAAC,MAAM;AAC3C,YAAM,cAAc,cAAc,MAAM,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,OAAO,QAAQ,CAAC;AAC3F,aAAO,cAAc;AAAA,IACvB,CAAC;AAOD,UAAM,mBAAmB,YAAY;AACnC,UAAI;AACF,cAAM,WAAW,MAAME,eAAAA;AAGvB,YAAI,WAAW;AACf,YAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,qBAAW;AAAA,QACjB,WAAe,YAAY,MAAM,QAAQ,SAAS,IAAI,GAAG;AACnD,qBAAW,SAAS;AAAA,QAC1B,WAAe,YAAY,SAAS,SAAS,OAAO,MAAM,QAAQ,SAAS,IAAI,GAAG;AAC5E,qBAAW,SAAS;AAAA,QACrB;AAEDC,sBAAY,MAAA,MAAA,OAAA,gCAAA,YAAY,QAAQ;AAEhC,YAAI,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAEvC,gBAAM,eAAe,UAAU,MAAM,IAAI,UAAQ,KAAK,KAAK;AAC3D,gBAAM,iBAAiB,SAAS;AAAA,YAAO,UACrC,QAAQ,KAAK,UAAU,CAAC,aAAa,SAAS,KAAK,MAAM;AAAA,UACjE;AAEM,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAsEA,UAAM,mBAAmB,CAAC,aAAa,UAAU;AAC/C,YAAM,SAAS;AAAA,QACb,SAAS,aAAa,WAAW,MAAM,UAAU;AAAA,QACjD,UAAU,WAAW,MAAM;AAAA,MAC/B;AAGE,UAAI,cAAc,MAAM,QAAQ;AAC9B,eAAO,QAAQ,cAAc,MAAM,KAAI;AAAA,MACxC;AAGD,YAAM,UAAU,WAAW,UAAU,IAAI,uBAAuB,QAAQ,mBAAmB;AAG3F,UAAI,QAAQ,WAAW,GAAG;AAExB,cAAM,iBAAiB;AAAA,UACrB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACT;AAEI,YAAI,eAAe,QAAQ,QAAQ,GAAG;AAEpC,iBAAO,WAAW,eAAe,QAAQ,QAAQ;AAAA,QACvD,WAAe,QAAQ,YAAY,KAAK;AAElC,gBAAM,aAAa,QAAQ,WAAW;AACtC,cAAI,aAAa,YAAY,MAAM,QAAQ;AACzC,mBAAO,WAAW,YAAY,MAAM,UAAU;AAAA,UAC/C;AAAA,QACF;AAAA,MACF;AAGD,UAAI,WAAW,UAAU,KAAK,QAAQ,SAAS,GAAG;AAChD,cAAM,wBAAwB;AAAA,UAC5B,GAAG;AAAA;AAAA,UACH,GAAG;AAAA;AAAA,UACH,GAAG;AAAA;AAAA,QACT;AAEI,YAAI,sBAAsB,eAAe,QAAQ,MAAM,GAAG;AACxD,iBAAO,qBAAqB,sBAAsB,QAAQ,MAAM;AAAA,QACtE,OAAW;AACLA,4EAAa,eAAe,QAAQ,MAAM;AAAA,QAC3C;AAAA,MACF;AAGD,UAAI,WAAW,UAAU,GAAG;AAE1B,eAAO,UAAU;AACjB,eAAO,QAAQ;AAAA,MACnB,OAAS;AAEL,gBAAQ,QAAQ,QAAM;AAAA,UACpB,KAAK;AACH,mBAAO,UAAU;AACjB;AAAA,UACF,KAAK;AACH,mBAAO,UAAU;AACjB,mBAAO,QAAQ;AACfA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,eAAe;AAC3B;AAAA,UACF,KAAK;AACH,mBAAO,UAAU;AACjB,mBAAO,QAAQ;AACfA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,oBAAoB;AAChC;AAAA,UACF;AACE,mBAAO,UAAU;AACjB,mBAAO,QAAQ;AACfA,0BAAAA,mDAAY,YAAY;AAAA,QAC3B;AAAA,MACF;AAGD,UAAI,QAAQ,YAAY,GAAG;AACzB,cAAM,YAAY,qBAAqB,QAAQ,SAAS;AACxD,YAAI,WAAW;AACb,iBAAO,OAAO,QAAQ,SAAS;AAAA,QAChC;AAAA,MACF;AAED,aAAO;AAAA,IACT;AAOA,UAAM,uBAAuB,CAAC,mBAAmB;AAC/C,YAAM,MAAM,oBAAI;AAChB,UAAI,YAAY;AAChB,UAAI,UAAU;AAEd,cAAQ,gBAAc;AAAA,QACpB,KAAK;AACH,sBAAY;AACZ,oBAAU,IAAI,KAAK,IAAI,QAAS,IAAG,IAAI,KAAK,KAAK,KAAK,GAAI;AAC1DA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,eAAe,UAAU,mBAAkB,GAAI,KAAK,QAAQ,mBAAoB,CAAA;AAC5F;AAAA,QACF,KAAK;AACH,sBAAY;AACZ,oBAAU,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAQ,IAAK,GAAG,IAAI,QAAS,CAAA;AACvEA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,eAAe,UAAU,mBAAkB,GAAI,KAAK,QAAQ,mBAAoB,CAAA;AAC5F;AAAA,QACF,KAAK;AACH,sBAAY;AACZ,oBAAU,IAAI,KAAK,IAAI,YAAW,IAAK,GAAG,IAAI,SAAU,GAAE,IAAI,QAAS,CAAA;AACvEA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,eAAe,UAAU,mBAAkB,GAAI,KAAK,QAAQ,mBAAoB,CAAA;AAC5F;AAAA,QACF;AACEA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,YAAY;AACxB,iBAAO;AAAA,MACV;AAED,aAAO;AAAA,QACL,gBAAgB,UAAU,YAAa;AAAA,QACvC,cAAc,QAAQ,YAAa;AAAA,MACvC;AAAA,IACA;AAKA,UAAM,iBAAiB,OAAO,aAAa,UAAU;AACnD,UAAI,UAAU;AAAO;AAErB,gBAAU,QAAQ;AAElB,UAAI;AACF,cAAM,SAAS,iBAAiB,UAAU;AAC1CA,sBAAA,MAAA,MAAA,OAAA,gCAAY,SAAS,MAAM;AAG3B,YAAI;AACJ,YAAI,WAAW,UAAU,GAAG;AAE1B,qBAAW,MAAMC,oCAAqB,MAAM;AAAA,QAClD,OAAW;AAEL,qBAAW,MAAMC,+BAAgB,MAAM;AAAA,QACxC;AACD,cAAM;AAAA,UACJ,OAAO,CAAA;AAAA,UAAI,QAAQ;AAAA,QACpB,IAAG;AAEJ,YAAI,YAAY;AACd,oBAAU,MAAM,KAAK,GAAG,IAAI;AAAA,QAClC,OAAW;AACL,oBAAU,QAAQ;AAClB,qBAAW,MAAM,UAAU;AAAA,QAC5B;AAED,mBAAW,MAAM,QAAQ;AACzB,mBAAW,MAAM,UAAU,UAAU,MAAM,SAAS;AAGpD,kBAAU,QAAQ;AAElBF,sBAAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,KAAK,MAAM,aAAa,KAAK,EAAE;AAAA,MAEzD,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAGhC,YAAI,eAAe;AACnB,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,SAAS,GAAG;AACtD,yBAAe;AAAA,QACrB,WAAe,MAAM,WAAW,MAAM,QAAQ,SAAS,SAAS,GAAG;AAC7D,yBAAe;AAAA,QAChB;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,YAAI,CAAC,cAAc,UAAU,MAAM,WAAW,GAAG;AAC/C,oBAAU,QAAQ;AAAA,QACnB;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAClB,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAKA,UAAM,kBAAkBG,YAAQ,SAAC,MAAM;AACrC;IACF,GAAG,GAAG;AAMN,UAAM,0BAA0BN,cAAQ,SAAC,MAAM;AAC7C,YAAM,gBAAgB,SAAS,MAAM,KAAK,YAAU,OAAO,UAAU,mBAAmB,MAAM,MAAM;AACpG,YAAM,QAAQ,gBAAgB,cAAc,QAAQ;AACpD,aAAO;AAAA,IACT,CAAC;AAKD,UAAM,4BAA4BA,cAAQ,SAAC,MAAM;AAC/C,YAAM,gBAAgB,SAAS,MAAM,KAAK,YAAU,OAAO,UAAU,mBAAmB,MAAM,MAAM;AACpG,YAAM,QAAQ,gBAAgB,cAAc,QAAQ;AACpD,aAAO;AAAA,IACT,CAAC;AAKD,UAAM,8BAA8BA,cAAQ,SAAC,MAAM;AACjD,YAAM,gBAAgB,SAAS,MAAM,KAAK,YAAU,OAAO,UAAU,mBAAmB,MAAM,QAAQ;AACtG,aAAO,gBAAgB,cAAc,QAAQ;AAAA,IAC/C,CAAC;AAKD,UAAM,0BAA0BA,cAAQ,SAAC,MAAM;AAC7C,YAAM,gBAAgB,SAAS,MAAM,KAAK,YAAU,OAAO,UAAU,mBAAmB,MAAM,SAAS;AACvG,aAAO,gBAAgB,cAAc,QAAQ;AAAA,IAC/C,CAAC;AAKD,UAAM,kCAAkCA,cAAQ,SAAC,MAAM;AACrD,YAAM,gBAAgB,SAAS,MAAM,KAAK,YAAU,OAAO,UAAU,uBAAuB,MAAM,QAAQ;AAC1G,aAAO,gBAAgB,cAAc,QAAQ;AAAA,IAC/C,CAAC;AAKD,UAAM,8BAA8BA,cAAQ,SAAC,MAAM;AACjD,YAAM,gBAAgB,SAAS,MAAM,KAAK,YAAU,OAAO,UAAU,uBAAuB,MAAM,SAAS;AAC3G,aAAO,gBAAgB,cAAc,QAAQ;AAAA,IAC/C,CAAC;AAMD,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAEnB,gBAAU,QAAQ;AAClB,iBAAW,MAAM,UAAU;AAC3B,iBAAW,MAAM,UAAU;AAE3B;IACF;AAKA,UAAM,WAAW,CAAC,UAAU;AAC1B,oBAAc,QAAQ;AACtB;IACF;AAKA,UAAM,iBAAiB,MAAM;AAC3BG,oBAAAA,mDAAY,kBAAkB;AAE9B,gBAAU,QAAQ;AAClB,iBAAW,MAAM,UAAU;AAC3B,iBAAW,MAAM,UAAU;AAE3B;IACF;AAKA,UAAM,kBAAkB,MAAM;AAC5B,oBAAc,QAAQ,CAAC,cAAc;AAErC,wBAAkB,QAAQ;AAC1B,oBAAc,QAAQ;AACtB,sBAAgB,QAAQ;AAAA,IAC1B;AAKA,UAAM,sBAAsB,MAAM;AAChC,wBAAkB,QAAQ,CAAC,kBAAkB;AAE7C,oBAAc,QAAQ;AACtB,oBAAc,QAAQ;AACtB,sBAAgB,QAAQ;AAExB,wBAAkB,QAAQ;AAAA,IAC5B;AAKA,UAAM,kBAAkB,MAAM;AAC5B,oBAAc,QAAQ,CAAC,cAAc;AAErC,oBAAc,QAAQ;AACtB,wBAAkB,QAAQ;AAC1B,sBAAgB,QAAQ;AAExB,wBAAkB,QAAQ;AAAA,IAC5B;AAKA,UAAM,oBAAoB,MAAM;AAC9B,sBAAgB,QAAQ,CAAC,gBAAgB;AAEzC,oBAAc,QAAQ;AACtB,wBAAkB,QAAQ;AAC1B,oBAAc,QAAQ;AAAA,IAExB;AAKA,UAAM,mBAAmB,CAAC,UAAU;AAElC,sBAAgB,MAAM,SAAS;AAC/BA,oBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,KAAK;AAAA,IAE9B;AAKA,UAAM,uBAAuB,CAAC,UAAU;AACtC,UAAI,WAAW,UAAU,GAAG;AAC1B,4BAAoB,MAAM,WAAW;AAAA,MACzC,OAAS;AACL,wBAAgB,MAAM,WAAW;AAAA,MAClC;AACDA,oBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,KAAK;AAAA,IAE9B;AAKA,UAAM,mBAAmB,CAAC,UAAU;AAClC,UAAI,WAAW,UAAU,GAAG;AAC1B,4BAAoB,MAAM,YAAY;AAAA,MAC1C,OAAS;AACL,wBAAgB,MAAM,YAAY;AAAA,MACnC;AACDA,oBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,KAAK;AAAA,IAE9B;AAKA,UAAM,qBAAqB,CAAC,UAAU;AAEpC,sBAAgB,MAAM,SAAS;AAC/BA,oBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,KAAK;AAAA,IAE9B;AAKA,UAAM,kBAAkB,MAAM;AAE5B,sBAAgB,MAAM,SAAS;AAE/B,yBAAmB,MAAM,SAAS;AAClC,oBAAc,QAAQ;AACtBA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa;AACzB;IACF;AAKA,UAAM,qBAAqB,MAAM;AAE/B,yBAAmB,MAAM,SAAS,gBAAgB,MAAM;AACxD,oBAAc,QAAQ;AACtBA,0BAAY,MAAA,OAAA,iCAAA,WAAW,gBAAgB,MAAM,MAAM;AACnD;IACF;AAKA,UAAM,sBAAsB,MAAM;AAChC,UAAI,WAAW,UAAU,GAAG;AAC1B,4BAAoB,MAAM,WAAW;AACrC,+BAAuB,MAAM,WAAW;AAAA,MAC5C,OAAS;AACL,wBAAgB,MAAM,WAAW;AACjC,2BAAmB,MAAM,WAAW;AAAA,MACrC;AACD,wBAAkB,QAAQ;AAC1BA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa;AACzB;IACF;AAKA,UAAM,yBAAyB,MAAM;AACnC,UAAI,WAAW,UAAU,GAAG;AAC1B,+BAAuB,MAAM,WAAW,oBAAoB,MAAM;AAAA,MACtE,OAAS;AACL,2BAAmB,MAAM,WAAW,gBAAgB,MAAM;AAAA,MAC3D;AACD,wBAAkB,QAAQ;AAC1BA,wEAAY,WAAW,WAAW,UAAU,IAAI,oBAAoB,MAAM,WAAW,gBAAgB,MAAM,QAAQ;AACnH;AAEA,wBAAkB,QAAQ;AAAA,IAC5B;AAKA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,WAAW,UAAU,GAAG;AAC1B,4BAAoB,MAAM,YAAY;AACtC,+BAAuB,MAAM,YAAY;AAAA,MAC7C,OAAS;AACL,wBAAgB,MAAM,YAAY;AAClC,2BAAmB,MAAM,YAAY;AAAA,MACtC;AACD,oBAAc,QAAQ;AACtBA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa;AACzB;IACF;AAKA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,WAAW,UAAU,GAAG;AAC1B,+BAAuB,MAAM,YAAY,oBAAoB,MAAM;AAAA,MACvE,OAAS;AACL,2BAAmB,MAAM,YAAY,gBAAgB,MAAM;AAAA,MAC5D;AACD,oBAAc,QAAQ;AACtBA,oBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,WAAW,UAAU,IAAI,oBAAoB,MAAM,YAAY,gBAAgB,MAAM,SAAS;AACrH;AAEA,wBAAkB,QAAQ;AAAA,IAC5B;AAKA,UAAM,oBAAoB,MAAM;AAE9B,sBAAgB,MAAM,SAAS;AAC/B,yBAAmB,MAAM,SAAS;AAClC,sBAAgB,QAAQ;AACxBA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa;AACzB;IACF;AAKA,UAAM,uBAAuB,MAAM;AAEjC,yBAAmB,MAAM,SAAS,gBAAgB,MAAM;AACxD,sBAAgB,QAAQ;AACxBA,0BAAY,MAAA,OAAA,iCAAA,WAAW,gBAAgB,MAAM,MAAM;AACnD;IACF;AAKA,UAAM,iBAAiB,MAAM;AAC3B,oBAAc,QAAQ;AACtB,wBAAkB,QAAQ;AAC1B,oBAAc,QAAQ;AACtB,sBAAgB,QAAQ;AAAA,IAC1B;AAKA,UAAM,aAAa,CAAC,UAAU;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oCAAoC,MAAM,EAAE;AAAA,MACrD,CAAG;AAAA,IACH;AAKA,UAAM,mBAAmB,MAAM;AAC7B,iBAAW,QAAQ;AACnB;IACF;AAOA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,iBAAW,MAAM,UAAU;AAC3B;IACF;AAKA,UAAM,aAAa,MAAM;AACvB,UAAI,CAAC,WAAW,MAAM,WAAW,UAAU;AAAO;AAElD,iBAAW,MAAM;AACjB,qBAAe,IAAI;AAAA,IACrB;AAGAI,kBAAAA,OAAO,MAAM;AAEX,YAAM,SAASJ,cAAAA,MAAI,eAAe,cAAc;AAChD,iBAAW,SAAQ,iCAAQ,YAAW;AAGtC;AACA;AAGAA,0BAAI,IAAI,eAAe,MAAM;AAC3BA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,uBAAuB;AAGnC;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,CAAG;AAAA,IACH,CAAC;AAGDK,kBAAAA,OAAO,MAAM;AACXL,oBAAG,MAAC,WAAU;AAAA,IAChB,CAAC;AAGDM,kBAAAA,SAAS,MAAM;AACbN,0BAAI,KAAK,aAAa;AAAA,IACxB,CAAC;AAEDO,kBAAAA,cAAc,MAAM;AAClB;IACF,CAAC;AAEDC,kBAAAA,kBAAkB,MAAM;AACtB;AACA,iBAAW,MAAM;AACfR,sBAAG,MAAC,oBAAmB;AAAA,MACxB,GAAE,GAAI;AAAA,IACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7wCD,GAAG,WAAW,eAAe;"}