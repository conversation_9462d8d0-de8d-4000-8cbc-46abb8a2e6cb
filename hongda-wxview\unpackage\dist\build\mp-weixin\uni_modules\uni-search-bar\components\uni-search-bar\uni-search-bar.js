"use strict";const e=require("../../../../common/vendor.js"),t=require("./i18n/index.js"),{t:a}=e.initVueI18n(t.messages),l={name:"UniSearchBar",emits:["input","update:modelValue","clear","cancel","confirm","blur","focus"],props:{placeholder:{type:String,default:""},radius:{type:[Number,String],default:5},clearButton:{type:String,default:"auto"},cancelButton:{type:String,default:"auto"},cancelText:{type:String,default:""},bgColor:{type:String,default:"#F8F8F8"},textColor:{type:String,default:"#000000"},maxlength:{type:[Number,String],default:100},value:{type:[Number,String],default:""},modelValue:{type:[Number,String],default:""},focus:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},data:()=>({show:!1,showSync:!1,searchVal:""}),computed:{cancelTextI18n(){return this.cancelText||a("uni-search-bar.cancel")},placeholderText(){return this.placeholder||a("uni-search-bar.placeholder")}},watch:{modelValue:{immediate:!0,handler(e){this.searchVal=e,e&&(this.show=!0)}},focus:{immediate:!0,handler(e){if(e){if(this.readonly)return;this.show=!0,this.$nextTick((()=>{this.showSync=!0}))}}},searchVal(e,t){this.$emit("input",e),this.$emit("update:modelValue",e)}},methods:{searchClick(){this.readonly||this.show||(this.show=!0,this.$nextTick((()=>{this.showSync=!0})))},clear(){this.searchVal="",this.$nextTick((()=>{this.$emit("clear",{value:""})}))},cancel(){this.readonly||(this.$emit("cancel",{value:this.searchVal}),this.searchVal="",this.show=!1,this.showSync=!1,e.index.hideKeyboard())},confirm(){e.index.hideKeyboard(),this.$emit("confirm",{value:this.searchVal})},blur(){e.index.hideKeyboard(),this.$emit("blur",{value:this.searchVal})},emitFocus(e){this.$emit("focus",e.detail)}}};if(!Array){e.resolveComponent("uni-icons")()}Math;const c=e._export_sfc(l,[["render",function(t,a,l,c,r,o){return e.e({a:e.p({color:"#c0c4cc",size:"18",type:"search"}),b:r.show||r.searchVal},r.show||r.searchVal?{c:r.showSync,d:l.readonly,e:o.placeholderText,f:l.maxlength,g:l.textColor,h:e.o(((...e)=>o.confirm&&o.confirm(...e))),i:e.o(((...e)=>o.blur&&o.blur(...e))),j:e.o(((...e)=>o.emitFocus&&o.emitFocus(...e))),k:r.searchVal,l:e.o((e=>r.searchVal=e.detail.value))}:{m:e.t(l.placeholder)},{n:r.show&&("always"===l.clearButton||"auto"===l.clearButton&&""!==r.searchVal)&&!l.readonly},r.show&&("always"===l.clearButton||"auto"===l.clearButton&&""!==r.searchVal)&&!l.readonly?{o:e.p({color:"#c0c4cc",size:"20",type:"clear"}),p:e.o(((...e)=>o.clear&&o.clear(...e)))}:{},{q:l.radius+"px",r:l.bgColor,s:e.o(((...e)=>o.searchClick&&o.searchClick(...e))),t:"always"===l.cancelButton||r.show&&"auto"===l.cancelButton},"always"===l.cancelButton||r.show&&"auto"===l.cancelButton?{v:e.t(o.cancelTextI18n),w:e.o(((...e)=>o.cancel&&o.cancel(...e)))}:{})}]]);wx.createComponent(c);
