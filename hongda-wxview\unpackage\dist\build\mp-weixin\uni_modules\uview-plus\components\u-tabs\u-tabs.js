"use strict";const e=require("../../../../common/vendor.js"),t=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),s=require("../../libs/mixin/mixin.js"),n=require("../../libs/config/props.js"),r=require("../../libs/function/index.js"),a={name:"u-tabs",mixins:[i.mpMixin,s.mixin,t.props],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent="string"==typeof e?parseInt(e):e,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return e=>{const t={},i=e==this.innerCurrent?r.addStyle(this.activeStyle):r.addStyle(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),r.deepMerge(i,t)}},propsBadge:()=>n.props.badge},async mounted(){this.init(),this.windowResizeCallback=e=>{this.init()},e.index.onWindowResize(this.windowResizeCallback)},beforeUnmount(){e.index.offWindowResize(this.windowResizeCallback)},emits:["click","longPress","change","update:current"],methods:{addStyle:r.addStyle,addUnit:r.addUnit,setLineLeft(){const e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0);const i=r.getPx(this.lineWidth);this.lineOffsetLeft=t+(e.rect.width-i)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),10)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t},t),e.disabled||this.innerCurrent!=t&&(this.innerCurrent=t,this.resize(),this.$emit("update:current",t),this.$emit("change",{...e,index:t},t))},longPressHandler(e,t){this.$emit("longPress",{...e,index:t})},init(){r.sleep().then((()=>{this.resize()}))},setScrollLeft(){this.innerCurrent<0&&(this.innerCurrent=0);const e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),i=r.getWindowInfo().windowWidth;let s=t-(this.tabsRect.width-e.rect.width)/2-(i-this.tabsRect.right)/2+this.tabsRect.left/2;s=Math.min(s,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,s)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([e,t=[]])=>{e.left>e.width&&(e.right=e.right-Math.floor(e.left/e.width)*e.width,e.left=e.left%e.width),this.tabsRect=e,this.scrollViewWidth=0,t.map(((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((e=>{this.queryRect("u-tabs__wrapper__scroll-view").then((t=>e(t)))}))},getAllItemRect(){return new Promise((e=>{const t=this.list.map(((e,t)=>this.queryRect(`u-tabs__wrapper__nav__item-${t}`,!0)));Promise.all(t).then((t=>e(t)))}))},queryRect(e,t){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))}}};if(!Array){(e.resolveComponent("up-icon")+e.resolveComponent("u-badge"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-badge/u-badge.js"))();const o=e._export_sfc(a,[["render",function(t,i,s,n,r,a){return{a:e.f(t.list,((i,s,n)=>e.e(t.$slots.icon?{a:"icon-"+n,b:e.r("icon",{item:i,keyName:t.keyName,index:s},n)}:e.e({c:i.icon},i.icon?{d:"a397283f-0-"+n,e:e.p({name:i.icon,customStyle:a.addStyle(t.iconStyle)})}:{}),t.$slots.content?{f:"content-"+n,g:e.r("content",{item:i,keyName:t.keyName,index:s},n)}:t.$slots.content||!t.$slots.default&&!t.$slots.$default?{j:e.t(i[t.keyName]),k:e.n(i.disabled&&"u-tabs__wrapper__nav__item__text--disabled"),l:e.s(a.textStyle(s))}:{h:"d-"+n,i:e.r("d",{item:i,keyName:t.keyName,index:s},n)},{m:"a397283f-1-"+n,n:e.p({show:!(!i.badge||!(i.badge.show||i.badge.isDot||i.badge.value)),isDot:i.badge&&i.badge.isDot||a.propsBadge.isDot,value:i.badge&&i.badge.value||a.propsBadge.value,max:i.badge&&i.badge.max||a.propsBadge.max,type:i.badge&&i.badge.type||a.propsBadge.type,showZero:i.badge&&i.badge.showZero||a.propsBadge.showZero,bgColor:i.badge&&i.badge.bgColor||a.propsBadge.bgColor,color:i.badge&&i.badge.color||a.propsBadge.color,shape:i.badge&&i.badge.shape||a.propsBadge.shape,numberType:i.badge&&i.badge.numberType||a.propsBadge.numberType,inverted:i.badge&&i.badge.inverted||a.propsBadge.inverted,customStyle:"margin-left: 4px;"}),o:s,p:e.o((e=>a.clickHandler(i,s)),s),q:e.o((e=>a.longPressHandler(i,s)),s),r:`u-tabs__wrapper__nav__item-${s}`,s:e.n(`u-tabs__wrapper__nav__item-${s}`),t:e.n(i.disabled&&"u-tabs__wrapper__nav__item--disabled"),v:e.n(r.innerCurrent==s?"u-tabs__wrapper__nav__item-active":"")}))),b:t.$slots.icon,c:t.$slots.content,d:!t.$slots.content&&(t.$slots.default||t.$slots.$default),e:e.s(a.addStyle(t.itemStyle)),f:e.s({flex:t.scrollable?"":1}),g:e.s({width:a.addUnit(t.lineWidth),transform:`translate(${r.lineOffsetLeft}px)`,transitionDuration:`${r.firstTime?0:t.duration}ms`,height:a.addUnit(t.lineHeight),background:t.lineColor,backgroundSize:t.lineBgSize}),h:t.scrollable,i:r.scrollLeft,j:e.n(t.customClass)}}],["__scopeId","data-v-a397283f"]]);wx.createComponent(o);
