<template>
  <view class="page-container">
    <view class="custom-header">
      <view class="status-bar"></view>
      <view class="nav-bar" :style="navBarStyle">
        <u-icon name="arrow-left" size="22" color="#303133" @click="navigateBack"></u-icon>
        <view class="page-title-container">
          <text class="page-title">红大出海</text>
        </view>
      </view>

      <view class="search-section">
        <view class="search-input-wrapper">
          <u-icon name="search" color="#909399" size="18"></u-icon>
          <input
              class="search-input"
              v-model="keyword"
              placeholder="搜索活动、资讯"
              confirm-type="search"
              @confirm="handleSearch"
          />
          <u-icon v-if="keyword" @click="handleCancel" name="close-circle-fill" color="#c8c9cc" size="18"></u-icon>
        </view>
        <text v-if="keyword" class="cancel-btn" @click="handleCancel">取消</text>
      </view>
    </view>

    <view class="tabs-container">
      <view class="custom-tabs">
        <view
            v-for="(tab, index) in tabList"
            :key="index"
            class="tab-item"
            :class="{ 'active': currentTab === index }"
            @click="selectTab(index)"
            :style="currentTab === index ? activeTabStyle : {}"
        >
          {{ tab.name }}
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="result-list-scroll"  @scrolltolower="loadMoreData">
      <view v-if="isLoading" class="loading-container">
        <u-loading-icon mode="circle" text="正在搜索..." size="24"></u-loading-icon>
      </view>
      <view v-else>
        <view v-if="displayedList.length === 0" class="empty-state-container">
          <u-empty v-if="searchStatus === 'pristine'" mode="search" text="请输入关键词开始搜索"></u-empty>
          <u-empty v-else mode="data" text="暂无相关结果" marginTop="100"></u-empty>
        </view>
        <view v-else class="result-list">
          <view
              v-for="item in displayedList"
              :key="item.id"
              class="event-card"
              @click="goToDetail(item.id, currentTab)">
            <view class="card-left">
              <image class="event-image" :src="item.coverUrl" mode="aspectFill" :lazy-load="true"></image>
              <view v-if="item.statusText && currentTab === 0" :class="['status-tag', item.statusClass]">
                {{ item.statusText }}
              </view>
            </view>
            <view class="card-right">
              <text class="event-title">{{ item.title }}</text>
              <view class="event-info-row">
                <view class="time-location-item">
                  <image class="event-info-icon" :src="listTimeIconUrl" mode="aspectFit"></image>
                  <text class="info-text">{{ item.date }}</text>
                </view>
                <view class="time-location-item">
                  <image class="event-info-icon" :src="listLocationIconUrl" mode="aspectFit"></image>
                  <text class="info-text">{{ item.location }}</text>
                </view>
              </view>
              <view class="event-info remaining-spots">
                <text class="spots-count">
                  {{ item.slotsPrefix }}: {{ item.slots }}
                </text>
              </view>
            </view>
          </view>
          <u-loadmore :status="loadStatus" :line="true" marginTop="20"/>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue';
import {getArticleList} from '@/api/content/article.js';
import {searchEventsApi} from '@/api/data/event.js';
import {getFullImageUrl} from '@/utils/image.js';
import {formatEventLocation} from '@/utils/location.js';

const navBarStyle = ref({});
const keyword = ref('');
const currentTab = ref(0);
const isLoading = ref(false);
const searchStatus = ref('pristine');
const tabList = ref([{name: '活动'}, {name: '资讯'}]);
const activityResults = ref([]);
const newsResults = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const loadStatus = ref('loadmore');
const hasMoreData = ref(true);
const assets = ref({}); // 【新增】用于存储静态资源
const listTimeIconUrl = ref(''); // 【新增】时间图标URL
const listLocationIconUrl = ref(''); // 【新增】地点图标URL

// 【新增】动态计算激活Tab的背景样式
const activeTabStyle = computed(() => {
  // 优先使用后台配置的URL，如果不存在，则使用旧的地址作为备用
  const imageUrl = assets.value.bg_tab_active_search || 'http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png';
  return {
    backgroundImage: `url('${imageUrl}')`
  };
});

onMounted(() => {
  // 【新增】页面加载时，从全局缓存读取静态资源
  assets.value = uni.getStorageSync('staticAssets') || {};
  
  // 【新增】初始化图标URL
  listTimeIconUrl.value = assets.value?.list_time || '';
  listLocationIconUrl.value = assets.value?.list_location || '';

  // #ifdef MP-WEIXIN
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  const systemInfo = uni.getSystemInfoSync();
  const navHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2;
  const navPaddingRight = systemInfo.windowWidth - menuButtonInfo.left;
  navBarStyle.value = {
    height: `${navHeight}px`,
    'padding-right': `${navPaddingRight}px`,
    'align-items': 'center'
  };
  // #endif
});

const displayedList = computed(() => {
  return currentTab.value === 0 ? activityResults.value : newsResults.value;
});

/**
 * 格式化活动数据，包括状态计算和地点格式化
 * @param {Object} event - 活动对象
 * @returns {Object} 格式化后的活动数据
 */
const formatEventData = (event) => {
  // 计算报名状态
  const getRegistrationStatus = () => {
    const now = new Date();
    const startTime = event.registrationStartTime ? new Date(event.registrationStartTime) : null;
    const endTime = event.registrationEndTime ? new Date(event.registrationEndTime) : null;
    
    if (startTime && now < startTime) {
      return { text: '即将开始', class: 'not-started' };
    } else if (endTime && now > endTime) {
      return { text: '报名截止', class: 'ended' };
    } else {
      return { text: '报名中', class: 'open' };
    }
  };
  
  const status = getRegistrationStatus();
  
  return {
    id: event.id,
    title: event.title,
    date: event.startTime.split(' ')[0],
    location: formatEventLocation(event), // 使用formatEventLocation函数获取市级信息
    slots: (event.maxParticipants || 0) - (event.registeredCount || 0),
    coverUrl: getFullImageUrl(event.coverImageUrl),
    statusText: status.text,
    statusClass: status.class,
    slotsPrefix: '剩余名额'
  };
};

const formatArticleData = (article) => ({
  id: article.id, title: article.title, date: article.publishTime.split(' ')[0],
  location: article.source || '未知来源', slots: article.viewCount, coverUrl: getFullImageUrl(article.coverImageUrl),
  statusText: '热门', statusClass: 'hot', slotsPrefix: '阅读量'
});

const handleSearch = async () => {
  if (!keyword.value.trim()) {
    uni.showToast({ title: '请输入搜索内容', icon: 'none' });
    return;
  }

  pageNum.value = 1;
  activityResults.value = [];
  newsResults.value = [];
  hasMoreData.value = true;
  loadStatus.value = 'loading';
  isLoading.value = true;
  searchStatus.value = 'searched';

  try {
    const [eventRes, articleRes] = await Promise.all([
      searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value }),
      getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value })
    ]);

    if (eventRes.code === 200 && eventRes.rows) {
      activityResults.value = eventRes.rows.map(formatEventData);
      hasMoreData.value = eventRes.rows.length >= pageSize.value;
      loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';
    } else {
      activityResults.value = [];
      hasMoreData.value = false;
      loadStatus.value = 'nomore';
    }

    if (articleRes.code === 200 && articleRes.rows) {
      newsResults.value = articleRes.rows.map(formatArticleData);
    }

  } catch (error) {
    console.error('搜索失败:', error);
    uni.showToast({ title: '搜索失败，请稍后重试', icon: 'none' });
    loadStatus.value = 'loadmore';
  } finally {
    isLoading.value = false;
  }
};

const loadMoreData = async () => {
  if (loadStatus.value !== 'loadmore' || !hasMoreData.value) {
    return;
  }

  loadStatus.value = 'loading';
  pageNum.value++;

  try {
    if (currentTab.value === 0) {
      const res = await searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });
      if (res.code === 200 && res.rows) {
        activityResults.value.push(...res.rows.map(formatEventData));
        hasMoreData.value = res.rows.length >= pageSize.value;
        loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';
      }
    } else {
      const res = await getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });
      if (res.code === 200 && res.rows) {
        newsResults.value.push(...res.rows.map(formatArticleData));
        hasMoreData.value = res.rows.length >= pageSize.value;
        loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';
      }
    }
  } catch (error) {
    console.error('加载更多失败:', error);
    loadStatus.value = 'loadmore';
  }
};

const handleCancel = () => {
  keyword.value = '';
  activityResults.value = [];
  newsResults.value = [];
  searchStatus.value = 'pristine';
};

const selectTab = (index) => {
  currentTab.value = index;
  // 切换tab时，重置分页和加载状态
  pageNum.value = 1;
  hasMoreData.value = true;
  if (displayedList.value.length < pageSize.value) {
    loadStatus.value = 'nomore';
    hasMoreData.value = false;
  } else {
    loadStatus.value = 'loadmore';
  }
};

const navigateBack = () => {
  uni.navigateBack();
};

const goToDetail = (id, tabIndex) => {
  const url = tabIndex === 0
      ? `/pages_sub/pages_event/detail?id=${id}`
      : `/pages_sub/pages_article/detail?id=${id}`;
  uni.navigateTo({ url });
};
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
}

.custom-header {
  padding: 0 0 24rpx;
  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  height: 378rpx;
  justify-content: space-around;
}

.status-bar {
  height: var(--status-bar-height);
}

.nav-bar {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 32rpx;
}

.page-title-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.page-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #303133;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 0 32rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 36rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #23232A;
}

.cancel-btn {
  color: #9B9A9A;
  font-size: 28rpx;
}

.tabs-container {
  display: flex;
  justify-content: flex-start;
  padding: 20rpx 0;
  background-color: #FFFFFF;
}

.custom-tabs {
  display: flex;
  gap: 20rpx;
  padding: 0 32rpx;
}

.tab-item {
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  color: #23232A;
  font-size: 32rpx;
  font-weight: 500;
  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);
  transition: all 0.3s ease;
}

/* 活动按钮样式*/
.tab-item:first-child {
  width: 132rpx;
  height: 60rpx;
  font-size: 32rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 资讯按钮样式*/
.tab-item:last-child {
  width: 132rpx;
  height: 60rpx;
  background: rgba(42, 97, 241, 0.1);
  border-radius: 30rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #23232A;
}

.tab-item.active {
  /* 【关键修改】移除 background-image */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 激活状态下的资讯按钮 */
.tab-item:last-child.active {
  background: rgba(42, 97, 241, 1);
  color: #FFFFFF;
}

/* 激活状态下的活动按钮 */
.tab-item:first-child.active {
  color: #23232A;
}

.result-list-scroll {
  flex: 1;
  height: 0;
  background-color: #FFFFFF;
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.result-list {
  padding: 0;
}

.loading-container, .empty-state-container {
  padding-top: 200rpx;
}

.event-card {
  width: 100%;
  height: 272rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  border: none;
  border-top: 2rpx solid #EEEEEE;
  border-bottom: 2rpx solid #EEEEEE;
  margin-bottom: 0rpx;
  padding: 24rpx 24rpx;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
}

.card-left {
  position: relative;
  width: 336rpx;
  height: 192rpx;
  flex-shrink: 0;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 16rpx;
}

.status-tag {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  width: 96rpx;
  height: 44rpx;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 6rpx 12rpx;
  color: #23232A;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: 500;
  font-size: 22rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.ended {
    background: #9B9A9A;
    color: #FFFFFF;
    width: 116rpx;
  }
  
  &.not-started {
    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
    color: #23232A;
    width: 116rpx;
  }
  
  &.open {
    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
    color: #23232A;
  }
}

.card-right {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.event-title {
  width: 346rpx;
  height: 80rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.event-info-row {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 24rpx !important;
  margin-bottom: 18rpx !important;
  flex-wrap: nowrap !important;
}

.time-location-item {
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
  flex-shrink: 0 !important;
}

.event-info-icon {
  width: 32rpx !important;
  height: 32rpx !important;
  flex-shrink: 0 !important;
}

.info-text {
  width: 176rpx !important;
  height: 32rpx !important;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;
  font-weight: normal !important;
  font-size: 22rpx !important;
  color: #9B9A9A !important;
  text-align: left !important;
  font-style: normal !important;
  text-transform: none !important;
  line-height: 32rpx !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.remaining-spots {
  width: 154rpx;
  height: 40rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FB8620;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;

  .spots-count {
    width: 100%;
    height: 36rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
    font-weight: normal;
    font-size: 20rpx;
    color: #FB8620;
    text-align: center;
    font-style: normal;
    text-transform: none;
    line-height: 36rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* 状态标签样式 */
.status-tag.ended {
  width: 116rpx; /* 报名截止 */
  background-color: #ff4757;
}

.status-tag.not-started {
  width: 116rpx; /* 即将开始 */
  background-color: #ffa502;
}

.status-tag.open {
  /* 报名中 - 保持默认宽度 */
  background-color: #2ed573;
}
</style>
