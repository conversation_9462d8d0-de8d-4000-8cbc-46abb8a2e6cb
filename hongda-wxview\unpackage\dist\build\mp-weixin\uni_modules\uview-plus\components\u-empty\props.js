"use strict";const t=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),p=t.defineMixin({props:{icon:{type:String,default:()=>e.props.empty.icon},text:{type:String,default:()=>e.props.empty.text},textColor:{type:String,default:()=>e.props.empty.textColor},textSize:{type:[String,Number],default:()=>e.props.empty.textSize},iconColor:{type:String,default:()=>e.props.empty.iconColor},iconSize:{type:[String,Number],default:()=>e.props.empty.iconSize},mode:{type:String,default:()=>e.props.empty.mode},width:{type:[String,Number],default:()=>e.props.empty.width},height:{type:[String,Number],default:()=>e.props.empty.height},show:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.empty.show},marginTop:{type:[String,Number],default:()=>e.props.empty.marginTop}}});exports.props=p;
