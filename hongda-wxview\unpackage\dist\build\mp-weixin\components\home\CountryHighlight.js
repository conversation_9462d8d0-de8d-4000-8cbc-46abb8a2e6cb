"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js");if(!Array){(e.resolveComponent("uni-load-more")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const n={__name:"CountryHighlight",setup(n){const t=e.getCurrentInstance(),o=e.ref(!0),i=e.ref(!1),u=e.ref([]),c=e.ref(null),l=e.ref(null),v=e.ref("basic"),r=e.ref({}),s=e.ref(0),d=e.ref([{id:"basic",name:"基本信息",iconKey:"icon_tab_basic_normal",activeIconKey:"icon_tab_basic_active"},{id:"investment",name:"招商政策",iconKey:"icon_tab_investment_normal",activeIconKey:"icon_tab_investment_active"},{id:"customs",name:"海关政策",iconKey:"icon_tab_customs_normal",activeIconKey:"icon_tab_customs_active"},{id:"tax",name:"税务政策",iconKey:"icon_tab_tax_normal",activeIconKey:"icon_tab_tax_active"},{id:"parks",name:"工业园区",iconKey:"icon_tab_parks_normal",activeIconKey:"icon_tab_parks_active"}]),_=e.computed((()=>d.value.map((e=>({id:e.id,name:e.name,icon:r.value[e.iconKey]||"",activeIcon:r.value[e.activeIconKey]||""}))))),m=e.computed((()=>({backgroundImage:r.value.bg_badge_gold?`url('${r.value.bg_badge_gold}')`:"none"}))),b=e.computed((()=>({backgroundImage:r.value.bg_badge_blue?`url('${r.value.bg_badge_blue}')`:"none"}))),g=e.computed((()=>({backgroundImage:r.value.bg_tab_active_home?`url('${r.value.bg_tab_active_home}')`:"none"}))),p=e.computed((()=>{var e,a;return`${(null==(e=l.value)?void 0:e.nameCn)||""} - ${(null==(a=_.value.find((e=>e.id===v.value)))?void 0:a.name)||""}`})),y=e.computed((()=>{if(!l.value)return"<p>暂无相关信息。</p>";return{basic:l.value.introduction,investment:l.value.investmentPolicy,customs:l.value.customsPolicy,tax:l.value.taxPolicy,parks:"<p>请点击“更多”查看详细的工业园区列表。</p>"}[v.value]||"<p>暂无相关信息。</p>"})),f=async e=>{i.value=!0,l.value=null;try{const n=await a.getCountryDetail(e);l.value=n.data}catch(n){console.error(`获取ID为 ${e} 的国别详情失败:`,n)}finally{i.value=!1}},I=()=>{c.value&&e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${c.value}&tab=${v.value}`})};return e.onMounted((()=>{r.value=e.index.getStorageSync("staticAssets")||{},(async()=>{o.value=!0;try{const e={pageNum:1,pageSize:5},n=await a.getCountryList(e);if(n.data&&n.data.length>0){u.value=n.data;const e=n.data[0].id;c.value=e,await f(e)}}catch(e){console.error("获取推荐国别失败:",e)}finally{o.value=!1}})()})),(a,n)=>e.e({a:!o.value&&u.value.length>0},!o.value&&u.value.length>0?e.e({b:e.f(u.value,((a,n,o)=>({a:a.listCoverUrl,b:e.t(a.nameCn),c:c.value===a.id?1:"",d:e.s(c.value===a.id?m.value:b.value),e:a.id,f:"country-card-"+a.id,g:c.value===a.id?1:"",h:e.o((n=>{return o=a.id,void(c.value!==o&&(c.value=o,v.value="basic",f(o),e.nextTick$1((()=>{const a=e.index.createSelectorQuery().in(t);a.select(".country-selector-inner").boundingClientRect(),a.select(`#country-card-${o}`).boundingClientRect(),a.exec((e=>{if(e&&e[0]&&e[1]){const a=e[0],n=e[1].left-a.left;s.value=n}}))}))));var o}),a.id)}))),c:s.value,d:e.f(_.value,((a,n,t)=>({a:v.value===a.id?a.activeIcon:a.icon,b:e.t(a.name),c:a.id,d:v.value===a.id?1:"",e:e.o((e=>{return n=a.id,void(v.value=n);var n}),a.id),f:e.s(v.value===a.id?g.value:{})}))),e:i.value},i.value?{f:e.p({status:"loading"})}:l.value?{h:e.t(p.value),i:e.p({type:"right",size:"14",color:"#888"}),j:e.o(I),k:y.value}:{},{g:l.value}):{})}},t=e._export_sfc(n,[["__scopeId","data-v-302032bf"]]);wx.createComponent(t);
