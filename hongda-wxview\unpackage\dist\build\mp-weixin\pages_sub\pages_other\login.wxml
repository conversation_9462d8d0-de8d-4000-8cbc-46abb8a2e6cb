<view class="login-page data-v-b7c422fe"><image class="background-image data-v-b7c422fe" src="{{a}}" mode="aspectFill"></image><up-navbar wx:if="{{b}}" class="data-v-b7c422fe" u-i="b7c422fe-0" bind:__l="__l" u-p="{{b}}"></up-navbar><view class="content-wrapper data-v-b7c422fe"><view class="login-content data-v-b7c422fe"><view class="logo-section data-v-b7c422fe"><image class="logo-image data-v-b7c422fe" src="{{c}}" mode="aspectFit"></image></view><view class="action-section data-v-b7c422fe"><button class="login-btn data-v-b7c422fe" open-type="{{d}}" bindgetphonenumber="{{e}}" bindtap="{{f}}"> 微信手机号快捷登录 </button><view class="agreement-section data-v-b7c422fe" bindtap="{{k}}"><view class="{{['custom-checkbox', 'data-v-b7c422fe', h && 'is-checked']}}"><view wx:if="{{g}}" class="checkmark data-v-b7c422fe"></view></view><view class="agreement-text data-v-b7c422fe"><text class="data-v-b7c422fe">我已阅读并同意</text><text class="link-text data-v-b7c422fe" catchtap="{{i}}">《用户协议》</text><text class="data-v-b7c422fe">和</text><text class="link-text data-v-b7c422fe" catchtap="{{j}}">《隐私政策》</text></view></view></view></view></view></view>