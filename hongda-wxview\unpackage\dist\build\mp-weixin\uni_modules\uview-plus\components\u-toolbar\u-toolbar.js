"use strict";const e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),o=require("../../libs/mixin/mixin.js"),n=require("../../../../common/vendor.js"),t={name:"u-toolbar",mixins:[i.mpMixin,o.mixin,e.props],emits:["confirm","cancel"],created(){},methods:{cancel(){this.$emit("cancel")},confirm(){this.$emit("confirm")}}};const c=n._export_sfc(t,[["render",function(e,i,o,t,c,r){return n.e({a:e.show},e.show?n.e({b:n.t(e.cancelText),c:n.o(((...e)=>r.cancel&&r.cancel(...e))),d:e.cancelColor,e:e.title},e.title?{f:n.t(e.title)}:{},{g:!e.rightSlot},e.rightSlot?{}:{h:n.t(e.confirmText),i:n.o(((...e)=>r.confirm&&r.confirm(...e))),j:e.confirmColor},{k:n.o(((...i)=>e.noop&&e.noop(...i)))}):{})}],["__scopeId","data-v-0a2da91a"]]);wx.createComponent(c);
