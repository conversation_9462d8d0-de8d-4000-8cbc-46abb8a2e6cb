/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-1e6eaa19 {
  height: 100%;
  background-color: #f4f4f4;
}

/* 页面总容器 */
.page-container.data-v-1e6eaa19 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: transparent;
}

/* 顶部背景区域 */
.header-background.data-v-1e6eaa19 {
  height: 420rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 10;
}

/* 页面标题容器 */
.page-title-wrapper.data-v-1e6eaa19 {
  position: absolute;
  top: 94rpx;
  left: 0;
  width: 750rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page-title.data-v-1e6eaa19 {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}

/* 搜索栏包裹容器 */
.search-bar-wrapper.data-v-1e6eaa19 {
  position: absolute;
  top: 182rpx;
  left: 0;
  width: 750rpx;
  height: 88rpx;
  box-sizing: border-box;
}
.data-v-1e6eaa19 .uni-search-bar {
  margin: 14rpx 16rpx 14rpx 32rpx;
  height: calc(100% - 28rpx);
  padding: 0 !important;
}
.data-v-1e6eaa19 .uni-search-bar__box {
  height: 100% !important;
  justify-content: flex-start !important;
}
.data-v-1e6eaa19 .uni-search-bar__text-placeholder {
  font-size: 28rpx !important;
  color: #9B9A9A !important;
}
.data-v-1e6eaa19 .uni-searchbar__cancel {
  font-size: 30rpx !important;
  color: #FFFFFF !important;
}

/* Tab容器和指示器 */
.continent-tabs-container.data-v-1e6eaa19 {
  position: absolute;
  top: 320rpx;
  transform: translateY(-50%);
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-top: 10rpx;
}
.continent-tabs.data-v-1e6eaa19 {
  white-space: nowrap;
}
.continent-tabs.data-v-1e6eaa19::-webkit-scrollbar {
  display: none;
}
.continent-tabs.data-v-1e6eaa19::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
.tab-item.data-v-1e6eaa19 {
  display: inline-block;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #23232A;
  border-radius: 30rpx;
  background-color: #FFFFFF;
  transition: all 0.3s;
  background-size: cover;
  background-position: center;
  margin-right: 20rpx;
}
.tab-item.data-v-1e6eaa19:last-child {
  margin-right: 0;
}
.tab-item.active.data-v-1e6eaa19 {
  color: #23232A;
  font-weight: bold;
}

/* 指示器样式优化 */
.active-indicator.data-v-1e6eaa19 {
  position: absolute;
  bottom: -32rpx;
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 25rpx solid #fff;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  z-index: 10;
  opacity: 0;
  /* 初始隐藏 */
}

/* 国别列表包裹容器 */
.country-list-wrapper.data-v-1e6eaa19 {
  flex: 1;
  height: 0;
  background-color: #ffffff;
  position: relative;
  z-index: 15;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: -32rpx;
  box-sizing: border-box;
  overflow: hidden;
}

/* 国别卡片 */
.country-card.data-v-1e6eaa19 {
  display: flex;
  align-items: center;
  width: 100%;
  height: 272rpx;
  padding: 0 30rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 卡片左侧封面图 */
.country-image.data-v-1e6eaa19 {
  flex-shrink: 0;
  width: 336rpx;
  height: 192rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

/* 卡片右侧信息布局 */
.country-info.data-v-1e6eaa19 {
  flex: 1;
  min-width: 0;
  height: 192rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 顶部信息块 */
.info-top.data-v-1e6eaa19 {
  display: flex;
  flex-direction: column;
}

/* 国名行 */
.name-line.data-v-1e6eaa19 {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}
.name-line .name-cn.data-v-1e6eaa19 {
  font-size: 28rpx;
  font-weight: bold;
  color: #23232A;
}
.name-line .name-en.data-v-1e6eaa19 {
  font-size: 22rpx;
  margin-left: 12rpx;
  color: #9B9A9A;
}

/* 简介 */
.summary.data-v-1e6eaa19 {
  font-size: 24rpx;
  color: #23232A;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* 国旗 */
.country-flag.data-v-1e6eaa19 {
  width: 60rpx;
  height: 40rpx;
  border-radius: 4rpx;
  border: 1rpx solid #eee;
  align-self: flex-start;
}

/* 状态提示 */
.status-tip.data-v-1e6eaa19, .empty-message-container.data-v-1e6eaa19 {
  padding: 80rpx 0;
  text-align: center;
}
.empty-text.data-v-1e6eaa19 {
  font-size: 28rpx;
  color: #999;
}
.scroll-view-bottom-spacer.data-v-1e6eaa19 {
  height: 180rpx;
}