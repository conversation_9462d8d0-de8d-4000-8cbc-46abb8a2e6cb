/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-46cca27f {
  height: 100%;
  background-color: #f4f4f4;
}

/* --- 页面布局及自定义导航栏样式 --- */
.page-container.data-v-46cca27f {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFFFFF;
}
.fixed-header.data-v-46cca27f {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}
.scrollable-content.data-v-46cca27f {
  flex: 1;
  height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.status-bar.data-v-46cca27f {
  width: 100%;
}
.custom-nav-bar.data-v-46cca27f {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box;
}
.nav-back-button.data-v-46cca27f {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.nav-title.data-v-46cca27f {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
}
.loading-container.data-v-46cca27f {
  padding-top: 200rpx;
}
.cover-section.data-v-46cca27f {
  position: relative;
  height: 400rpx;
}
.cover-section .cover-image.data-v-46cca27f {
  width: 100%;
  height: 100%;
}
.cover-section .cover-overlay.data-v-46cca27f {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  color: #fff;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
}
.cover-section .cover-overlay .country-name-cn.data-v-46cca27f {
  font-size: 48rpx;
  font-weight: bold;
}
.cover-section .cover-overlay .country-name-en.data-v-46cca27f {
  font-size: 32rpx;
  opacity: 0.9;
}
.tabs-wrapper.data-v-46cca27f {
  margin: 30rpx 30rpx 0;
  background-color: #F4F4F4;
  border-radius: 16rpx;
  padding: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tabs.data-v-46cca27f {
  display: flex;
  white-space: nowrap;
}
.tabs.data-v-46cca27f::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
.tab-item.data-v-46cca27f {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
}
.tab-item.data-v-46cca27f:last-child {
  margin-right: 0;
}
.tab-item .tab-icon.data-v-46cca27f {
  width: 40rpx;
  height: 40rpx;
}
.tab-item .tab-text.data-v-46cca27f {
  font-size: 28rpx;
  color: #9B9A9A;
}
.tab-item.active.data-v-46cca27f {
  background-size: cover;
  background-position: center;
}
.tab-item.active .tab-text.data-v-46cca27f {
  color: #23232A;
  font-weight: bold;
}
.content-wrapper.data-v-46cca27f {
  padding: 24rpx;
}
.section-card.data-v-46cca27f {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
}
.section-title.data-v-46cca27f {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding-bottom: 15rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.plain-text-content.data-v-46cca27f {
  font-size: 28rpx;
  line-height: 1.7;
  white-space: pre-wrap;
}
.introduction-text.data-v-46cca27f {
  color: #66666E;
}
.policy-text.data-v-46cca27f {
  color: #23232A;
}
.basic-info-card.data-v-46cca27f {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 20rpx;
}
.info-grid-container.data-v-46cca27f {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.info-row.data-v-46cca27f {
  display: flex;
  width: 100%;
}
.info-column.data-v-46cca27f {
  flex: 1;
  width: 50%;
  padding-right: 20rpx;
  box-sizing: border-box;
}
.key-row .info-column.data-v-46cca27f {
  font-size: 24rpx;
  color: #66666E;
}
.value-row.data-v-46cca27f {
  margin-top: 8rpx;
}
.value-row .info-column.data-v-46cca27f {
  font-size: 28rpx;
  color: #23232A;
  font-weight: 500;
}
.policy-title-header.data-v-46cca27f {
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
  background: linear-gradient(to bottom, #CEDEF5, #F0F2F3) center;
  border-bottom: 2rpx solid #DCDFE6;
}
.policy-title-header .policy-title.data-v-46cca27f {
  font-size: 32rpx;
  font-weight: bold;
  color: #023F98;
}
.policy-main-content.data-v-46cca27f {
  background-color: #F2F4FA;
  border-radius: 0 0 16rpx 16rpx;
  padding: 30rpx;
}
.policy-intro-wrapper.data-v-46cca27f {
  margin-bottom: 30rpx;
}
.park-card.data-v-46cca27f {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  overflow: hidden;
  /* [修改] 增加相对定位，作为序号标记的定位父级 */
  position: relative;
}
.park-card.data-v-46cca27f:active {
  transform: scale(0.98);
}
.park-card .park-card-image-wrapper.data-v-46cca27f {
  width: 100%;
  height: 240rpx;
  background-color: #f0f2f5;
}
.park-card .park-card-content.data-v-46cca27f {
  padding: 30rpx;
}
.park-card .park-name.data-v-46cca27f {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}
.park-card .park-info-item.data-v-46cca27f {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.park-card .park-info-item.data-v-46cca27f:last-child {
  margin-bottom: 0;
}
.park-card .park-info-icon.data-v-46cca27f {
  width: 26rpx;
  height: 26rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* [新增] 园区卡片左上角序号标记的样式 */
.park-number-badge.data-v-46cca27f {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 2;
  /* 确保在图片上方 */
  width: 48rpx;
  height: 48rpx;
  background-color: #FFBF51;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #23232A;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  /* 增加一点阴影使其更突出 */
}