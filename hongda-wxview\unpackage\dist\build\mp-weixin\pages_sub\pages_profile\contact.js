"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),l=require("../../utils/image.js"),u=require("./api/platform/consultant.js");if(!Array){(e.resolveComponent("uni-icons")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js"))();const o={__name:"contact",setup(o){const t=e.index.upx2px(20),n=e.ref(0),r=e.ref(0),i=e.ref(0),s=()=>{e.index.navigateBack({delta:1})},v=e.ref(!0),c=e.ref(null),d=e.computed((()=>c.value?l.getFullImageUrl(c.value.avatarUrl):"")),m=e.computed((()=>c.value?l.getFullImageUrl(c.value.qrCodeUrl):"")),p=()=>{m.value&&e.index.previewImage({urls:[m.value]})},g=e=>{console.error(`${e} image failed to load.`),"avatar"===e?c.value.avatarUrl="/static/images/default-avatar.png":"qrCode"===e&&(c.value.qrCodeUrl="/static/images/default-qrcode.png")};return e.onLoad((()=>{(()=>{try{const a=e.index.getMenuButtonBoundingClientRect();n.value=a.top,r.value=a.height,i.value=a.bottom+t}catch(a){const l=e.index.getSystemInfoSync();n.value=l.statusBarHeight||20,r.value=44,i.value=n.value+r.value+t}})(),v.value=!0,(async()=>{try{const e=await u.getDisplayConsultantApi();200===e.code&&e.data?c.value=e.data:c.value=null}catch(e){console.error("获取顾问信息失败:",e),c.value=null}finally{v.value=!1}})()})),(l,u)=>e.e({a:n.value+"px",b:e.p({type:"left",color:"#000000",size:"22"}),c:e.o(s),d:r.value+"px",e:e.unref(t)+"px",f:i.value+"px",g:v.value},v.value?{h:e.p({status:"loading","show-icon":!0})}:{},{i:!v.value&&c.value},!v.value&&c.value?{j:d.value,k:e.o((e=>g("avatar"))),l:e.t(c.value.name),m:e.t(c.value.introduction),n:m.value,o:e.o((e=>g("qrCode"))),p:e.o(p)}:{},{q:!v.value&&!c.value},v.value||c.value?{}:{r:a._imports_0},{s:i.value+"px"})}},t=e._export_sfc(o,[["__scopeId","data-v-ae6ce6ee"]]);wx.createPage(t);
