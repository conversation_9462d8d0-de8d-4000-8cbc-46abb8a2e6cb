<view wx:if="{{a}}" class="highlight-container data-v-302032bf"><view class="section-header data-v-302032bf"><text class="title-main data-v-302032bf">出海</text><text class="title-gradient data-v-302032bf">国别</text></view><scroll-view class="country-selector-scroll data-v-302032bf" scroll-x show-scrollbar="{{false}}" scroll-left="{{c}}" scroll-with-animation="{{true}}"><view class="country-selector-inner data-v-302032bf"><view wx:for="{{b}}" wx:for-item="country" wx:key="e" id="{{country.f}}" class="{{['country-card', 'data-v-302032bf', country.g && 'active']}}" bindtap="{{country.h}}"><image class="country-card-bg data-v-302032bf" src="{{country.a}}" mode="aspectFill"></image><view class="{{['corner-badge', 'data-v-302032bf', country.c && 'is-gold']}}" style="{{country.d}}"><text class="badge-text data-v-302032bf">{{country.b}}</text></view></view></view></scroll-view><view class="tabs-wrapper data-v-302032bf"><scroll-view class="tabs data-v-302032bf" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{d}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', 'data-v-302032bf', tab.d && 'active']}}" bindtap="{{tab.e}}" style="{{tab.f}}"><image class="tab-icon data-v-302032bf" src="{{tab.a}}"></image><text class="tab-text data-v-302032bf">{{tab.b}}</text></view></scroll-view></view><view wx:if="{{e}}" class="content-loading data-v-302032bf"><uni-load-more wx:if="{{f}}" class="data-v-302032bf" u-i="302032bf-0" bind:__l="__l" u-p="{{f}}"/></view><view wx:elif="{{g}}" class="content-display-area data-v-302032bf"><view class="content-header data-v-302032bf"><text class="content-title data-v-302032bf">{{h}}</text><view class="more-link data-v-302032bf" bindtap="{{j}}"><text class="data-v-302032bf">更多</text><uni-icons wx:if="{{i}}" class="data-v-302032bf" u-i="302032bf-1" bind:__l="__l" u-p="{{i}}"></uni-icons></view></view><view class="summary-content data-v-302032bf"><rich-text class="data-v-302032bf" nodes="{{k}}"></rich-text></view></view></view>