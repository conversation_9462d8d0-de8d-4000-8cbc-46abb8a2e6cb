/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-7a6d2e7b {
  height: 100%;
  background-color: #f4f4f4;
}
.header-container.data-v-7a6d2e7b {
  width: 100%;
  background-color: #023f98;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
}
.header-content.data-v-7a6d2e7b {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  /* 这里的 padding-left/right 需要与 JS 中的 HEADER_HORIZONTAL_PADDING 常量值保持一致 */
  padding: 0 24rpx;
  box-sizing: border-box;
}
.search-bar-wrapper.data-v-7a6d2e7b {
  background-color: #ffffff;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
}
.search-bar-wrapper.data-v-7a6d2e7b:active {
  opacity: 0.8;
}
.search-placeholder.data-v-7a6d2e7b {
  font-size: 28rpx;
  color: #9b9a9a;
}

/* [新增] 自定义搜索图标的样式 */
.search-icon.data-v-7a6d2e7b {
  width: 25rpx;
  height: 25rpx;
  flex-shrink: 0;
}