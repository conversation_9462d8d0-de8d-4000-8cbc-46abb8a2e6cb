"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),n=require("../../libs/mixin/mixin.js"),i=require("../../libs/function/index.js"),s=require("../../libs/function/test.js"),l=require("../../../../common/vendor.js"),o={name:"u-picker",mixins:[t.mpMixin,n.mixin,e.props],data:()=>({lastIndex:[],innerIndex:[],innerColumns:[],columnIndex:0,showByClickInput:!1,currentActiveValue:[]}),watch:{columns:{immediate:!0,deep:!0,handler(e){this.setColumns(e)}},defaultIndex:{immediate:!0,deep:!0,handler(e,t){t&&e.join("/")==t.join("/")||this.setIndexs(e,!0)}},modelValue:{immediate:!0,deep:!0,handler(e,t){if(!t||e.join("/")!=t.join("/")){let t=[];null!=e&&(e.forEach(((e,n)=>{let i=this.getColumnValues(n);i&&"[object Object]"===Object.prototype.toString.call(i)?i.forEach(((n,i)=>{n[this.keyName]==e&&t.push(i)})):i.forEach(((n,i)=>{n==e&&t.push(i)}))})),0==t.length&&this.defaultIndex||this.setIndexs(t,!0))}}}},emits:["close","cancel","confirm","change","update:modelValue","update:show"],computed:{inputPropsInner(){return{border:this.inputBorder,placeholder:this.placeholder,disabled:this.disabled,disabledColor:this.disabledColor,...this.inputProps}},inputLabel(){let e=this.innerColumns[0]&&this.innerColumns[0][0];if(e&&"[object Object]"===Object.prototype.toString.call(e)){let e=this.innerColumns[0].filter((e=>this.modelValue.includes(e.id)));return e=e.map((e=>e[this.keyName])),e.join("/")}return this.modelValue.join("/")},inputValue(){let e=this.innerColumns.map(((e,t)=>e[this.innerIndex[t]])),t=[];return e[0]&&"[object Object]"===Object.prototype.toString.call(e[0])?e.forEach((e=>{t.push(e&&e[this.valueName])})):e.forEach(((e,n)=>{t.push(e)})),t}},methods:{addUnit:i.addUnit,testArray:s.test.array,onShowByClickInput(){this.disabled||(this.showByClickInput=!this.showByClickInput)},getItemText(e){return s.test.object(e)?e[this.keyName]:e},closeHandler(){this.closeOnClickOverlay&&(this.hasInput&&(this.showByClickInput=!1),this.setDefault(),this.$emit("update:show",!1),this.$emit("close"))},cancel(){this.hasInput&&(this.showByClickInput=!1),this.setDefault(),this.$emit("update:show",!1),this.$emit("cancel")},setDefault(){let e=[0];e=0==this.lastIndex.length?Array.isArray(this.defaultIndex)&&this.defaultIndex.length==this.innerColumns.length?[...this.defaultIndex]:Array(this.innerColumns.length).fill(0):i.deepClone(this.lastIndex),this.setLastIndex(e),this.setIndexs(e)},confirm(){this.currentActiveValue.length||this.setDefault(),this.$emit("update:modelValue",this.inputValue),this.hasInput&&(this.showByClickInput=!1),this.setLastIndex(this.innerIndex),this.$emit("update:show",!1),this.$emit("confirm",{indexs:this.innerIndex,value:this.innerColumns.map(((e,t)=>e[this.innerIndex[t]])),values:this.innerColumns})},changeHandler(e){const{value:t}=e.detail;let n=0,i=0;this.currentActiveValue=t;for(let l=0;l<t.length;l++){let e=t[l];if(e!==(this.lastIndex[l]||0)){i=l,n=e;break}}this.columnIndex=i;const s=this.innerColumns;this.setIndexs(t),this.$emit("change",{value:this.innerColumns.map(((e,n)=>e[t[n]])),index:n,indexs:t,values:s,columnIndex:i})},setIndexs(e,t){this.innerIndex=i.deepClone(e),t&&this.setLastIndex(e)},setLastIndex(e){this.lastIndex=i.deepClone(e)},setColumnValues(e,t){this.innerColumns.splice(e,1,t),this.setLastIndex(this.innerIndex.slice(0,e));let n=i.deepClone(this.innerIndex);for(let i=0;i<this.innerColumns.length;i++)i>this.columnIndex&&(n[i]=0);this.setIndexs(n)},getColumnValues(e){return(async()=>{await i.sleep()})(),this.innerColumns[e]},setColumns(e){this.innerColumns=i.deepClone(e),0===this.innerIndex.length&&(this.innerIndex=new Array(e.length).fill(0))},getIndexs(){return this.innerIndex},getValues(){return(async()=>{await i.sleep()})(),this.innerColumns.map(((e,t)=>e[this.innerIndex[t]]))}}};if(!Array){(l.resolveComponent("up-input")+l.resolveComponent("u-toolbar")+l.resolveComponent("u-loading-icon")+l.resolveComponent("u-popup"))()}Math||((()=>"../u-input/u-input.js")+(()=>"../u-toolbar/u-toolbar.js")+(()=>"../u-loading-icon/u-loading-icon.js")+(()=>"../u-popup/u-popup.js"))();const r=l._export_sfc(o,[["render",function(e,t,n,i,s,o){return l.e({a:e.hasInput},e.hasInput?l.e({b:l.r("d",{value:o.inputLabel}),c:l.r("trigger",{value:o.inputLabel}),d:!e.$slots.default&&!e.$slots.$default&&!e.$slots.trigger},e.$slots.default||e.$slots.$default||e.$slots.trigger?{}:{e:l.o((e=>o.inputLabel=e)),f:l.p({readonly:!0,...o.inputPropsInner,modelValue:o.inputLabel})},{g:l.o(((...e)=>o.onShowByClickInput&&o.onShowByClickInput(...e)))}):{},{h:e.showToolbar},e.showToolbar?{i:l.o(o.cancel),j:l.o(o.confirm),k:l.p({cancelColor:e.cancelColor,confirmColor:e.confirmColor,cancelText:e.cancelText,confirmText:e.confirmText,title:e.title,rightSlot:!!e.toolbarRightSlot})}:{},{l:l.f(s.innerColumns,((t,n,i)=>l.e({a:o.testArray(t)},o.testArray(t)?{b:l.f(t,((e,t,i)=>({a:l.t(o.getItemText(e)),b:l.n(t===s.innerIndex[n]&&"u-picker__view__column__item--selected"),c:t,d:t===s.innerIndex[n]?"bold":"normal"}))),c:o.addUnit(e.itemHeight,"px"),d:o.addUnit(e.itemHeight,"px")}:{},{e:n}))),m:`height: ${o.addUnit(e.itemHeight,"px")}`,n:s.innerIndex,o:e.immediateChange,p:`${o.addUnit(e.visibleItemCount*e.itemHeight,"px")}`,q:l.o(((...e)=>o.changeHandler&&o.changeHandler(...e))),r:e.loading},e.loading?{s:l.p({mode:"circle"})}:{},{t:l.o(o.closeHandler),v:l.p({show:e.show||e.hasInput&&s.showByClickInput,mode:e.popupMode,zIndex:e.zIndex,bgColor:e.bgColor,round:e.round,duration:e.duration,overlayOpacity:e.overlayOpacity})})}],["__scopeId","data-v-f9d85f99"]]);wx.createComponent(r);
