"use strict";const i=require("../../../../common/vendor.js"),e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),n=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),o={name:"u-link",mixins:[t.mpMixin,n.mixin,e.props],computed:{linkStyle(){return{color:this.color,fontSize:s.addUnit(this.fontSize),lineHeight:s.addUnit(s.getPx(this.fontSize)+2),textDecoration:this.underLine?"underline":"none"}}},emits:["click"],methods:{addStyle:s.addStyle,openLink(){i.index.setClipboardData({data:this.href,success:()=>{i.index.hideToast(),this.$nextTick((()=>{s.toast(this.mpTips)}))}}),this.$emit("click")}}};const r=i._export_sfc(o,[["render",function(e,t,n,s,o,r){return{a:i.t(e.text),b:i.o(((...i)=>r.openLink&&r.openLink(...i))),c:i.s(r.linkStyle),d:i.s(r.addStyle(e.customStyle))}}],["__scopeId","data-v-05a279e1"]]);wx.createComponent(r);
