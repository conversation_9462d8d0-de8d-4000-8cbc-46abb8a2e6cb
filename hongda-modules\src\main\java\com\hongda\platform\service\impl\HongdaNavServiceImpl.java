package com.hongda.platform.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.content.domain.*;
import com.hongda.content.service.*;
import com.hongda.platform.domain.HongdaNav;
import com.hongda.platform.domain.HongdaPage;
import com.hongda.platform.mapper.HongdaNavMapper;
import com.hongda.platform.service.IHongdaNavService;
import com.hongda.platform.service.IHongdaPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 导航配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class HongdaNavServiceImpl implements IHongdaNavService
{
    @Autowired
    private HongdaNavMapper hongdaNavMapper;

    // ==================【⭐ 新增代码开始 ⭐】==================
    // 注入所有需要用到的Service
    @Autowired
    private IHongdaArticleService articleService;
    @Autowired
    private IHongdaEventService eventService;
    @Autowired
    private IHongdaCountryService countryService;
    @Autowired
    private IHongdaParkService parkService;
    @Autowired
    private IHongdaPageService pageService;
    @Autowired
    private IHongdaCountryPolicyArticleService countryPolicyArticleService;
    // ==================【⭐ 新增代码结束 ⭐】==================

    /**
     * 查询导航配置
     *
     * @param id 导航配置主键
     * @return 导航配置
     */
    @Override
    public HongdaNav selectHongdaNavById(Long id)
    {
        return hongdaNavMapper.selectHongdaNavById(id);
    }

    /**
     * 查询导航配置列表
     *
     * @param hongdaNav 导航配置
     * @return 导航配置
     */
    @Override
    public List<HongdaNav> selectHongdaNavList(HongdaNav hongdaNav)
    {
        // ==================【⭐ 关键修改开始 ⭐】==================
        // 1. 从数据库获取原始列表
        List<HongdaNav> list = hongdaNavMapper.selectHongdaNavList(hongdaNav);

        // 2. 遍历列表，根据ID填充标题
        for (HongdaNav nav : list) {
            String linkType = nav.getLinkType();
            String linkTarget = nav.getLinkTarget();

            if (StringUtils.isNoneBlank(linkType, linkTarget) && !"#".equals(linkTarget)) {
                try {
                    switch (linkType) {
                        case "ARTICLE":
                            HongdaArticle article = articleService.selectHongdaArticleById(Long.parseLong(linkTarget));
                            if (article != null) nav.setLinkTargetTitle(article.getTitle());
                            break;
                        case "EVENT":
                            HongdaEvent event = eventService.selectHongdaEventById(Long.parseLong(linkTarget));
                            if (event != null) nav.setLinkTargetTitle(event.getTitle());
                            break;
                        case "COUNTRY":
                            HongdaCountry country = countryService.selectHongdaCountryById(Long.parseLong(linkTarget));
                            if (country != null) nav.setLinkTargetTitle(country.getNameCn());
                            break;
                        case "PARK":
                            HongdaPark park = parkService.selectHongdaParkById(Long.parseLong(linkTarget));
                            if (park != null) nav.setLinkTargetTitle(park.getName());
                            break;
                        case "CUSTOM_PAGE":
                            HongdaPage page = pageService.selectHongdaPageById(Long.parseLong(linkTarget));
                            if (page != null) nav.setLinkTargetTitle(page.getTitle());
                            break;
                        case "COUNTRY_POLICY":
                            HongdaCountryPolicyArticle policy = countryPolicyArticleService.selectHongdaCountryPolicyArticleByArticleId(Long.parseLong(linkTarget));
                            if (policy != null) nav.setLinkTargetTitle(policy.getTitle());
                            break;
                    }
                } catch (NumberFormatException e) {
                    // 如果ID不是数字格式，则忽略，避免程序崩溃
                }
            }
        }
        // 3. 返回填充好标题的列表
        return list;
        // ==================【⭐ 关键修改结束 ⭐】==================
    }

    /**
     * 新增导航配置
     *
     * @param hongdaNav 导航配置
     * @return 结果
     */
    @Override
    public int insertHongdaNav(HongdaNav hongdaNav)
    {
        hongdaNav.setCreateTime(DateUtils.getNowDate());
        return hongdaNavMapper.insertHongdaNav(hongdaNav);
    }

    /**
     * 修改导航配置
     *
     * @param hongdaNav 导航配置
     * @return 结果
     */
    @Override
    public int updateHongdaNav(HongdaNav hongdaNav)
    {
        hongdaNav.setUpdateTime(DateUtils.getNowDate());
        return hongdaNavMapper.updateHongdaNav(hongdaNav);
    }

    /**
     * 批量删除导航配置
     *
     * @param ids 需要删除的导航配置主键
     * @return 结果
     */
    @Override
    public int deleteHongdaNavByIds(Long[] ids)
    {
        return hongdaNavMapper.deleteHongdaNavByIds(ids);
    }

    /**
     * 删除导航配置信息
     *
     * @param id 导航配置主键
     * @return 结果
     */
    @Override
    public int deleteHongdaNavById(Long id)
    {
        return hongdaNavMapper.deleteHongdaNavById(id);
    }
}