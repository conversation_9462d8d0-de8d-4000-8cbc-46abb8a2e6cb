"use strict";const t=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),r=t.defineMixin({props:{safeAreaInsetTop:{type:Boolean,default:()=>e.props.navbar.safeAreaInsetTop},placeholder:{type:Boolean,default:()=>e.props.navbar.placeholder},fixed:{type:Boolean,default:()=>e.props.navbar.fixed},border:{type:Boolean,default:()=>e.props.navbar.border},leftIcon:{type:String,default:()=>e.props.navbar.leftIcon},leftText:{type:String,default:()=>e.props.navbar.leftText},rightText:{type:String,default:()=>e.props.navbar.rightText},rightIcon:{type:String,default:()=>e.props.navbar.rightIcon},title:{type:[String,Number],default:()=>e.props.navbar.title},titleColor:{type:String,default:()=>e.props.navbar.titleColor},bgColor:{type:String,default:()=>e.props.navbar.bgColor},statusBarBgColor:{type:String,default:()=>""},titleWidth:{type:[String,Number],default:()=>e.props.navbar.titleWidth},height:{type:[String,Number],default:()=>e.props.navbar.height},leftIconSize:{type:[String,Number],default:()=>e.props.navbar.leftIconSize},leftIconColor:{type:String,default:()=>e.props.navbar.leftIconColor},autoBack:{type:Boolean,default:()=>e.props.navbar.autoBack},titleStyle:{type:[String,Object],default:()=>e.props.navbar.titleStyle}}});exports.props=r;
