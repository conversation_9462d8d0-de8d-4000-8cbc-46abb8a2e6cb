/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-08cb6188 {
  height: 100%;
  background-color: #f4f4f4;
}
.info-card.data-v-08cb6188 {
  background: #FFFFFF;
  margin: 0 30rpx;
  margin-top: -60rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.1);
  padding: 30rpx;
  position: relative;
  z-index: 1;
}
.info-icon.data-v-08cb6188 {
  width: 32rpx;
  height: 32rpx;
}
.card-header.data-v-08cb6188 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.status-tag-detail.data-v-08cb6188 {
  width: 90rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  font-size: 22rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.status-tag-detail.ended.data-v-08cb6188 {
  background: #9B9A9A;
  color: #FFFFFF;
  width: 110rpx;
}
.status-tag-detail.ended .status-bg-image.data-v-08cb6188 {
  display: none;
}
.status-tag-detail.not-started.data-v-08cb6188 {
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  color: #23232A;
  width: 110rpx;
}
.status-tag-detail.not-started .status-bg-image.data-v-08cb6188 {
  display: none;
}
.status-tag-detail.open.data-v-08cb6188 {
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  color: #23232A;
}
.status-tag-detail.open .status-bg-image.data-v-08cb6188 {
  display: none;
}
.event-title-section.data-v-08cb6188 {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
.event-title.data-v-08cb6188 {
  white-space: normal;
  word-break: break-word;
  font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
  font-weight: normal;
  font-size: 32rpx;
  color: #23232A;
  line-height: 1.5;
}
.info-row.data-v-08cb6188 {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}
.info-text.data-v-08cb6188 {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-size: 26rpx;
  color: #606266;
  margin-left: 16rpx;
}
.status-bg-image.data-v-08cb6188 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.status-text.data-v-08cb6188 {
  font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
  font-weight: normal;
  font-size: 22rpx;
  color: #023F98;
  position: relative;
  z-index: 2;
}
.status-tag-detail.ended .status-text.data-v-08cb6188 {
  color: #FFFFFF;
}
.status-tag-detail.not-started .status-text.data-v-08cb6188 {
  color: #23232A;
}
.status-tag-detail.open .status-text.data-v-08cb6188 {
  color: #23232A;
}