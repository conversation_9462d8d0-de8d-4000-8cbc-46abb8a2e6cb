"use strict";const e=require("../../common/vendor.js"),o=require("../../api/data/user.js"),n=require("../../utils/config.js");if(!Array){(e.resolveComponent("up-avatar")+e.resolveComponent("up-cell")+e.resolveComponent("up-cell-group"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../uni_modules/uview-plus/components/u-cell/u-cell.js")+(()=>"../../uni_modules/uview-plus/components/u-cell-group/u-cell-group.js")+t)();const t=()=>"../../components/layout/CustomTabBar.js",l=Object.assign({name:"ProfileIndex"},{__name:"index",setup(t){const l=e.ref(!1),a=e.ref(null),i=e.ref(""),s=e.computed((()=>l.value&&a.value&&a.value.avatarUrl?a.value.avatarUrl:i.value)),r=e.ref(""),u=e.ref(""),c=e.ref(""),d=e.ref(""),v=e.ref(""),g=e.ref(""),p=e.ref(""),m=e.ref(""),h=o=>{const n=e.index.getStorageSync("staticAssets");return n&&n[o]?n[o]:""},f=()=>{i.value=h("default_avatar")},x=()=>{r.value=h("mybg"),u.value=h("my_edit"),c.value=h("group_right"),d.value=h("my_phone"),v.value=h("my_contract"),g.value=h("my_personal"),p.value=h("my_delete"),m.value=h("order-card-bg")},w=e.computed((()=>{if(!a.value)return"用户";if(a.value.nickname)return a.value.nickname;if(a.value.phoneNumber){const e=a.value.phoneNumber;return 11===e.length?e.substring(0,3)+"****"+e.substring(7):e}return"用户"})),y=e.computed((()=>{var e,o;if(!l.value)return"";const n=(null==(e=a.value)?void 0:e.phone)||(null==(o=a.value)?void 0:o.phoneNumber);return n?11===n.length?n.substring(0,3)+"****"+n.substring(7):n:"未绑定"})),_=o=>{if(!l.value)return console.log("用户未登录，跳转到登录页"),void L();console.log("用户已登录，跳转到:",o),e.index.navigateTo({url:o,fail:o=>{console.error("页面跳转失败:",o),e.index.showToast({title:"页面跳转失败",icon:"none",duration:2e3})}})},b=()=>{l.value?(console.log("点击报名订单卡片，跳转到订单页面"),e.index.navigateTo({url:"/pages_sub/pages_profile/orders",fail:o=>{console.error("跳转订单页面失败:",o),e.index.showToast({title:"页面跳转失败",icon:"none",duration:2e3})}})):(console.log("点击报名订单卡片，需要先登录"),L())},S=()=>{if(!l.value)return console.log("注销账号需要先登录"),void L();e.index.showModal({title:"注销账号",content:"注销后账号将被标记为已注销，确定要继续吗？",confirmText:"确定注销",cancelText:"取消",success:e=>{e.confirm&&(console.log("用户确认注销账号"),T())}})},T=async()=>{console.log("=== 开始执行注销账号流程 ==="),e.index.showLoading({title:"正在注销..."});try{console.log("调用后端注销接口...");const n=await o.deleteAccountApi();console.log("注销接口调用成功:",n),e.index.hideLoading(),e.index.showToast({title:"账号已成功注销",icon:"success",duration:2e3}),C(),console.log("注销账号流程完成")}catch(n){console.error("注销账号过程中发生错误:",n),e.index.hideLoading(),e.index.showToast({title:"注销失败，请稍后再试",icon:"none",duration:3e3}),console.error("注销失败详情:",{message:n.message,stack:n.stack})}},k=()=>{const o=e.index.getStorageSync("token"),n=e.index.getStorageSync("userInfo");console.log("从本地存储获取的token:",o),console.log("从本地存储获取的userInfo:",n);const t=!!o;console.log("计算出的登录状态:",t),console.log("当前页面登录状态:",l.value),l.value=t,a.value=n||null,l.value&&n?(console.log("用户已登录，用户信息:",n),n.phoneNumber&&console.log("用户手机号:",n.phoneNumber)):l.value&&!n?console.log("有token但无用户信息"):console.log("用户未登录")},L=()=>{e.index.navigateTo({url:"/pages_sub/pages_other/login"})},I=(o=!1)=>{o?j("已退出登录"):e.index.showModal({title:"提示",content:"确定要退出登录吗？",success:e=>{e.confirm&&j("已退出登录")}})},U=()=>{var o;if(!l.value)return console.log("编辑昵称需要先登录"),void L();e.index.showModal({title:"修改昵称",editable:!0,placeholderText:"请输入新昵称",content:(null==(o=a.value)?void 0:o.nickname)||"",success:async o=>{if(o.confirm&&o.content&&o.content.trim()){const n=o.content.trim();if(n.length>15)return void e.index.showToast({title:"昵称不能超过15个字符",icon:"none",duration:2e3});await A(n)}}})},A=async n=>{if(console.log("=== 开始更新昵称 ==="),n&&0!==n.trim().length)if(n.length>15)e.index.showToast({title:"昵称不能超过15个字符",icon:"none",duration:2e3});else{e.index.showLoading({title:"正在更新..."});try{console.log("调用updateUserInfoApi更新昵称:",n);const t=await o.updateUserInfoApi({nickname:n});console.log("昵称更新接口调用成功:",t),e.index.hideLoading(),a.value&&(a.value.nickname=n,e.index.setStorageSync("userInfo",a.value)),e.index.showToast({title:"昵称修改成功",icon:"success",duration:2e3}),console.log("昵称更新完成")}catch(t){console.error("更新昵称过程中发生错误:",t),e.index.hideLoading(),e.index.showToast({title:t.message||"更新失败，请稍后再试",icon:"none",duration:3e3}),console.error("昵称更新失败详情:",{message:t.message,stack:t.stack})}}else e.index.showToast({title:"昵称不能为空",icon:"none",duration:2e3})},j=o=>{console.log("=== 开始清除用户数据 ==="),e.index.removeStorageSync("token"),e.index.removeStorageSync("userInfo"),l.value=!1,a.value=null,o&&e.index.showToast({title:o,icon:"success",duration:1500}),console.log("用户数据已清除，UI已更新")},C=()=>{console.log("=== 静默清除用户数据 ==="),e.index.removeStorageSync("token"),e.index.removeStorageSync("userInfo"),l.value=!1,a.value=null,console.log("用户数据已静默清除，UI已更新")};e.onShow((()=>{e.index.hideTabBar(),setTimeout((()=>{k(),f(),x()}),100)})),e.onLoad((()=>{k(),f(),x()}));const N=e=>{var o;const n=null==(o=null==e?void 0:e.detail)?void 0:o.avatarUrl;n&&B(n)},B=async t=>{if(!l.value)return void L();const i=e.index.getStorageSync("token");if(i){e.index.showLoading({title:"上传中..."});try{await new Promise(((l,s)=>{e.index.uploadFile({url:n.BASE_URL+"/common/upload",filePath:t,name:"file",header:{Authorization:"Bearer "+i},success:async n=>{try{const t=JSON.parse(n.data||"{}");if(200===n.statusCode&&200===t.code&&t.url){const n=t.url;await o.updateUserInfoApi({avatarUrl:n}),a.value&&(a.value.avatarUrl=n,e.index.setStorageSync("userInfo",a.value)),e.index.showToast({title:"头像已更新",icon:"success"}),l(!0)}else e.index.showToast({title:"上传失败",icon:"none"}),s(new Error("upload error"))}catch(t){e.index.showToast({title:"响应解析失败",icon:"none"}),s(t)}},fail:o=>{e.index.showToast({title:"上传失败",icon:"none"}),s(o)},complete:()=>{e.index.hideLoading()}})}))}catch(s){}}else L()};return(o,n)=>e.e({a:r.value,b:e.o((e=>l.value?null:L)),c:e.p({size:54,src:s.value}),d:l.value},l.value?{e:e.o(N)}:{},{f:l.value},l.value?{g:e.t(w.value),h:u.value,i:e.o(U)}:{j:e.o(L)},{k:m.value,l:c.value,m:e.o(b),n:d.value,o:e.p({title:"绑定手机号",value:y.value,isLink:!1,border:!1}),p:g.value,q:e.o((e=>_("/pages_sub/pages_other/policy?type=privacy_policy"))),r:e.p({title:"隐私政策",isLink:!0,"arrow-direction":"right",border:!1}),s:v.value,t:e.o((e=>_("/pages_sub/pages_other/policy?type=user_agreement"))),v:e.p({title:"用户协议",isLink:!0,"arrow-direction":"right",border:!1}),w:p.value,x:e.o(S),y:e.p({title:"注销账号",isLink:!0,"arrow-direction":"right",border:!1}),z:e.p({border:!1}),A:l.value},l.value?{B:e.o(I)}:{},{C:e.p({current:4})})}}),a=e._export_sfc(l,[["__scopeId","data-v-300c2144"]]);wx.createPage(a);
