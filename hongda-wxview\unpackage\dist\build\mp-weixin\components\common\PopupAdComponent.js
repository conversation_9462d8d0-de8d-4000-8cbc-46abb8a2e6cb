"use strict";const e=require("../../common/vendor.js"),o=require("../../utils/navigation.js");if(!Array){e.resolveComponent("uni-icons")()}Math;const a={__name:"PopupAdComponent",props:{show:{type:Boolean,default:!1},adData:{type:Object,default:()=>({})}},emits:["close"],setup(a,{emit:t}){const n=a,s=t,c=e.ref(n.show);e.watch((()=>n.show),(e=>{console.log("弹窗显示状态变化:",e),console.log("广告数据:",n.adData),c.value=e}),{immediate:!0});const l=e.computed((()=>n.adData||{})),i=()=>{console.log("广告被点击:",n.adData),n.adData&&"function"==typeof o.navigateTo&&o.navigateTo(n.adData),p()},p=()=>{console.log("关闭弹窗"),c.value=!1,s("close")};return(o,a)=>e.e({a:c.value},c.value?{b:l.value.imageUrl,c:e.o(i),d:e.p({type:"closeempty",size:"20",color:"#fff"}),e:e.o(p),f:e.o((()=>{}))}:{})}},t=e._export_sfc(a,[["__scopeId","data-v-95b676fc"]]);wx.createComponent(t);
