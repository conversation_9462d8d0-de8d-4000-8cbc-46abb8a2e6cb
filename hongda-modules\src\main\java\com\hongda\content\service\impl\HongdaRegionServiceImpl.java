package com.hongda.content.service.impl;

import com.hongda.common.exception.ServiceException;
import com.hongda.common.utils.DateUtils;
import com.hongda.content.domain.HongdaRegion;
import com.hongda.content.mapper.HongdaRegionMapper;
import com.hongda.content.service.IHongdaRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 地区管理Service业务层处理
 * * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class HongdaRegionServiceImpl implements IHongdaRegionService
{
    @Autowired
    private HongdaRegionMapper hongdaRegionMapper;

    /**
     * 查询地区管理
     * * @param id 地区管理主键
     * @return 地区管理
     */
    @Override
    public HongdaRegion selectHongdaRegionById(Long id)
    {
        return hongdaRegionMapper.selectHongdaRegionById(id);
    }

    /**
     * 查询地区管理列表
     * * @param hongdaRegion 地区管理
     * @return 地区管理
     */
    @Override
    public List<HongdaRegion> selectHongdaRegionList(HongdaRegion hongdaRegion)
    {
        return hongdaRegionMapper.selectHongdaRegionList(hongdaRegion);
    }

    /**
     * 新增地区管理
     * * @param hongdaRegion 地区管理
     * @return 结果
     */
    @Override
    public int insertHongdaRegion(HongdaRegion hongdaRegion)
    {
        hongdaRegion.setCreateTime(DateUtils.getNowDate());
        return hongdaRegionMapper.insertHongdaRegion(hongdaRegion);
    }

    /**
     * 修改地区管理
     * * @param hongdaRegion 地区管理
     * @return 结果
     */
    @Override
    public int updateHongdaRegion(HongdaRegion hongdaRegion)
    {
        hongdaRegion.setUpdateTime(DateUtils.getNowDate());
        return hongdaRegionMapper.updateHongdaRegion(hongdaRegion);
    }

    /**
     * 批量删除地区管理
     * [修改] 增加业务逻辑判断
     * @param ids 需要删除的地区管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaRegionByIds(Long[] ids)
    {
        // 1. 根据ID数组查询出所有待删除的地区对象
        List<HongdaRegion> regionsToDelete = hongdaRegionMapper.selectHongdaRegionByIds(ids);

        // 2. 检查是否有地区已关联文章
        List<String> protectedRegions = new ArrayList<>();
        for (HongdaRegion region : regionsToDelete) {
            Integer articleCount = hongdaRegionMapper.countArticlesByRegion(region.getCode());
            if (articleCount > 0) {
                // 如果有关联，记录下地区的名字和数量
                protectedRegions.add(String.format("'%s'(%d篇)", region.getName(), articleCount));
            }
        }

        // 3. 如果 protectedRegions 列表不为空，说明有不能删除的地区
        if (!protectedRegions.isEmpty()) {
            // 构造详细的错误信息并抛出异常
            String errorMsg = "删除失败，以下地区已关联资讯: " + String.join("、", protectedRegions);
            throw new ServiceException(errorMsg);
        }

        // 4. 如果列表为空，说明所有选中的地区都可以安全删除
        if (ids.length == 0) {
            return 0;
        }
        return hongdaRegionMapper.deleteHongdaRegionByIds(ids);
    }

    /**
     * 删除地区信息
     * [修改] 增加业务逻辑判断
     * @param id 地区ID
     * @return 结果
     */
    @Override
    public int deleteHongdaRegionById(Long id)
    {
        // 1. 先根据ID查询出地区实体
        HongdaRegion region = hongdaRegionMapper.selectHongdaRegionById(id);
        if (region == null) {
            throw new ServiceException("地区不存在，可能已被删除");
        }

        // 2. 根据地区的code查询关联的文章数量
        Integer articleCount = hongdaRegionMapper.countArticlesByRegion(region.getCode());

        // 3. 如果有关联的文章，则抛出异常，阻止删除
        if (articleCount > 0) {
            throw new ServiceException(String.format("删除失败，地区'%s'已关联 %d 篇资讯", region.getName(), articleCount));
        }

        // 4. 如果没有关联的文章，才执行删除
        return hongdaRegionMapper.deleteHongdaRegionById(id);
    }
}