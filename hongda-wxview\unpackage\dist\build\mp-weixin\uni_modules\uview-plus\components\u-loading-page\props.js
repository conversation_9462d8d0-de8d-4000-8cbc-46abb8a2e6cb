"use strict";const e=require("../../libs/vue.js"),o=require("../../libs/config/props.js"),i=e.defineMixin({props:{loadingText:{type:[String,Number],default:()=>o.props.loadingPage.loadingText},image:{type:String,default:()=>o.props.loadingPage.image},loadingMode:{type:String,default:()=>o.props.loadingPage.loadingMode},loading:{type:<PERSON>olean,default:()=>o.props.loadingPage.loading},bgColor:{type:String,default:()=>o.props.loadingPage.bgColor},color:{type:String,default:()=>o.props.loadingPage.color},fontSize:{type:[String,Number],default:()=>o.props.loadingPage.fontSize},iconSize:{type:[String,Number],default:()=>o.props.loadingPage.fontSize},loadingColor:{type:String,default:()=>o.props.loadingPage.loadingColor},zIndex:{type:[Number],default:()=>o.props.loadingPage.zIndex}}});exports.props=i;
