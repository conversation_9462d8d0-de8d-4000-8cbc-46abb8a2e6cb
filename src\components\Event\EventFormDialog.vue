<template>
  <el-dialog 
    :title="title" 
    v-model="open" 
    width="1000px" 
    append-to-body
    @close="cancel"
  >
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="基本信息" name="basicInfo">
        <el-form ref="eventRef" :model="form" :rules="rules" label-width="120px">
          <!-- 基础信息卡片 -->
          <el-card shadow="never" class="form-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">基础信息</span>
              </div>
            </template>
            
            <!-- 核心信息区 -->
            <el-form-item label="活动名称" prop="title">
              <el-input 
                v-model="form.title" 
                placeholder="请输入活动名称" 
                maxlength="100"
                show-word-limit
                size="large"
              />
            </el-form-item>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="活动简介" prop="summary">
                  <el-input 
                    v-model="form.summary" 
                    placeholder="请输入活动简介，20字以内" 
                    maxlength="20"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="卖点" prop="sellPoint">
                  <el-input 
                    v-model="form.sellPoint" 
                    placeholder="请输入活动卖点，15字以内" 
                    maxlength="15"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 媒体资源区 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="活动封面" prop="coverImageUrl">
                  <image-upload v-model="form.coverImageUrl" recommendation-text="推荐大小为504px * 288px"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="活动图标" prop="iconUrl">
                  <image-upload v-model="form.iconUrl" :is-show-tip="false"/>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 推广配置区 - V3.0新增，始终显示 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="推广图片" prop="promotionImageUrl">
                  <image-upload 
                    v-model="form.promotionImageUrl" 
                    recommendation-text="推荐大小为909px * 300px，用于首页推广展示"
                    :is-show-tip="true"
                  />

                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="推广排序" prop="promotionSortOrder">
                  <el-input-number 
                    v-model="form.promotionSortOrder" 
                    :min="0" 
                    controls-position="right" 
                    style="width: 100%" 
                    placeholder="数字越小排序越靠前"
                  />
                  <div class="form-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>设置推广排序值，数字越小在首页展示越靠前</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          
          <!-- 报名设置卡片 -->
          <el-card shadow="never" class="form-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">报名设置</span>
              </div>
            </template>
            
            <!-- 时间设置 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="活动时间" prop="activityTimeRange">
                  <el-date-picker 
                    clearable 
                    v-model="form.activityTimeRange" 
                    type="datetimerange" 
                    value-format="YYYY-MM-DD HH:mm:ss" 
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 100%"
                    @change="handleActivityTimeChange"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="报名时间" prop="registrationTimeRange">
                  <el-date-picker 
                    clearable 
                    v-model="form.registrationTimeRange" 
                    type="datetimerange" 
                    value-format="YYYY-MM-DD HH:mm:ss" 
                    start-placeholder="报名开始"
                    end-placeholder="报名截止"
                    style="width: 100%"
                    @change="handleRegistrationTimeChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 地点设置 -->
            <el-form-item label="活动地点" prop="province">
              <el-cascader
                ref="cascaderRef"
                v-model="selectedLocation"
                :options="locationOptions"
                placeholder="请选择省市"
                style="width: 100%"
                @change="handleLocationChange"
                clearable
              />
            </el-form-item>
            
            <el-form-item label="详细地址" prop="addressDetail">
              <el-input
                v-model="form.addressDetail"
                placeholder="请输入区县、街道、楼栋、门牌号等详细地址"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <!-- 报名人数设置 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最大报名人数" prop="maxParticipants">
                  <div class="participant-setting">
                    <el-input-number 
                      v-model="form.maxParticipants" 
                      :min="0" 
                      placeholder="输入人数"
                      style="width: 200px"
                      controls-position="right"
                    />
                    <span class="participant-tip">（0表示不限制人数）</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          
          <!-- 详细内容卡片 -->
          <el-card shadow="never" class="form-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">详细内容</span>
              </div>
            </template>
            
            <el-form-item label="活动图文详情" prop="details">
              <editor v-model="form.details" :min-height="300"/>
            </el-form-item>
          </el-card>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="报名表单设计" name="formDesign">
          <fc-designer ref="designer" :rule="currentFormDefinition" :option="designerOption"></fc-designer>
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
        </div>
        <div class="footer-right">
          <el-button size="large" @click="cancel">取 消</el-button>
          <el-button type="primary" size="large" @click="handleConfirm">
            确 定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="EventFormDialog">
import { ref, reactive, toRefs, getCurrentInstance, nextTick } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// API导入
import { getEvent, addEvent, updateEvent } from "@/api/content/event";
import { getFormDefinition, saveFormDefinition } from "@/api/content/form";

// 导入全国省市区数据
import { regionData } from 'element-china-area-data';

// 定义 emit，用于通知父组件操作已成功
const emit = defineEmits(['success']);

// 基础状态
const open = ref(false);
const title = ref("");
const activeTab = ref("basicInfo");

// 表单相关
const designer = ref(null);
const currentFormDefinition = ref(null);

// 省市级联选择器相关
const selectedLocation = ref([]);

/**
 * 处理地区数据，只保留省市两级
 * @param {Array} data - 原始三级地区数据
 * @returns {Array} 处理后的两级地区数据
 */
const processTwoLevelLocationData = (data) => {
  return data.map(province => ({
    ...province,
    children: province.children ? province.children.map(city => {
      // 创建一个新的 city 对象，但不包含 children 属性
      const { children, ...cityWithoutChildren } = city;
      return cityWithoutChildren;
    }) : []
  }));
};

// 省市级联数据（处理为两级）
const locationOptions = ref(processTwoLevelLocationData(regionData));

// 配置项
const designerOption = ref({
  height: '500px'
});

// 主数据和表单
const data = reactive({
  form: {},
  rules: {
    title: [{ required: true, message: "活动名称不能为空", trigger: "blur" }],
    iconUrl: [{ required: true, message: "活动图标不能为空", trigger: "change" }],
    activityTimeRange: [{ required: true, message: "活动时间不能为空", trigger: "change" }],
    registrationTimeRange: [{ required: true, message: "报名时间不能为空", trigger: "change" }],
    province: [{ required: true, message: "请选择活动地点", trigger: "change" }],
    addressDetail: [{ required: true, message: "详细地址不能为空", trigger: "blur" }],
  }
});

const { form, rules } = toRefs(data);

// 通用API调用函数
const apiCall = async (apiFunc, params = null) => {
  try {
    return await apiFunc(params);
  } catch (error) {
    proxy.$modal.msgError(error.message || "操作失败");
    throw error;
  }
};

// 重置表单数据
const reset = () => {
  form.value = {
    id: null,
    title: null,
    iconUrl: null,
    coverImageUrl: null,
    details: '',
    summary: null,
    sellPoint: null,
    startTime: null,
    endTime: null,
    location: null,
    province: null,
    city: null,
    district: null,
    addressDetail: null,
    registrationDeadline: null,
    registrationStartTime: null,
    registrationEndTime: null,
    maxParticipants: 0,
    status: "0",
    isHot: 0,
    activityTimeRange: [],
    registrationTimeRange: [],
    // V3.0新增：推广配置字段，始终可编辑
    promotionImageUrl: null,
    promotionSortOrder: 0
  };
  // 重置级联选择器
  selectedLocation.value = [];
  if (proxy.$refs["eventRef"]) {
    proxy.$refs["eventRef"].resetFields();
  }
  activeTab.value = "basicInfo";
  currentFormDefinition.value = null;
  if (designer.value) {
    designer.value.setRule([]);
  }
};

// 关闭对话框
function cancel() {
  open.value = false;
  reset();
}

/**
 * 处理级联选择器变化（省市两级）
 * @param {Array} value - 选择的地区值数组
 */
function handleLocationChange(value) {
  if (value && value.length === 2) {
    // 根据地区代码查找对应的中文名称
    const provinceCode = value[0];
    const cityCode = value[1];
    
    // 查找省份名称
    const provinceData = locationOptions.value.find(p => p.value === provinceCode);
    const provinceName = provinceData ? provinceData.label : null;
    
    // 查找城市名称
    let cityName = null;
    if (provinceData && provinceData.children) {
      const cityData = provinceData.children.find(c => c.value === cityCode);
      cityName = cityData ? cityData.label : null;
    }

    // 直辖市的省份名称通常包含 "市" 字，例如 "北京市", "上海市"
    if (provinceName && (provinceName.includes('北京') || provinceName.includes('上海') || provinceName.includes('天津') || provinceName.includes('重庆'))) {
        // 如果是直辖市，市的名称就直接用省的名称（保持完整的"市"字）
        cityName = provinceName;
    }
    
    // 保存中文名称而不是代码
    form.value.province = provinceName;
    form.value.city = cityName;
    form.value.district = null; // 区级信息清空，在详细地址中填写
  } else {
    form.value.province = null;
    form.value.city = null;
    form.value.district = null;
  }
}

// 处理活动时间范围变化
function handleActivityTimeChange(value) {
  if (value && value.length === 2) {
    form.value.startTime = value[0];
    form.value.endTime = value[1];
    // 同步更新activityTimeRange用于验证
    form.value.activityTimeRange = value;
  } else {
    form.value.startTime = null;
    form.value.endTime = null;
    form.value.activityTimeRange = [];
  }
}

// 处理报名时间范围变化
function handleRegistrationTimeChange(value) {
  if (value && value.length === 2) {
    form.value.registrationStartTime = value[0];
    form.value.registrationEndTime = value[1];
    // 同步更新registrationTimeRange用于验证
    form.value.registrationTimeRange = value;
  } else {
    form.value.registrationStartTime = null;
    form.value.registrationEndTime = null;
    form.value.registrationTimeRange = [];
  }
}

/**
 * 根据省市字段设置级联选择器的值
 * 从中文名称查找对应的地区代码，特别处理直辖市情况
 */
function setLocationFromForm() {
  if (!form.value.province || !form.value.city) {
    selectedLocation.value = [];
    return;
  }

  // 根据中文名称查找对应的地区代码
  const provinceData = locationOptions.value.find(p => p.label === form.value.province);
  
  if (!provinceData) {
    selectedLocation.value = [];
    return;
  }

  // 先尝试直接匹配城市名称
  let cityData = provinceData.children?.find(c => c.label === form.value.city);
  
  // 如果直接匹配失败，尝试各种匹配方式
  if (!cityData) {
    // 检查是否为直辖市
    const isMunicipality = ['北京', '上海', '天津', '重庆'].some(name => 
      form.value.province.includes(name)
    );
    
    if (isMunicipality) {
      // 对于直辖市，通常子级是 "市辖区"，我们需要选择第一个可用选项
      if (provinceData.children && provinceData.children.length > 0) {
        // 优先选择包含"市辖区"的选项
        cityData = provinceData.children.find(c => c.label.includes('市辖区'));
        
        // 如果没有"市辖区"，则选择第一个子选项
        if (!cityData) {
          cityData = provinceData.children[0];
        }
      }
      
      // 如果还是没找到，尝试其他匹配方式
      if (!cityData) {
        const cityWithoutSuffix = form.value.city.replace(/市$/, '');
        const provinceWithoutSuffix = form.value.province.replace(/市$/, '');
        
        cityData = provinceData.children?.find(c => {
          const labelWithoutSuffix = c.label.replace(/市$/, '');
          return labelWithoutSuffix === cityWithoutSuffix || 
                 labelWithoutSuffix === provinceWithoutSuffix ||
                 c.label === form.value.province ||
                 c.label === form.value.city;
        });
      }
    } else {
      // 非直辖市，尝试模糊匹配
      cityData = provinceData.children?.find(c => {
        return c.label.includes(form.value.city) || 
               form.value.city.includes(c.label.replace(/市$/, ''));
      });
    }
  }
  
  if (cityData) {
    // 先清空，再设置新值，确保组件能够正确响应变化
    selectedLocation.value = [];
    nextTick(() => {
      selectedLocation.value = [provinceData.value, cityData.value];
    });
  } else {
    selectedLocation.value = [];
  }
}

// 根据表单数据设置时间范围选择器的值
function setTimeRangeFromForm() {
  // 设置活动时间范围
  if (form.value.startTime && form.value.endTime) {
    form.value.activityTimeRange = [form.value.startTime, form.value.endTime];
  } else {
    form.value.activityTimeRange = [];
  }
  
  // 设置报名时间范围
  if (form.value.registrationStartTime && form.value.registrationEndTime) {
    form.value.registrationTimeRange = [form.value.registrationStartTime, form.value.registrationEndTime];
  } else {
    form.value.registrationTimeRange = [];
  }
}

// 表单提交逻辑
function submitForm() {
  proxy.$refs["eventRef"].validate(valid => {
    if (valid) {
      const isUpdate = form.value.id != null;
      const apiFunc = isUpdate ? updateEvent : addEvent;
      
      apiCall(apiFunc, form.value).then(response => {
        if (!isUpdate) {
          form.value.id = response.data.id; 
          title.value = "修改活动"; 
          activeTab.value = "formDesign";
          proxy.$modal.msgSuccess("新增活动成功，请继续设计报名表单！");
        } else {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          // 通知父组件刷新列表
          emit('success'); 
        }
        // 通知父组件刷新列表
        emit('success');
      });
    }
  });
}

// 验证基础信息是否完整
function validateBasicInfo() {
  return new Promise((resolve) => {
    proxy.$refs["eventRef"].validate((valid) => {
      resolve(valid);
    });
  });
}

// 处理标签页切换（移除验证逻辑，允许自由切换）
function handleTabChange(tabName) {
  // 标签页切换时不进行验证，允许用户自由切换
}

function handleConfirm() {
  if (activeTab.value === 'formDesign') {
    // 在报名表单设计标签页时，先验证基础信息
    validateBasicInfo().then((valid) => {
      if (!valid) {
        proxy.$modal.msgWarning("请先完善基础信息后再进行表单设计！");
        activeTab.value = 'basicInfo';
        return;
      }
      
      // 验证通过后，保存表单设计并关闭对话框
      if (!form.value.id) {
        proxy.$modal.msgError("活动ID不存在，无法保存！");
        return;
      }
      const formJson = designer.value.getRule();
      const dataToSave = {
         eventId: form.value.id,
         fieldsJson: JSON.stringify(formJson)
      };
      apiCall(saveFormDefinition, dataToSave).then(() => {
         proxy.$modal.msgSuccess("报名表单设计已保存！");
         open.value = false;
         // 通知父组件刷新列表
         emit('success');
      });
    });
  } else {
    // 在基本信息标签页时，调用原有的 submitForm 逻辑
    submitForm();
  }
}

// 对外暴露一个方法，让父组件可以调用它来打开弹窗
const openDialog = async (eventId = null) => {
  reset();
  if (eventId) {
    // 修改模式
    title.value = "修改活动";
    try {
      const response = await apiCall(getEvent, eventId);
      form.value = response.data;
      
      // 设置级联选择器和时间范围选择器的值
      nextTick(() => {
        setLocationFromForm();
        setTimeRangeFromForm();
      });
      
      // 加载表单设计器定义
      nextTick(async () => {
        try {
          const formDefRes = await getFormDefinition(eventId);
          if (formDefRes.data && formDefRes.data.fieldsJson) {
            designer.value.setRule(JSON.parse(formDefRes.data.fieldsJson));
          } else {
            designer.value.setRule([]);
          }
        } catch (error) {
           designer.value.setRule([]);
        }
      });
    } catch (error) {
      proxy.$modal.msgError("获取活动信息失败");
      return;
    }
  } else {
    // 新增模式
    title.value = "添加活动";
  }
  open.value = true;
};

defineExpose({
  openDialog
});
</script>

<style scoped>
.form-create-custom {
  margin-top: 10px;
}

.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}

/* 表单卡片样式 */
.form-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.form-card :deep(.el-card__body) {
  padding: 24px 20px;
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 媒体上传区域样式 */
.media-upload-section .upload-tip {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

.media-upload-section .el-form-item {
  margin-bottom: 24px;
}

/* 报名人数设置样式 */
.participant-setting {
  display: flex;
  align-items: center;
  gap: 12px;
}

.participant-tip {
  font-size: 12px;
  color: #909399;
}

/* 表单项间距优化 */
.el-form .el-form-item {
  margin-bottom: 20px;
}

/* 时间选择器组合样式 */
.el-date-picker .el-input__wrapper {
  border-radius: 6px;
}

/* 表单提示样式 - V3.0新增 */
.form-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.form-tip .el-icon {
  font-size: 14px;
  color: #409eff;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .form-card :deep(.el-card__body) {
    padding: 16px;
  }
  
  .media-upload-section {
    margin-top: 20px;
  }
}
</style>
