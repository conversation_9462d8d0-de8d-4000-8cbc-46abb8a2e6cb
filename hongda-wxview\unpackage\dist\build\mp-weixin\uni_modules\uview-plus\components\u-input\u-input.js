"use strict";const e=require("../../../../common/vendor.js"),i=require("./props.js"),n=require("../../libs/mixin/mpMixin.js"),t=require("../../libs/mixin/mixin.js"),r=require("../../libs/function/index.js"),o={name:"u-input",mixins:[n.mpMixin,t.mixin,i.props],data:()=>({clearInput:!1,innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:e=>e}),created(){this.formatter&&(this.innerFormatter=this.formatter)},watch:{modelValue:{immediate:!0,handler(e,i){this.changeFromInner||this.innerValue===e||(this.innerValue=e,!1===this.firstChange&&!1===this.changeFromInner?this.valueChange(this.innerValue,!0):this.firstChange||r.formValidate(this,"change"),this.firstChange=!1),this.changeFromInner=!1}}},computed:{isShowClear(){const{clearable:e,readonly:i,focused:n,innerValue:t}=this;return!!e&&!i&&!!n&&""!==t},inputClass(){let e=[],{border:i,disabled:n,shape:t}=this;return"surround"===i&&(e=e.concat(["u-border","u-input--radius"])),e.push(`u-input--${t}`),"bottom"===i&&(e=e.concat(["u-border-bottom","u-input--no-radius"])),e.join(" ")},wrapperStyle(){const e={};return this.disabled&&(e.backgroundColor=this.disabledColor),"none"===this.border?e.padding="0":(e.paddingTop="6px",e.paddingBottom="6px",e.paddingLeft="9px",e.paddingRight="9px"),r.deepMerge(e,r.addStyle(this.customStyle))},inputStyle(){return{color:this.color,fontSize:r.addUnit(this.fontSize),textAlign:this.inputAlign}}},emits:["update:modelValue","focus","blur","change","confirm","clear","keyboardheightchange","nicknamereview"],methods:{setFormatter(e){this.innerFormatter=e},onInput(e){let{value:i=""}=e.detail||{};this.innerValue=i,this.$nextTick((()=>{let e=this.innerFormatter(i);this.innerValue=e,this.valueChange(e)}))},onBlur(e){this.$emit("blur",e.detail.value),r.sleep(150).then((()=>{this.focused=!1})),r.formValidate(this,"blur")},onFocus(e){this.focused=!0,this.$emit("focus")},doFocus(){this.$refs["input-native"].focus()},doBlur(){this.$refs["input-native"].blur()},onConfirm(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},onnicknamereview(e){this.$emit("nicknamereview",e)},valueChange(e,i=!1){this.clearInput&&(this.innerValue="",this.clearInput=!1),this.$nextTick((()=>{i&&!this.clearInput||(this.changeFromInner=!0,this.$emit("change",e),this.$emit("update:modelValue",e)),r.formValidate(this,"change")}))},onClear(){this.clearInput=!0,this.innerValue="",this.$nextTick((()=>{this.valueChange(""),this.$emit("clear")}))},clickHandler(){(this.disabled||this.readonly)&&e.index.hideKeyboard()}}};if(!Array){e.resolveComponent("u-icon")()}Math;const s=e._export_sfc(o,[["render",function(i,n,t,r,o,s){return e.e({a:i.prefixIcon||i.$slots.prefix},i.prefixIcon||i.$slots.prefix?{b:e.p({name:i.prefixIcon,size:"18",customStyle:i.prefixIconStyle})}:{},{c:e.s(s.inputStyle),d:i.type,e:i.focus,f:i.cursor,g:o.innerValue,h:i.autoBlur,i:i.disabled||i.readonly,j:i.maxlength,k:i.placeholder,l:i.placeholderStyle,m:i.placeholderClass,n:i.confirmType,o:i.confirmHold,p:i.holdKeyboard,q:i.cursorSpacing,r:i.adjustPosition,s:i.selectionEnd,t:i.selectionStart,v:i.password||"password"===i.type||!1,w:i.ignoreCompositionEvent,x:e.o(((...e)=>s.onInput&&s.onInput(...e))),y:e.o(((...e)=>s.onBlur&&s.onBlur(...e))),z:e.o(((...e)=>s.onFocus&&s.onFocus(...e))),A:e.o(((...e)=>s.onConfirm&&s.onConfirm(...e))),B:e.o(((...e)=>s.onkeyboardheightchange&&s.onkeyboardheightchange(...e))),C:e.o(((...e)=>s.onnicknamereview&&s.onnicknamereview(...e))),D:e.o(((...e)=>s.clickHandler&&s.clickHandler(...e))),E:s.isShowClear},s.isShowClear?{F:e.p({name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}),G:e.o(((...e)=>s.onClear&&s.onClear(...e)))}:{},{H:i.suffixIcon||i.$slots.suffix},i.suffixIcon||i.$slots.suffix?{I:e.p({name:i.suffixIcon,size:"18",customStyle:i.suffixIconStyle})}:{},{J:e.n(s.inputClass),K:e.s(s.wrapperStyle)})}],["__scopeId","data-v-8b3a09b3"]]);wx.createComponent(s);
