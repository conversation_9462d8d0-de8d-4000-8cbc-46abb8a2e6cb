"use strict";const e=require("../../common/vendor.js"),t=require("../../api/data/event.js"),a=require("../../utils/image.js");require("../../utils/config.js");const i=require("../../utils/location.js");if(!Array){(e.resolveComponent("up-icon")+e.resolveComponent("up-loading-icon")+e.resolveComponent("up-empty"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js"))();const n={__name:"ActivityGridComponent",emits:["all-loaded-change"],setup(n,{expose:l,emit:o}){const u=o,s={time:"",location:""},r=e.ref(!1),c=e.ref(!1),d=e.ref([]),m=e.ref(0),g=e.computed((()=>{const e="精选活动";return{main:e.slice(0,2),gradient:e.slice(2)}})),v=e.computed((()=>d.value.slice(0,Math.min(m.value,d.value.length)))),p=e.computed((()=>m.value<d.value.length));e.watch(p,(e=>{u("all-loaded-change",!e)})),e.onMounted((()=>{try{const t=e.index.getStorageSync("staticAssets");s.time=(null==t?void 0:t.detail_icon_time)||"",s.location=(null==t?void 0:t.detail_icon_location)||""}catch(t){}w()}));const f=e=>{if(!e)return null;if(e instanceof Date)return isNaN(e.getTime())?null:e;if("number"==typeof e){const t=new Date(e);return isNaN(t.getTime())?null:t}if("string"==typeof e){let t=e.trim();/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(t)&&(t=t.replace(" ","T"));let a=new Date(t);if(isNaN(a.getTime())){const e=t.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);if(e){const t=e[1],i=e[2],n=e[3],l=e[4]?` ${e[4]}:${e[5]}:${e[6]||"00"}`:"";a=new Date(`${t}/${i}/${n}${l}`)}}return isNaN(a.getTime())?null:a}return null},h=e=>{if(!e)return"";const t=f(e);if(!t)return"";return`${t.getMonth()+1}.${String(t.getDate()).padStart(2,"0")}`},y=e=>{if(!e)return"";const t=f(e);if(!t)return"";return["周日","周一","周二","周三","周四","周五","周六"][t.getDay()]},T=(e,t)=>{if(!e||e<=0)return"不限";const a=e-(t||0);return a>0?a:0},w=async()=>{if(!r.value){r.value=!0;try{const e=await t.getHotEventListApi(100),n=(e.data||e.rows||[]).filter((e=>1===e.registrationStatus));d.value=n.map((e=>({id:e.id,title:e.title,image:a.getFullImageUrl(e.coverImageUrl),location:i.formatActivityLocation(e),status:e.registrationStatusText||"报名中",statusClass:"status-registering",dateTime:h(e.startTime),weekday:y(e.startTime),remainingSpots:T(e.maxParticipants,e.registeredCount)}))),m.value=Math.min(4,d.value.length),u("all-loaded-change",!(m.value<d.value.length))}catch(n){console.error("获取热门活动失败:",n),d.value=[{id:1,title:"2025 Ozen卖家增长峰会·北京站",status:"报名中",statusClass:"status-registering",location:"北京",image:"https://via.placeholder.com/170x100/3c9cff/fff?text=活动1",dateTime:"7.15",weekday:"周二",remainingSpots:"29"}],m.value=Math.min(4,d.value.length),u("all-loaded-change",!(m.value<d.value.length)),e.index.showToast({title:"获取活动数据失败，显示示例数据",icon:"none",duration:2e3})}finally{r.value=!1}}},x=()=>{e.index.switchTab({url:"/pages/event/index",fail:()=>{e.index.navigateTo({url:"/pages/event/index"})}})};return l({loadMore:()=>{c.value||p.value&&(c.value=!0,setTimeout((()=>{m.value=Math.min(m.value+2,d.value.length),c.value=!1}),50))}}),(t,a)=>e.e({a:e.t(g.value.main),b:e.t(g.value.gradient),c:e.p({name:"arrow-right",size:"14"}),d:e.o(x),e:r.value},r.value?{f:e.p({mode:"spinner",size:"40"})}:{g:e.f(v.value,((t,a,i)=>({a:t.image,b:e.t(t.status),c:e.n(t.statusClass),d:e.t(t.title),e:e.t(t.dateTime),f:e.t(t.weekday),g:e.t(t.location),h:e.t(t.remainingSpots),i:t.id||a,j:e.o((a=>{return i=t,void e.index.navigateTo({url:`/pages_sub/pages_event/detail?id=${i.id}`});var i}),t.id||a)}))),h:s.time,i:s.location},{j:!r.value&&0===v.value.length},r.value||0!==v.value.length?{}:{k:e.p({mode:"data",text:"暂无精选活动",textColor:"#909399",iconSize:"80"})})}},l=e._export_sfc(n,[["__scopeId","data-v-aa50b19d"]]);wx.createComponent(l);
