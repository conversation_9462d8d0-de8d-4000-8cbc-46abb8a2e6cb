"use strict";const t=require("../../../../common/vendor.js"),e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),o=require("../../libs/mixin/mixin.js"),r=require("../../libs/config/config.js"),n=require("../../libs/function/index.js"),l={name:"u-navbar",mixins:[i.mpMixin,o.mixin,e.props],data:()=>({}),emits:["leftClick","rightClick"],methods:{addStyle:n.addStyle,addUnit:n.addUnit,getWindowInfo:n.getWindowInfo,getPx:n.getPx,leftClick(){this.$emit("leftClick"),null!=r.config.interceptor.navbarLeftClick?r.config.interceptor.navbarLeftClick():this.autoBack&&t.index.navigateBack()},rightClick(){this.$emit("rightClick")}}};if(!Array){(t.resolveComponent("u-status-bar")+t.resolveComponent("u-icon"))()}Math||((()=>"../u-status-bar/u-status-bar.js")+(()=>"../u-icon/u-icon.js"))();const s=t._export_sfc(l,[["render",function(e,i,o,r,n,l){return t.e({a:e.fixed&&e.placeholder},e.fixed&&e.placeholder?{b:l.addUnit(l.getPx(e.height)+l.getWindowInfo().statusBarHeight,"px")}:{},{c:e.safeAreaInsetTop},e.safeAreaInsetTop?{d:t.p({bgColor:e.statusBarBgColor?e.statusBarBgColor:e.bgColor})}:{},{e:e.leftIcon},e.leftIcon?{f:t.p({name:e.leftIcon,size:e.leftIconSize,color:e.leftIconColor})}:{},{g:e.leftText},e.leftText?{h:t.t(e.leftText),i:e.leftIconColor}:{},{j:t.o(((...t)=>l.leftClick&&l.leftClick(...t))),k:t.t(e.title),l:t.s({width:l.addUnit(e.titleWidth),color:e.titleColor}),m:t.s(l.addStyle(e.titleStyle)),n:e.$slots.right||e.rightIcon||e.rightText},e.$slots.right||e.rightIcon||e.rightText?t.e({o:e.rightIcon},e.rightIcon?{p:t.p({name:e.rightIcon,size:"20"})}:{},{q:e.rightText},e.rightText?{r:t.t(e.rightText)}:{},{s:t.o(((...t)=>l.rightClick&&l.rightClick(...t)))}):{},{t:t.n(e.border&&"u-border-bottom"),v:l.addUnit(e.height),w:e.bgColor,x:t.n(e.fixed&&"u-navbar--fixed"),y:t.n(e.customClass)})}],["__scopeId","data-v-b902dd44"]]);wx.createComponent(s);
