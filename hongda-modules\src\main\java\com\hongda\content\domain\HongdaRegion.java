package com.hongda.content.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 地区管理对象 hongda_region
 * * <AUTHOR>
 * @date 2025-08-01
 */
public class HongdaRegion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 地区ID */
    private Long id;

    /** 地区名称 */
    @Excel(name = "地区名称")
    private String name;

    /** 地区代码 */
    @Excel(name = "地区代码")
    private String code;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** [新增] 关联的文章数量 (非数据库字段) */
    private Integer articleCount;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setSortOrder(Long sortOrder)
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder()
    {
        return sortOrder;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    /** [新增] articleCount 的 getter 和 setter */
    public Integer getArticleCount() {
        return articleCount;
    }

    public void setArticleCount(Integer articleCount) {
        this.articleCount = articleCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("code", getCode())
                .append("sortOrder", getSortOrder())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                // [新增]
                .append("articleCount", getArticleCount())
                .toString();
    }
}