"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),r=require("../../libs/util/async-validator.js"),s=require("../../libs/function/index.js"),n=require("../../libs/function/test.js"),l=require("../../../../common/vendor.js");r.Schema.warning=function(){};const o={name:"u-form",mixins:[t.mpMixin,i.mixin,e.props],provide(){return{uForm:this}},data:()=>({formRules:{},validator:{},originalModel:null}),watch:{rules:{immediate:!0,handler(e){this.setRules(e)}},propsChange(e){var t;(null==(t=this.children)?void 0:t.length)&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))},model:{immediate:!0,handler(e){this.originalModel||(this.originalModel=s.deepClone(e))}}},computed:{propsChange(){return[this.errorType,this.borderBottom,this.labelPosition,this.labelWidth,this.labelAlign,this.labelStyle]}},created(){this.children=[]},methods:{setRules(e){0!==Object.keys(e).length&&(this.formRules=e,this.validator=new r.Schema(e))},resetFields(){this.resetModel()},resetModel(e){this.children.map((e=>{const t=null==e?void 0:e.prop,i=s.getProperty(this.originalModel,t);s.setProperty(this.model,t,i)}))},clearValidate(e){e=[].concat(e),this.children.map((t=>{(void 0===e[0]||e.includes(t.prop))&&(t.message=null)}))},async validateField(e,t,i=null,l){this.$nextTick((()=>{const o=[];e=[].concat(e);let a=this.children.map((t=>new Promise(((a,h)=>{const c=[];if(e.includes(t.prop)){const e=s.getProperty(this.model,t.prop),h=t.prop.split("."),d=h[h.length-1];let u=[];if(u=t.itemRules&&t.itemRules.length>0?t.itemRules:this.formRules[t.prop],!u)return void a();const p=[].concat(u);p.length||a();for(let s=0;s<p.length;s++){const h=p[s],u=[].concat(null==h?void 0:h.trigger);if(i&&!u.includes(i)){a();continue}new r.Schema({[d]:h}).validate({[d]:e},((e,i)=>{var r;n.test.array(e)&&(e.forEach((e=>{e.prop=t.prop})),o.push(...e),c.push(...e)),l&&1!=(null==l?void 0:l.showErrorMsg)||(t.message=(null==(r=c[0])?void 0:r.message)?c[0].message:null),s==p.length-1&&a(o)}))}}else a({})}))));Promise.all(a).then((e=>{"function"==typeof t&&t(o)})).catch((e=>{console.error("An error occurred:",e)}))}))},validate(e){return new Promise(((t,i)=>{this.$nextTick((()=>{const r=this.children.map((e=>e.prop));this.validateField(r,(e=>{e.length?("toast"===this.errorType&&s.toast(e[0].message),i(e)):t(!0)}),null,e)}))}))}}};const a=l._export_sfc(o,[["render",function(e,t,i,r,s,n){return{}}]]);wx.createComponent(a);
