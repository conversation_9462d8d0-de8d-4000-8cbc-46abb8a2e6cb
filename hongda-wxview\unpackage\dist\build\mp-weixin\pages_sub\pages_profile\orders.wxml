<view class="orders-page data-v-f98ff934"><up-navbar wx:if="{{a}}" class="data-v-f98ff934" u-i="f98ff934-0" bind:__l="__l" u-p="{{a}}"/><scroll-view scroll-y class="orders-list-scroll data-v-f98ff934" bindscrolltolower="{{l}}" refresher-enabled refresher-triggered="{{m}}" bindrefresherrefresh="{{n}}"><view wx:if="{{b}}" class="loading-state data-v-f98ff934"><up-loading-page wx:if="{{c}}" class="data-v-f98ff934" u-i="f98ff934-1" bind:__l="__l" u-p="{{c}}"/></view><view wx:elif="{{d}}" class="empty-state data-v-f98ff934"><up-empty wx:if="{{h}}" class="data-v-f98ff934" u-s="{{['bottom']}}" u-i="f98ff934-2" bind:__l="__l" u-p="{{h}}"><up-button wx:if="{{e}}" class="data-v-f98ff934" bindclick="{{f}}" u-i="f98ff934-3,f98ff934-2" bind:__l="__l" u-p="{{g}}" slot="bottom"/></up-empty></view><view wx:else class="orders-list data-v-f98ff934"><view wx:for="{{i}}" wx:for-item="item" wx:key="m" class="order-card data-v-f98ff934" bindtap="{{item.n}}"><view class="card-left data-v-f98ff934"><image src="{{item.a}}" mode="aspectFill" class="event-image data-v-f98ff934" lazy-load="{{true}}" binderror="{{item.b}}" bindload="{{item.c}}"/></view><view class="card-right data-v-f98ff934"><text class="event-title data-v-f98ff934">{{item.d}}</text><view class="event-info registration-time data-v-f98ff934"><text class="time-label data-v-f98ff934">报名时间：</text><text class="time-value data-v-f98ff934">{{item.e}}</text></view><view class="card-bottom-row data-v-f98ff934"><view class="data-v-f98ff934"><view wx:if="{{item.f}}" class="status-with-bg data-v-f98ff934"><image class="status-bg-image data-v-f98ff934" src="{{item.g}}" mode="aspectFit"></image><text class="status-text data-v-f98ff934">已报名</text></view><view wx:elif="{{item.h}}" class="status-cancelled data-v-f98ff934"><text class="status-text data-v-f98ff934">已取消</text></view><view wx:else class="{{['data-v-f98ff934', 'registration-status-tag', item.j]}}">{{item.i}}</view></view></view></view><view wx:if="{{item.k}}" class="cancel-btn-absolute data-v-f98ff934" catchtap="{{item.l}}"> 取消报名 </view></view></view><view wx:if="{{j}}" class="loadmore-wrapper data-v-f98ff934"><up-loadmore wx:if="{{k}}" class="data-v-f98ff934" u-i="f98ff934-4" bind:__l="__l" u-p="{{k}}"/></view></scroll-view><custom-tab-bar wx:if="{{o}}" class="data-v-f98ff934" u-i="f98ff934-5" bind:__l="__l" u-p="{{o}}"/><view wx:if="{{p}}" class="cancel-modal-overlay data-v-f98ff934" bindtap="{{v}}"><view class="cancel-modal-content data-v-f98ff934" catchtap="{{t}}"><view class="modal-header data-v-f98ff934"><image class="warning-icon data-v-f98ff934" src="{{q}}" mode="aspectFit"></image><text class="modal-title data-v-f98ff934">操作提示</text></view><view class="modal-body data-v-f98ff934"><text class="modal-message data-v-f98ff934">是否取消报名？</text></view><view class="modal-footer data-v-f98ff934"><view class="modal-btn cancel-btn data-v-f98ff934" bindtap="{{r}}"> 暂不取消 </view><view class="modal-btn confirm-btn data-v-f98ff934" bindtap="{{s}}"> 确认取消 </view></view></view></view></view>