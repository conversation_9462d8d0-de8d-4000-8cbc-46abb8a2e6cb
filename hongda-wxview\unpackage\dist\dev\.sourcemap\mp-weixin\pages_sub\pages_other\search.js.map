{"version": 3, "file": "search.js", "sources": ["pages_sub/pages_other/search.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXHNlYXJjaC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <view class=\"custom-header\">\r\n      <view class=\"status-bar\"></view>\r\n      <view class=\"nav-bar\" :style=\"navBarStyle\">\r\n        <u-icon name=\"arrow-left\" size=\"22\" color=\"#303133\" @click=\"navigateBack\"></u-icon>\r\n        <view class=\"page-title-container\">\r\n          <text class=\"page-title\">红大出海</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"search-section\">\r\n        <view class=\"search-input-wrapper\">\r\n          <u-icon name=\"search\" color=\"#909399\" size=\"18\"></u-icon>\r\n          <input\r\n              class=\"search-input\"\r\n              v-model=\"keyword\"\r\n              placeholder=\"搜索活动、资讯\"\r\n              confirm-type=\"search\"\r\n              @confirm=\"handleSearch\"\r\n          />\r\n          <u-icon v-if=\"keyword\" @click=\"handleCancel\" name=\"close-circle-fill\" color=\"#c8c9cc\" size=\"18\"></u-icon>\r\n        </view>\r\n        <text v-if=\"keyword\" class=\"cancel-btn\" @click=\"handleCancel\">取消</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"tabs-container\">\r\n      <view class=\"custom-tabs\">\r\n        <view\r\n            v-for=\"(tab, index) in tabList\"\r\n            :key=\"index\"\r\n            class=\"tab-item\"\r\n            :class=\"{ 'active': currentTab === index }\"\r\n            @click=\"selectTab(index)\"\r\n            :style=\"currentTab === index ? activeTabStyle : {}\"\r\n        >\r\n          {{ tab.name }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"result-list-scroll\"  @scrolltolower=\"loadMoreData\">\r\n      <view v-if=\"isLoading\" class=\"loading-container\">\r\n        <u-loading-icon mode=\"circle\" text=\"正在搜索...\" size=\"24\"></u-loading-icon>\r\n      </view>\r\n      <view v-else>\r\n        <view v-if=\"displayedList.length === 0\" class=\"empty-state-container\">\r\n          <u-empty v-if=\"searchStatus === 'pristine'\" mode=\"search\" text=\"请输入关键词开始搜索\"></u-empty>\r\n          <u-empty v-else mode=\"data\" text=\"暂无相关结果\" marginTop=\"100\"></u-empty>\r\n        </view>\r\n        <view v-else class=\"result-list\">\r\n          <view\r\n              v-for=\"item in displayedList\"\r\n              :key=\"item.id\"\r\n              class=\"event-card\"\r\n              @click=\"goToDetail(item.id, currentTab)\">\r\n            <view class=\"card-left\">\r\n              <image class=\"event-image\" :src=\"item.coverUrl\" mode=\"aspectFill\" :lazy-load=\"true\"></image>\r\n              <view v-if=\"item.statusText && currentTab === 0\" :class=\"['status-tag', item.statusClass]\">\r\n                {{ item.statusText }}\r\n              </view>\r\n            </view>\r\n            <view class=\"card-right\">\r\n              <text class=\"event-title\">{{ item.title }}</text>\r\n              <view class=\"event-info-row\">\r\n                <view class=\"time-location-item\">\r\n                  <image class=\"event-info-icon\" :src=\"listTimeIconUrl\" mode=\"aspectFit\"></image>\r\n                  <text class=\"info-text\">{{ item.date }}</text>\r\n                </view>\r\n                <view class=\"time-location-item\">\r\n                  <image class=\"event-info-icon\" :src=\"listLocationIconUrl\" mode=\"aspectFit\"></image>\r\n                  <text class=\"info-text\">{{ item.location }}</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"event-info remaining-spots\">\r\n                <text class=\"spots-count\">\r\n                  {{ item.slotsPrefix }}: {{ item.slots }}\r\n                </text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <u-loadmore :status=\"loadStatus\" :line=\"true\" marginTop=\"20\"/>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {computed, onMounted, ref} from 'vue';\r\nimport {getArticleList} from '@/api/content/article.js';\r\nimport {searchEventsApi} from '@/api/data/event.js';\r\nimport {getFullImageUrl} from '@/utils/image.js';\r\nimport {formatEventLocation} from '@/utils/location.js';\r\n\r\nconst navBarStyle = ref({});\r\nconst keyword = ref('');\r\nconst currentTab = ref(0);\r\nconst isLoading = ref(false);\r\nconst searchStatus = ref('pristine');\r\nconst tabList = ref([{name: '活动'}, {name: '资讯'}]);\r\nconst activityResults = ref([]);\r\nconst newsResults = ref([]);\r\nconst pageNum = ref(1);\r\nconst pageSize = ref(10);\r\nconst loadStatus = ref('loadmore');\r\nconst hasMoreData = ref(true);\r\nconst assets = ref({}); // 【新增】用于存储静态资源\r\nconst listTimeIconUrl = ref(''); // 【新增】时间图标URL\r\nconst listLocationIconUrl = ref(''); // 【新增】地点图标URL\r\n\r\n// 【新增】动态计算激活Tab的背景样式\r\nconst activeTabStyle = computed(() => {\r\n  // 优先使用后台配置的URL，如果不存在，则使用旧的地址作为备用\r\n  const imageUrl = assets.value.bg_tab_active_search || 'http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png';\r\n  return {\r\n    backgroundImage: `url('${imageUrl}')`\r\n  };\r\n});\r\n\r\nonMounted(() => {\r\n  // 【新增】页面加载时，从全局缓存读取静态资源\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n  \r\n  // 【新增】初始化图标URL\r\n  listTimeIconUrl.value = assets.value?.list_time || '';\r\n  listLocationIconUrl.value = assets.value?.list_location || '';\r\n\r\n  // #ifdef MP-WEIXIN\r\n  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n  const systemInfo = uni.getSystemInfoSync();\r\n  const navHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2;\r\n  const navPaddingRight = systemInfo.windowWidth - menuButtonInfo.left;\r\n  navBarStyle.value = {\r\n    height: `${navHeight}px`,\r\n    'padding-right': `${navPaddingRight}px`,\r\n    'align-items': 'center'\r\n  };\r\n  // #endif\r\n});\r\n\r\nconst displayedList = computed(() => {\r\n  return currentTab.value === 0 ? activityResults.value : newsResults.value;\r\n});\r\n\r\n/**\r\n * 格式化活动数据，包括状态计算和地点格式化\r\n * @param {Object} event - 活动对象\r\n * @returns {Object} 格式化后的活动数据\r\n */\r\nconst formatEventData = (event) => {\r\n  // 计算报名状态\r\n  const getRegistrationStatus = () => {\r\n    const now = new Date();\r\n    const startTime = event.registrationStartTime ? new Date(event.registrationStartTime) : null;\r\n    const endTime = event.registrationEndTime ? new Date(event.registrationEndTime) : null;\r\n    \r\n    if (startTime && now < startTime) {\r\n      return { text: '即将开始', class: 'not-started' };\r\n    } else if (endTime && now > endTime) {\r\n      return { text: '报名截止', class: 'ended' };\r\n    } else {\r\n      return { text: '报名中', class: 'open' };\r\n    }\r\n  };\r\n  \r\n  const status = getRegistrationStatus();\r\n  \r\n  return {\r\n    id: event.id,\r\n    title: event.title,\r\n    date: event.startTime.split(' ')[0],\r\n    location: formatEventLocation(event), // 使用formatEventLocation函数获取市级信息\r\n    slots: (event.maxParticipants || 0) - (event.registeredCount || 0),\r\n    coverUrl: getFullImageUrl(event.coverImageUrl),\r\n    statusText: status.text,\r\n    statusClass: status.class,\r\n    slotsPrefix: '剩余名额'\r\n  };\r\n};\r\n\r\nconst formatArticleData = (article) => ({\r\n  id: article.id, title: article.title, date: article.publishTime.split(' ')[0],\r\n  location: article.source || '未知来源', slots: article.viewCount, coverUrl: getFullImageUrl(article.coverImageUrl),\r\n  statusText: '热门', statusClass: 'hot', slotsPrefix: '阅读量'\r\n});\r\n\r\nconst handleSearch = async () => {\r\n  if (!keyword.value.trim()) {\r\n    uni.showToast({ title: '请输入搜索内容', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  pageNum.value = 1;\r\n  activityResults.value = [];\r\n  newsResults.value = [];\r\n  hasMoreData.value = true;\r\n  loadStatus.value = 'loading';\r\n  isLoading.value = true;\r\n  searchStatus.value = 'searched';\r\n\r\n  try {\r\n    const [eventRes, articleRes] = await Promise.all([\r\n      searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value }),\r\n      getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value })\r\n    ]);\r\n\r\n    if (eventRes.code === 200 && eventRes.rows) {\r\n      activityResults.value = eventRes.rows.map(formatEventData);\r\n      hasMoreData.value = eventRes.rows.length >= pageSize.value;\r\n      loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';\r\n    } else {\r\n      activityResults.value = [];\r\n      hasMoreData.value = false;\r\n      loadStatus.value = 'nomore';\r\n    }\r\n\r\n    if (articleRes.code === 200 && articleRes.rows) {\r\n      newsResults.value = articleRes.rows.map(formatArticleData);\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('搜索失败:', error);\r\n    uni.showToast({ title: '搜索失败，请稍后重试', icon: 'none' });\r\n    loadStatus.value = 'loadmore';\r\n  } finally {\r\n    isLoading.value = false;\r\n  }\r\n};\r\n\r\nconst loadMoreData = async () => {\r\n  if (loadStatus.value !== 'loadmore' || !hasMoreData.value) {\r\n    return;\r\n  }\r\n\r\n  loadStatus.value = 'loading';\r\n  pageNum.value++;\r\n\r\n  try {\r\n    if (currentTab.value === 0) {\r\n      const res = await searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });\r\n      if (res.code === 200 && res.rows) {\r\n        activityResults.value.push(...res.rows.map(formatEventData));\r\n        hasMoreData.value = res.rows.length >= pageSize.value;\r\n        loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';\r\n      }\r\n    } else {\r\n      const res = await getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });\r\n      if (res.code === 200 && res.rows) {\r\n        newsResults.value.push(...res.rows.map(formatArticleData));\r\n        hasMoreData.value = res.rows.length >= pageSize.value;\r\n        loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('加载更多失败:', error);\r\n    loadStatus.value = 'loadmore';\r\n  }\r\n};\r\n\r\nconst handleCancel = () => {\r\n  keyword.value = '';\r\n  activityResults.value = [];\r\n  newsResults.value = [];\r\n  searchStatus.value = 'pristine';\r\n};\r\n\r\nconst selectTab = (index) => {\r\n  currentTab.value = index;\r\n  // 切换tab时，重置分页和加载状态\r\n  pageNum.value = 1;\r\n  hasMoreData.value = true;\r\n  if (displayedList.value.length < pageSize.value) {\r\n    loadStatus.value = 'nomore';\r\n    hasMoreData.value = false;\r\n  } else {\r\n    loadStatus.value = 'loadmore';\r\n  }\r\n};\r\n\r\nconst navigateBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst goToDetail = (id, tabIndex) => {\r\n  const url = tabIndex === 0\r\n      ? `/pages_sub/pages_event/detail?id=${id}`\r\n      : `/pages_sub/pages_article/detail?id=${id}`;\r\n  uni.navigateTo({ url });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.custom-header {\r\n  padding: 0 0 24rpx;\r\n  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n  height: 378rpx;\r\n  justify-content: space-around;\r\n}\r\n\r\n.status-bar {\r\n  height: var(--status-bar-height);\r\n}\r\n\r\n.nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.page-title-container {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.page-title {\r\n  font-size: 34rpx;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.search-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.search-input-wrapper {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  height: 72rpx;\r\n  padding: 0 24rpx;\r\n  background-color: #ffffff;\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 36rpx;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n}\r\n\r\n.cancel-btn {\r\n  color: #9B9A9A;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  padding: 20rpx 0;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.custom-tabs {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.tab-item {\r\n  padding: 16rpx 40rpx;\r\n  border-radius: 40rpx;\r\n  color: #23232A;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 活动按钮样式*/\r\n.tab-item:first-child {\r\n  width: 132rpx;\r\n  height: 60rpx;\r\n  font-size: 32rpx;\r\n  padding: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 资讯按钮样式*/\r\n.tab-item:last-child {\r\n  width: 132rpx;\r\n  height: 60rpx;\r\n  background: rgba(42, 97, 241, 0.1);\r\n  border-radius: 30rpx;\r\n  padding: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 32rpx;\r\n  color: #23232A;\r\n}\r\n\r\n.tab-item.active {\r\n  /* 【关键修改】移除 background-image */\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n}\r\n\r\n/* 激活状态下的资讯按钮 */\r\n.tab-item:last-child.active {\r\n  background: rgba(42, 97, 241, 1);\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 激活状态下的活动按钮 */\r\n.tab-item:first-child.active {\r\n  color: #23232A;\r\n}\r\n\r\n.result-list-scroll {\r\n  flex: 1;\r\n  height: 0;\r\n  background-color: #FFFFFF;\r\n  /* 隐藏滚动条 */\r\n  ::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n  scrollbar-width: none; /* Firefox */\r\n  -ms-overflow-style: none; /* IE 10+ */\r\n}\r\n\r\n.result-list {\r\n  padding: 0;\r\n}\r\n\r\n.loading-container, .empty-state-container {\r\n  padding-top: 200rpx;\r\n}\r\n\r\n.event-card {\r\n  width: 100%;\r\n  height: 272rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\r\n  border: none;\r\n  border-top: 2rpx solid #EEEEEE;\r\n  border-bottom: 2rpx solid #EEEEEE;\r\n  margin-bottom: 0rpx;\r\n  padding: 24rpx 24rpx;\r\n  display: flex;\r\n  overflow: hidden;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.card-left {\r\n  position: relative;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  flex-shrink: 0;\r\n  margin-top: 16rpx;\r\n  margin-bottom: 16rpx;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n  border-radius: 16rpx;\r\n}\r\n\r\n.status-tag {\r\n  position: absolute;\r\n  top: 12rpx;\r\n  left: 12rpx;\r\n  width: 96rpx;\r\n  height: 44rpx;\r\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n  border-radius: 22rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-sizing: border-box;\r\n  padding: 6rpx 12rpx;\r\n  color: #23232A;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n  font-weight: 500;\r\n  font-size: 22rpx;\r\n  text-align: center;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: 1.2;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n\r\n  &.ended {\r\n    background: #9B9A9A;\r\n    color: #FFFFFF;\r\n    width: 116rpx;\r\n  }\r\n  \r\n  &.not-started {\r\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n    color: #23232A;\r\n    width: 116rpx;\r\n  }\r\n  \r\n  &.open {\r\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n    color: #23232A;\r\n  }\r\n}\r\n\r\n.card-right {\r\n  flex: 1;\r\n  padding: 16rpx 20rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.event-title {\r\n  width: 346rpx;\r\n  height: 80rpx;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n  font-weight: normal;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  text-align: justify;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: 1.4;\r\n  margin-bottom: 24rpx;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  line-clamp: 2;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.event-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.event-info-row {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: flex-start !important;\r\n  gap: 24rpx !important;\r\n  margin-bottom: 18rpx !important;\r\n  flex-wrap: nowrap !important;\r\n}\r\n\r\n.time-location-item {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 8rpx !important;\r\n  flex-shrink: 0 !important;\r\n}\r\n\r\n.event-info-icon {\r\n  width: 32rpx !important;\r\n  height: 32rpx !important;\r\n  flex-shrink: 0 !important;\r\n}\r\n\r\n.info-text {\r\n  width: 176rpx !important;\r\n  height: 32rpx !important;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;\r\n  font-weight: normal !important;\r\n  font-size: 22rpx !important;\r\n  color: #9B9A9A !important;\r\n  text-align: left !important;\r\n  font-style: normal !important;\r\n  text-transform: none !important;\r\n  line-height: 32rpx !important;\r\n  overflow: hidden !important;\r\n  text-overflow: ellipsis !important;\r\n  white-space: nowrap !important;\r\n}\r\n\r\n.remaining-spots {\r\n  width: 154rpx;\r\n  height: 40rpx;\r\n  border-radius: 4rpx 4rpx 4rpx 4rpx;\r\n  border: 1rpx solid #FB8620;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0;\r\n  margin: 0;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n  flex-shrink: 0;\r\n\r\n  .spots-count {\r\n    width: 100%;\r\n    height: 36rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n    font-weight: normal;\r\n    font-size: 20rpx;\r\n    color: #FB8620;\r\n    text-align: center;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    line-height: 36rpx;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n}\r\n\r\n/* 状态标签样式 */\r\n.status-tag.ended {\r\n  width: 116rpx; /* 报名截止 */\r\n  background-color: #ff4757;\r\n}\r\n\r\n.status-tag.not-started {\r\n  width: 116rpx; /* 即将开始 */\r\n  background-color: #ffa502;\r\n}\r\n\r\n.status-tag.open {\r\n  /* 报名中 - 保持默认宽度 */\r\n  background-color: #2ed573;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/search.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "formatEventLocation", "getFullImageUrl", "searchEventsApi", "getArticleList"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAgGA,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAAA,IAAI,UAAU;AACnC,UAAM,UAAUA,cAAAA,IAAI,CAAC,EAAC,MAAM,KAAI,GAAG,EAAC,MAAM,KAAI,CAAC,CAAC;AAChD,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAC9B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,CAAC;AACrB,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,aAAaA,cAAAA,IAAI,UAAU;AACjC,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAC5B,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AACrB,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,sBAAsBA,cAAAA,IAAI,EAAE;AAGlC,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AAEpC,YAAM,WAAW,OAAO,MAAM,wBAAwB;AACtD,aAAO;AAAA,QACL,iBAAiB,QAAQ,QAAQ;AAAA,MACrC;AAAA,IACA,CAAC;AAEDC,kBAAAA,UAAU,MAAM;;AAEd,aAAO,QAAQC,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA;AAGrD,sBAAgB,UAAQ,YAAO,UAAP,mBAAc,cAAa;AACnD,0BAAoB,UAAQ,YAAO,UAAP,mBAAc,kBAAiB;AAG3D,YAAM,iBAAiBA,oBAAI;AAC3B,YAAM,aAAaA,oBAAI;AACvB,YAAM,YAAY,eAAe,UAAU,eAAe,MAAM,WAAW,mBAAmB;AAC9F,YAAM,kBAAkB,WAAW,cAAc,eAAe;AAChE,kBAAY,QAAQ;AAAA,QAClB,QAAQ,GAAG,SAAS;AAAA,QACpB,iBAAiB,GAAG,eAAe;AAAA,QACnC,eAAe;AAAA,MACnB;AAAA,IAEA,CAAC;AAED,UAAM,gBAAgBF,cAAQ,SAAC,MAAM;AACnC,aAAO,WAAW,UAAU,IAAI,gBAAgB,QAAQ,YAAY;AAAA,IACtE,CAAC;AAOD,UAAM,kBAAkB,CAAC,UAAU;AAEjC,YAAM,wBAAwB,MAAM;AAClC,cAAM,MAAM,oBAAI;AAChB,cAAM,YAAY,MAAM,wBAAwB,IAAI,KAAK,MAAM,qBAAqB,IAAI;AACxF,cAAM,UAAU,MAAM,sBAAsB,IAAI,KAAK,MAAM,mBAAmB,IAAI;AAElF,YAAI,aAAa,MAAM,WAAW;AAChC,iBAAO,EAAE,MAAM,QAAQ,OAAO,cAAa;AAAA,QACjD,WAAe,WAAW,MAAM,SAAS;AACnC,iBAAO,EAAE,MAAM,QAAQ,OAAO,QAAO;AAAA,QAC3C,OAAW;AACL,iBAAO,EAAE,MAAM,OAAO,OAAO,OAAM;AAAA,QACpC;AAAA,MACL;AAEE,YAAM,SAAS;AAEf,aAAO;AAAA,QACL,IAAI,MAAM;AAAA,QACV,OAAO,MAAM;AAAA,QACb,MAAM,MAAM,UAAU,MAAM,GAAG,EAAE,CAAC;AAAA,QAClC,UAAUG,eAAmB,oBAAC,KAAK;AAAA;AAAA,QACnC,QAAQ,MAAM,mBAAmB,MAAM,MAAM,mBAAmB;AAAA,QAChE,UAAUC,YAAAA,gBAAgB,MAAM,aAAa;AAAA,QAC7C,YAAY,OAAO;AAAA,QACnB,aAAa,OAAO;AAAA,QACpB,aAAa;AAAA,MACjB;AAAA,IACA;AAEA,UAAM,oBAAoB,CAAC,aAAa;AAAA,MACtC,IAAI,QAAQ;AAAA,MAAI,OAAO,QAAQ;AAAA,MAAO,MAAM,QAAQ,YAAY,MAAM,GAAG,EAAE,CAAC;AAAA,MAC5E,UAAU,QAAQ,UAAU;AAAA,MAAQ,OAAO,QAAQ;AAAA,MAAW,UAAUA,4BAAgB,QAAQ,aAAa;AAAA,MAC7G,YAAY;AAAA,MAAM,aAAa;AAAA,MAAO,aAAa;AAAA,IACrD;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,QAAQ,MAAM,QAAQ;AACzBF,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAChD;AAAA,MACD;AAED,cAAQ,QAAQ;AAChB,sBAAgB,QAAQ;AACxB,kBAAY,QAAQ;AACpB,kBAAY,QAAQ;AACpB,iBAAW,QAAQ;AACnB,gBAAU,QAAQ;AAClB,mBAAa,QAAQ;AAErB,UAAI;AACF,cAAM,CAAC,UAAU,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC/CG,eAAAA,gBAAgB,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAK,CAAE;AAAA,UAC1FC,oBAAAA,eAAe,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAK,CAAE;AAAA,QAC/F,CAAK;AAED,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,0BAAgB,QAAQ,SAAS,KAAK,IAAI,eAAe;AACzD,sBAAY,QAAQ,SAAS,KAAK,UAAU,SAAS;AACrD,qBAAW,QAAQ,YAAY,QAAQ,aAAa;AAAA,QAC1D,OAAW;AACL,0BAAgB,QAAQ;AACxB,sBAAY,QAAQ;AACpB,qBAAW,QAAQ;AAAA,QACpB;AAED,YAAI,WAAW,SAAS,OAAO,WAAW,MAAM;AAC9C,sBAAY,QAAQ,WAAW,KAAK,IAAI,iBAAiB;AAAA,QAC1D;AAAA,MAEF,SAAQ,OAAO;AACdJ,sBAAA,MAAA,MAAA,SAAA,2CAAc,SAAS,KAAK;AAC5BA,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAM,CAAE;AACnD,mBAAW,QAAQ;AAAA,MACvB,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI,WAAW,UAAU,cAAc,CAAC,YAAY,OAAO;AACzD;AAAA,MACD;AAED,iBAAW,QAAQ;AACnB,cAAQ;AAER,UAAI;AACF,YAAI,WAAW,UAAU,GAAG;AAC1B,gBAAM,MAAM,MAAMG,eAAAA,gBAAgB,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAO,CAAA;AAC5G,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,4BAAgB,MAAM,KAAK,GAAG,IAAI,KAAK,IAAI,eAAe,CAAC;AAC3D,wBAAY,QAAQ,IAAI,KAAK,UAAU,SAAS;AAChD,uBAAW,QAAQ,YAAY,QAAQ,aAAa;AAAA,UACrD;AAAA,QACP,OAAW;AACL,gBAAM,MAAM,MAAMC,oBAAAA,eAAe,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAO,CAAA;AAC3G,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,wBAAY,MAAM,KAAK,GAAG,IAAI,KAAK,IAAI,iBAAiB,CAAC;AACzD,wBAAY,QAAQ,IAAI,KAAK,UAAU,SAAS;AAChD,uBAAW,QAAQ,YAAY,QAAQ,aAAa;AAAA,UACrD;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAA,MAAA,MAAA,SAAA,2CAAc,WAAW,KAAK;AAC9B,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzB,cAAQ,QAAQ;AAChB,sBAAgB,QAAQ;AACxB,kBAAY,QAAQ;AACpB,mBAAa,QAAQ;AAAA,IACvB;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAEnB,cAAQ,QAAQ;AAChB,kBAAY,QAAQ;AACpB,UAAI,cAAc,MAAM,SAAS,SAAS,OAAO;AAC/C,mBAAW,QAAQ;AACnB,oBAAY,QAAQ;AAAA,MACxB,OAAS;AACL,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,IAAI,aAAa;AACnC,YAAM,MAAM,aAAa,IACnB,oCAAoC,EAAE,KACtC,sCAAsC,EAAE;AAC9CA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjSA,GAAG,WAAW,eAAe;"}