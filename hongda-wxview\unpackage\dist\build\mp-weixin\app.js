"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("./common/vendor.js"),e=require("./api/platform/asset.js"),s=require("./uni_modules/uview-plus/index.js");Math;const t={onLaunch:function(){console.log("App Launch"),this.fetchAndCacheAssets(),this.loadCdnFonts(),this.loadIconFont()},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")},methods:{loadCdnFonts(){const e=[{family:"Alibaba PuHuiTi 3.0",source:'url("https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/AlibabaPuHuiTi-3-55-Regular.subset.woff2")'},{family:"YouSheBiaoTiHei",source:'url("https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/YouSheBiaoTiHei.subset.woff2")'}];let s=0;const t=e.length;e.forEach((e=>{o.index.loadFontFace({global:!0,family:e.family,source:e.source,success:()=>{console.log(`CDN字体 [${e.family}] 加载成功!`),s++,s===t&&(this.globalData=this.globalData||{},this.globalData.fontLoaded=!0,o.index.$emit("customFontsLoaded"),console.log("所有自定义字体加载完成，已触发全局事件"))},fail(a){console.error(`CDN字体 [${e.family}] 加载失败:`,a),s++,s===t&&(this.globalData=this.globalData||{},this.globalData.fontLoaded=!0,o.index.$emit("customFontsLoaded"),console.log("字体加载处理完成（部分可能失败），已触发全局事件"))}})}))},async fetchAndCacheAssets(){try{const s=await e.getAllAssets();if(200===s.code&&Array.isArray(s.data)){const e=s.data.reduce(((o,e)=>(e.assetKey&&e.assetUrl&&(o[e.assetKey]=e.assetUrl),o)),{});o.index.setStorageSync("staticAssets",e),console.log("小程序图片等静态资源已更新并缓存成功！")}}catch(s){console.error("获取小程序静态资源失败",s)}},loadIconFont(){o.index.loadFontFace({global:!0,family:"uicon-iconfont",source:'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',success(){console.log("uview-plus图标字体加载成功")},fail(e){console.error("uview-plus图标字体加载失败:",e),o.index.loadFontFace({global:!0,family:"uicon-iconfont",source:'url("https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf")',success(){console.log("备用图标字体加载成功")},fail(o){console.error("备用图标字体也加载失败:",o)}})}})}}};function a(){const e=o.createSSRApp(t);return e.use(s.uviewPlus,(()=>({options:{config:{unit:"rpx"},props:{}}}))),{app:e}}a().app.mount("#app"),exports.createApp=a;
