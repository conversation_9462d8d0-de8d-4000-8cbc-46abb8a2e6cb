<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaRegionMapper">

    <resultMap type="com.hongda.content.domain.HongdaRegion" id="HongdaRegionResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="articleCount" column="article_count" />
    </resultMap>

    <sql id="selectHongdaRegionVo">
        select id, name, code, sort_order, status, create_by, create_time, update_by, update_time, remark from hongda_region
    </sql>

    <select id="selectHongdaRegionList" parameterType="com.hongda.content.domain.HongdaRegion" resultMap="HongdaRegionResult">
        SELECT r.id, r.name, r.code, r.sort_order, r.status, r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
        (SELECT count(a.id) FROM hongda_article a WHERE a.region = r.code AND a.status != '2') as article_count
        FROM hongda_region r
        <where>
            <if test="name != null  and name != ''"> and r.name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and r.code = #{code}</if>
        </where>
    </select>

    <select id="selectHongdaRegionById" parameterType="Long" resultMap="HongdaRegionResult">
        <include refid="selectHongdaRegionVo"/>
        where id = #{id}
    </select>

    <select id="selectHongdaRegionByIds" parameterType="java.lang.Long" resultMap="HongdaRegionResult">
        <include refid="selectHongdaRegionVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertHongdaRegion" parameterType="com.hongda.content.domain.HongdaRegion" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHongdaRegion" parameterType="com.hongda.content.domain.HongdaRegion">
        update hongda_region
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaRegionById" parameterType="Long">
        delete from hongda_region where id = #{id}
    </delete>

    <delete id="deleteHongdaRegionByIds" parameterType="java.lang.Long">
        delete from hongda_region where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countArticlesByRegion" resultType="java.lang.Integer">
        SELECT count(id)
        FROM hongda_article
        WHERE region = #{regionCode} AND status != '2'
    </select>
</mapper>