"use strict";const p=require("../../libs/vue.js"),o=require("../../libs/config/props.js"),e=p.defineMixin({props:{show:{type:Boolean,default:()=>o.props.popup.show},overlay:{type:Boolean,default:()=>o.props.popup.overlay},mode:{type:String,default:()=>o.props.popup.mode},duration:{type:[String,Number],default:()=>o.props.popup.duration},closeable:{type:<PERSON>olean,default:()=>o.props.popup.closeable},overlayStyle:{type:[Object,String],default:()=>o.props.popup.overlayStyle},closeOnClickOverlay:{type:Boolean,default:()=>o.props.popup.closeOnClickOverlay},zIndex:{type:[String,Number],default:()=>o.props.popup.zIndex},safeAreaInsetBottom:{type:Boolean,default:()=>o.props.popup.safeAreaInsetBottom},safeAreaInsetTop:{type:Boolean,default:()=>o.props.popup.safeAreaInsetTop},closeIconPos:{type:String,default:()=>o.props.popup.closeIconPos},round:{type:[Boolean,String,Number],default:()=>o.props.popup.round},zoom:{type:Boolean,default:()=>o.props.popup.zoom},bgColor:{type:String,default:()=>o.props.popup.bgColor},overlayOpacity:{type:[Number,String],default:()=>o.props.popup.overlayOpacity}}});exports.props=e;
