"use strict";var e=function(){function e(e,t){return null!=t&&e instanceof t}var t,r,n;try{t=Map}catch(f){t=function(){}}try{r=Set}catch(f){r=function(){}}try{n=Promise}catch(f){n=function(){}}function o(c,f,u,a,p){"object"==typeof f&&(u=f.depth,a=f.prototype,p=f.includeNonEnumerable,f=f.circular);var l=[],s=[],y="undefined"!=typeof Buffer;return void 0===f&&(f=!0),void 0===u&&(u=1/0),function c(u,b){if(null===u)return null;if(0===b)return u;var g,j;if("object"!=typeof u)return u;if(e(u,t))g=new t;else if(e(u,r))g=new r;else if(e(u,n))g=new n((function(e,t){u.then((function(t){e(c(t,b-1))}),(function(e){t(c(e,b-1))}))}));else if(o.__isArray(u))g=[];else if(o.__isRegExp(u))g=new RegExp(u.source,i(u)),u.lastIndex&&(g.lastIndex=u.lastIndex);else if(o.__isDate(u))g=new Date(u.getTime());else{if(y&&Buffer.isBuffer(u))return Buffer.from?g=Buffer.from(u):(g=new Buffer(u.length),u.copy(g)),g;e(u,Error)?g=Object.create(u):void 0===a?(j=Object.getPrototypeOf(u),g=Object.create(j)):(g=Object.create(a),j=a)}if(f){var O=l.indexOf(u);if(-1!=O)return s[O];l.push(u),s.push(g)}for(var d in e(u,t)&&u.forEach((function(e,t){var r=c(t,b-1),n=c(e,b-1);g.set(r,n)})),e(u,r)&&u.forEach((function(e){var t=c(e,b-1);g.add(t)})),u){Object.getOwnPropertyDescriptor(u,d)&&(g[d]=c(u[d],b-1));try{if("undefined"===Object.getOwnPropertyDescriptor(u,d).set)continue;g[d]=c(u[d],b-1)}catch(P){if(P instanceof TypeError)continue;if(P instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var v=Object.getOwnPropertySymbols(u);for(d=0;d<v.length;d++){var _=v[d];(!(h=Object.getOwnPropertyDescriptor(u,_))||h.enumerable||p)&&(g[_]=c(u[_],b-1),Object.defineProperty(g,_,h))}}if(p){var w=Object.getOwnPropertyNames(u);for(d=0;d<w.length;d++){var h,m=w[d];(h=Object.getOwnPropertyDescriptor(u,m))&&h.enumerable||(g[m]=c(u[m],b-1),Object.defineProperty(g,m,h))}}return g}(c,u)}function c(e){return Object.prototype.toString.call(e)}function i(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return o.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},o.__objToStr=c,o.__isDate=function(e){return"object"==typeof e&&"[object Date]"===c(e)},o.__isArray=function(e){return"object"==typeof e&&"[object Array]"===c(e)},o.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===c(e)},o.__getRegExpFlags=i,o}();exports.clone=e;
