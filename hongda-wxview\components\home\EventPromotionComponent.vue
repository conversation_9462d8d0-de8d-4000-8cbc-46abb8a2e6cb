<template>
	<view class="promo-container">		
		<!-- 骨架屏 - 加载时显示 -->
		<view v-if="isLoading" class="skeleton-wrapper">
			<u-skeleton
				:loading="true"
				:animate="true"
				:rows="4"
				:title="true"
				titleWidth="60%"
				rowsWidth="['100%', '40%', '40%', '100%']"
				rowsHeight="['180px', '20px', '20px', '40px']"
			></u-skeleton>
		</view>

		<view v-else-if="promoDataList.length > 0" class="promo-card-wrapper">
			<!-- 轮播区域 -->
			<swiper 
				ref="swiperRef"
				class="promo-swiper"
				:indicator-dots="false"
				:autoplay="true"
				:interval="4000"
				:duration="500"
				:circular="true"
				:current="currentIndex"
				@change="onSwiperChange"
			>
				<swiper-item 
					v-for="(item, index) in promoDataList" 
					:key="item.id || index"
					class="swiper-item"
				>
					<view class="promo-card" @click="handlePromoClick(item)">
						<!-- 1. 顶部标题 -->
						<view class="promo-header">
							<image v-if="item.iconUrl" :src="getFullImageUrl(item.iconUrl)" class="promo-icon" mode="aspectFill"></image>
							<text class="promo-title">{{ item.title }}</text>
						</view>

						<!-- 2. 中间主图 -->
					<view class="image-container">
						<image 
							class="promo-main-image" 
							:src="item.image" 
							mode="aspectFill"
							:lazy-load="true"
							@error="onImageError"
							@load="onImageLoad"
						></image>
					</view>
						<!-- 3. 简介信息 -->
							<view class="promo-description">
								<view v-if="item.descriptionLine1" class="desc-item">
									<image class="desc-icon" :src="flameIconUrl" mode="aspectFit"></image>
									<text class="desc-text">{{ item.descriptionLine1 }}</text>
								</view>
								<view v-if="item.descriptionLine2" class="desc-item">
									<image class="desc-icon" :src="thumbUpIconUrl" mode="aspectFit"></image>
									<text class="desc-text">{{ item.descriptionLine2 }}</text>
								</view>
							</view>
						
						<!-- 4. 底部按钮 -->
						<view class="promo-footer">
							<up-button 
								type="primary" 
								shape="square" 
								text="立即报名"
								size="large"
                            @click="handlePromoClick(item)"
								:customStyle="{ 
									backgroundColor: '#023F98',
									height: '68rpx',
									width: '654rpx',
									borderRadius: '8rpx',
									margin: '0 auto',
									border: 'none'
									}"
							></up-button>
						</view>
					</view>
				</swiper-item>
			</swiper>
			
			<!-- 自定义指示器-->
			<view v-if="promoDataList.length > 1" class="custom-indicators">
				<view 
					v-for="(item, index) in promoDataList" 
					:key="index"
					class="indicator-dot"
					:class="{ 'active': currentIndex === index }"
					@click="switchToSlide(index)"
				></view>
			</view>
		</view>
		
		<view v-else class="no-data-tip" style="padding: 40rpx; text-align: center; color: #999;">
			<text>暂无推广活动</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getPromotionEventListApi } from '@/api/data/event.js';
import { getFullImageUrl } from '@/utils/image.js';

// 静态资源 URL（仅暗号读取）
const flameIconUrl = ref('')
const thumbUpIconUrl = ref('')

// --- 响应式状态 ---
const promoDataList = ref([]); 
const isLoading = ref(true);
const currentIndex = ref(0); 

/**
 * 获取推广活动数据
 */
const fetchPromoData = async () => {
	try {
		// 调用新的活动推广API
		const response = await getPromotionEventListApi({
			pageSize: 10
		});
		
		if (response && response.code === 200) {
			// 兼容两种数据格式：{rows: []} 和 {data: {rows: []}}
			let dataArray = null;
			if (response.rows && Array.isArray(response.rows)) {
				dataArray = response.rows;
			} else if (response.data && response.data.rows && Array.isArray(response.data.rows)) {
				dataArray = response.data.rows;
			}
			
			if (dataArray && Array.isArray(dataArray) && dataArray.length > 0) {
				// 处理活动推广数据
				promoDataList.value = dataArray.map(event => {
					return {
						id: event.id,
						title: event.promotionTitle || event.title, // 优先使用推广标题
						image: getFullImageUrl(event.promotionImageUrl || event.coverImageUrl), // 优先使用推广图片
						iconUrl: event.iconUrl, 
						linkUrl: `/pages_sub/pages_event/detail?id=${event.id}`, // 直接跳转到活动详情
						// 使用活动的简介和卖点字段，如果为空则提供默认文案
						descriptionLine1: event.summary || '官方认证，品质保证',
						descriptionLine2: event.sellPoint || '干货满满，不容错过'
					};
				});
				
				console.log('获取推广活动成功，数量:', promoDataList.value.length);
			} else {
				promoDataList.value = [];
				console.warn('暂无推广活动数据');
			}
		} else {
			promoDataList.value = [];
			console.warn('获取推广活动失败，响应码:', response?.code);
		}
		
	} catch (error) {
		console.error('获取推广数据失败:', error.message);
		promoDataList.value = [];
	} finally {
		isLoading.value = false;
	}
};

/**
 * 轮播切换事件处理
 */
const onSwiperChange = (e) => {
	currentIndex.value = e.detail.current;
};

/**
 * 点击指示器切换轮播
 */
const switchToSlide = (index) => {
	currentIndex.value = index;
	console.log('点击指示器切换到索引:', index);
};

/**
 * 处理卡片点击事件
 */
/**
 * 处理卡片点击事件 - 简化版
 */
const handlePromoClick = (promoItem) => {
    if (!promoItem || !promoItem.id) {
        console.warn('推广卡片数据异常');
        return;
    }

    // 直接跳转到活动详情页
    uni.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${promoItem.id}`
    });
};

/**
 * 图片加载成功事件
 */
const onImageLoad = (e) => {
	console.log('图片加载成功');
};

/**
 * 图片加载失败事件
 */
const onImageError = (e) => {
	console.error('图片加载失败:', e);
};

// --- 生命周期 ---
onMounted(() => {
	// 读取静态资源配置
	try {
    const assets = uni.getStorageSync('staticAssets')
    flameIconUrl.value = assets?.['flame-icon'] || ''
    thumbUpIconUrl.value = assets?.['thumb-up-icon'] || ''
	} catch (e) {}

	fetchPromoData();
});
</script>

<style lang="scss" scoped>
.promo-container {
	width: 702rpx;
    height: auto; 
	background: linear-gradient(180deg, rgba(2,63,152,0.1) 0%, rgba(2,63,152,0) 100%);
	border-radius: 32rpx 32rpx 32rpx 32rpx;
    margin: 24rpx 24rpx 0 24rpx; 
}

.skeleton-wrapper {
	padding: 24rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	height: 100%;
}

// 轮播卡片容器
.promo-card-wrapper {
	border-radius: 16rpx 16rpx 0 0;
	box-shadow: none;
    height: auto;
    padding-bottom: 16rpx;
}
// 轮播组件样式
.promo-swiper {
	width: 100%;
    height: 560rpx; 
    position: relative;
    z-index: 2; 
}

.swiper-item {
	width: 100%;
	height: 100%; 
}

.promo-card {
	padding: 24rpx;
	width: 100%;
	height: 100%; 
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.promo-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	flex-shrink: 0;
	
	.promo-icon {
		width: 44rpx;
		height: 44rpx;
		border-radius: 50%;
		margin-right: 12rpx;
		flex-shrink: 0;
	}
	
	.promo-title {
		width: 552rpx;
		height: 44rpx;
		font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
		font-weight: normal;
		font-size: 32rpx;
		color: #23232A;
		text-align: left;
		font-style: normal;
		text-transform: none;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex: 1;
		line-height: 44rpx;
	}
}

.promo-main-image {
	width: 100%;
	max-height: 200rpx; 
	border-radius: 12rpx;
	display: block;
	object-fit: cover; 
	flex-shrink: 0; 
}

.image-container {
	background: #FFFFFF; 
	border-radius: 16rpx; 
	padding: 24rpx; 
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08); 
}

.promo-description {
	margin: 24rpx 0;
	flex: 1;
	
	.desc-item {
		display: flex;
		align-items: flex-start; 
		margin-bottom: 16rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.desc-icon {
	    width: 36rpx;
	    height: 36rpx;
	    flex-shrink: 0; 
	}
	
	.desc-text {
		font-size: 28rpx;
		color: #606266;
		margin-left: 12rpx;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-box-orient: vertical;
      line-clamp: 2; 
		-webkit-line-clamp: 2; 
		overflow: hidden;
	}
}

.promo-footer {
	flex-shrink: 0; 
	margin-top: auto; 
  position: relative;
  z-index: 2;
    margin-bottom: 24rpx; 
}

// 自定义指示器样式 - 位于立即报名按钮下方
.custom-indicators {
    display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 0;
        padding: 0;
        gap: 12rpx;
        position: relative;
        z-index: 1;
        pointer-events: none;
}

.indicator-dot {
    width: 18rpx;
        height: 8rpx;
        border-radius: 4rpx;
    	background-color: #e4e7ed; 
    	transition: background-color 0.3s ease;
        pointer-events: auto;
        flex-shrink: 0;
	
	/* 激活状态 */
	&.active {
		background-color: #004085;
	}
}
</style>
