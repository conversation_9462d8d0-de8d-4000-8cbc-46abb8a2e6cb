"use strict";const t=require("../../../../common/vendor.js"),e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),n=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),r={name:"u-subsection",mixins:[i.mpMixin,n.mixin,e.props],data:()=>({itemRect:{width:0,height:0},innerCurrent:"",windowResizeCallback:{}}),watch:{list(t,e){this.init()},current:{immediate:!0,handler(t){t!==this.innerCurrent&&(this.innerCurrent=t)}}},computed:{wrapperStyle(){const t={};return"button"===this.mode&&(t.backgroundColor=this.bgColor),t},barStyle(){const t={};return t.width=`${this.itemRect.width}px`,t.height=`${this.itemRect.height}px`,t.transform=`translateX(${this.innerCurrent*this.itemRect.width}px)`,"subsection"===this.mode&&(t.backgroundColor=this.activeColor),t},itemStyle(t){return t=>{const e={};return"subsection"===this.mode&&(e.borderColor=this.activeColor,e.borderWidth="1px",e.borderStyle="solid"),e}},textStyle(t,e){return(t,e)=>{const i={};i.fontWeight=this.bold&&this.innerCurrent===t?"bold":"normal",i.fontSize=s.addUnit(this.fontSize);let n=null,r=null;return"object"==typeof e&&e[this.activeColorKeyName]&&(n=e[this.activeColorKeyName]),"object"==typeof e&&e[this.inactiveColorKeyName]&&(r=e[this.inactiveColorKeyName]),"subsection"===this.mode?this.innerCurrent===t?i.color=n||"#FFF":i.color=r||this.inactiveColor:this.innerCurrent===t?i.color=n||this.activeColor:i.color=r||this.inactiveColor,i}}},mounted(){this.init(),this.windowResizeCallback=t=>{this.init()},t.index.onWindowResize(this.windowResizeCallback)},beforeUnmount(){t.index.offWindowResize(this.windowResizeCallback)},emits:["change","update:current"],methods:{addStyle:s.addStyle,init(){this.innerCurrent=this.current,s.sleep().then((()=>this.getRect()))},getText(t){return"object"==typeof t?t[this.keyName]:t},getRect(){this.$uGetRect(".u-subsection__item--0").then((t=>{this.itemRect=t}))},clickHandler(t){this.disabled||(this.innerCurrent=t,this.$emit("update:current",t),this.$emit("change",t))},getTextViewDisableClass(t){return this.disabled?"button"===this.mode?"item-button--disabled":"item-subsection--disabled":""}}};const o=t._export_sfc(r,[["render",function(e,i,n,s,r,o){return{a:t.s(o.barStyle),b:t.n("button"===e.mode&&"u-subsection--button__bar"),c:t.n(0===r.innerCurrent&&"subsection"===e.mode&&"u-subsection__bar--first"),d:t.n(r.innerCurrent>0&&r.innerCurrent<e.list.length-1&&"subsection"===e.mode&&"u-subsection__bar--center"),e:t.n(r.innerCurrent===e.list.length-1&&"subsection"===e.mode&&"u-subsection__bar--last"),f:t.f(e.list,((i,n,s)=>({a:t.t(o.getText(i)),b:t.s(o.textStyle(n,i)),c:t.n(`u-subsection__item--${n}`),d:t.n(n<e.list.length-1&&"u-subsection__item--no-border-right"),e:t.n(0===n&&"u-subsection__item--first"),f:t.n(n===e.list.length-1&&"u-subsection__item--last"),g:t.n(o.getTextViewDisableClass(n)),h:`u-subsection__item--${n}`,i:t.s(o.itemStyle(n)),j:t.o((t=>o.clickHandler(n)),n),k:n}))),g:t.n(e.disabled?"u-subsection--disabled":""),h:t.n(`u-subsection--${e.mode}`),i:t.s(o.addStyle(e.customStyle)),j:t.s(o.wrapperStyle)}}],["__scopeId","data-v-89138a42"]]);wx.createComponent(o);
