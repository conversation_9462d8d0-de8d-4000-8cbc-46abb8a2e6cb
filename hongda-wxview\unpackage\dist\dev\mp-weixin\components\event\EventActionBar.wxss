/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-a955827f {
  height: 100%;
  background-color: #f4f4f4;
}
.bottom-action-bar.data-v-a955827f {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 100;
  /* 兼容性处理：为不支持env()的环境提供兜底值 */
  height: 156rpx;
  height: calc(156rpx + env(safe-area-inset-bottom, 0rpx));
  background-color: #FFFFFF;
  border-top: 2rpx solid #EEEEEE;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  /* 兼容性处理：为不支持env()的环境提供兜底值 */
  padding-bottom: 0rpx;
  padding-bottom: env(safe-area-inset-bottom, 0rpx);
}
.share-icon.data-v-a955827f {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.share-text.data-v-a955827f {
  width: 56rpx;
  height: 44rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #023F98;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.share-button-content.data-v-a955827f {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.data-v-a955827f .up-button--square {
  border-radius: 10rpx !important;
}
.data-v-a955827f .up-button--primary {
  border-radius: 44rpx !important;
}