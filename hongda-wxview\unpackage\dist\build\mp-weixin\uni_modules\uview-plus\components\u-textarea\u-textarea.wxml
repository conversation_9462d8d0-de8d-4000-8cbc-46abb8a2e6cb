<view class="{{['u-textarea', 'data-v-d8442fe6', F]}}" style="{{G}}"><block wx:if="{{r0}}"><textarea class="u-textarea__field data-v-d8442fe6" value="{{a}}" style="{{b}}" placeholder="{{c}}" placeholder-style="{{d}}" placeholder-class="{{e}}" disabled="{{f}}" focus="{{g}}" autoHeight="{{h}}" fixed="{{i}}" cursorSpacing="{{j}}" cursor="{{k}}" showConfirmBar="{{l}}" selectionStart="{{m}}" selectionEnd="{{n}}" adjustPosition="{{o}}" disableDefaultPadding="{{p}}" holdKeyboard="{{q}}" maxlength="{{r}}" confirm-type="{{s}}" ignoreCompositionEvent="{{t}}" bindfocus="{{v}}" bindblur="{{w}}" bindlinechange="{{x}}" bindinput="{{y}}" bindconfirm="{{z}}" bindkeyboardheightchange="{{A}}"></textarea></block><text wx:if="{{B}}" class="u-textarea__count data-v-d8442fe6" style="{{'background-color:' + E}}">{{C}}/{{D}}</text></view>