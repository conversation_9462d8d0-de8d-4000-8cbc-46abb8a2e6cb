package com.hongda.platform.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.content.domain.*;
import com.hongda.content.service.*;
import com.hongda.platform.domain.HongdaAd;
import com.hongda.platform.domain.HongdaPage;
import com.hongda.platform.mapper.HongdaAdMapper;
import com.hongda.platform.service.IHongdaAdService;
import com.hongda.platform.service.IHongdaPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class HongdaAdServiceImpl implements IHongdaAdService
{
    @Autowired
    private HongdaAdMapper hongdaAdMapper;

    // ==================【⭐ 新增代码开始 ⭐】==================
    // 注入所有需要用到的Service
    @Autowired
    private IHongdaArticleService articleService;
    @Autowired
    private IHongdaEventService eventService;
    @Autowired
    private IHongdaCountryService countryService;
    @Autowired
    private IHongdaParkService parkService;
    @Autowired
    private IHongdaPageService pageService;
    @Autowired
    private IHongdaCountryPolicyArticleService countryPolicyArticleService;
    // ==================【⭐ 新增代码结束 ⭐】==================


    /**
     * 查询广告管理
     *
     * @param id 广告管理主键
     * @return 广告管理
     */
    @Override
    public HongdaAd selectHongdaAdById(Long id)
    {
        return hongdaAdMapper.selectHongdaAdById(id);
    }

    /**
     * 查询广告管理列表
     *
     * @param hongdaAd 广告管理
     * @return 广告管理
     */
    @Override
    public List<HongdaAd> selectHongdaAdList(HongdaAd hongdaAd)
    {
        // ==================【⭐ 关键修改开始 ⭐】==================
        // 1. 从数据库获取原始列表
        List<HongdaAd> list = hongdaAdMapper.selectHongdaAdList(hongdaAd);

        // 2. 遍历列表，根据ID填充标题
        for (HongdaAd ad : list) {
            String linkType = ad.getLinkType();
            String linkTarget = ad.getLinkTarget();

            if (StringUtils.isNoneBlank(linkType, linkTarget) && !"#".equals(linkTarget)) {
                try {
                    switch (linkType) {
                        case "ARTICLE":
                            HongdaArticle article = articleService.selectHongdaArticleById(Long.parseLong(linkTarget));
                            if (article != null) ad.setLinkTargetTitle(article.getTitle());
                            break;
                        case "EVENT":
                            HongdaEvent event = eventService.selectHongdaEventById(Long.parseLong(linkTarget));
                            if (event != null) ad.setLinkTargetTitle(event.getTitle());
                            break;
                        case "COUNTRY":
                            HongdaCountry country = countryService.selectHongdaCountryById(Long.parseLong(linkTarget));
                            if (country != null) ad.setLinkTargetTitle(country.getNameCn());
                            break;
                        case "PARK":
                            HongdaPark park = parkService.selectHongdaParkById(Long.parseLong(linkTarget));
                            if (park != null) ad.setLinkTargetTitle(park.getName());
                            break;
                        case "CUSTOM_PAGE":
                            HongdaPage page = pageService.selectHongdaPageById(Long.parseLong(linkTarget));
                            if (page != null) ad.setLinkTargetTitle(page.getTitle());
                            break;
                        case "COUNTRY_POLICY":
                            HongdaCountryPolicyArticle policy = countryPolicyArticleService.selectHongdaCountryPolicyArticleByArticleId(Long.parseLong(linkTarget));
                            if (policy != null) ad.setLinkTargetTitle(policy.getTitle());
                            break;
                    }
                } catch (NumberFormatException e) {
                    // 如果ID不是数字格式，则忽略，避免程序崩溃
                }
            }
        }
        // 3. 返回填充好标题的列表
        return list;
        // ==================【⭐ 关键修改结束 ⭐】==================
    }

    /**
     * 新增广告管理
     *
     * @param hongdaAd 广告管理
     * @return 结果
     */
    @Override
    public int insertHongdaAd(HongdaAd hongdaAd)
    {
        hongdaAd.setCreateTime(DateUtils.getNowDate());
        return hongdaAdMapper.insertHongdaAd(hongdaAd);
    }

    /**
     * 修改广告管理
     *
     * @param hongdaAd 广告管理
     * @return 结果
     */
    @Override
    public int updateHongdaAd(HongdaAd hongdaAd)
    {
        hongdaAd.setUpdateTime(DateUtils.getNowDate());
        return hongdaAdMapper.updateHongdaAd(hongdaAd);
    }

    /**
     * 批量删除广告管理
     *
     * @param ids 需要删除的广告管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaAdByIds(Long[] ids)
    {
        return hongdaAdMapper.deleteHongdaAdByIds(ids);
    }

    /**
     * 删除广告管理信息
     *
     * @param id 广告管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaAdById(Long id)
    {
        return hongdaAdMapper.deleteHongdaAdById(id);
    }
}