<template>
  <view class="page-container">

    <view class="header-section" :style="headerStyle">
      <view class="custom-nav-bar">
        <view class="status-bar"></view>
        <view class="nav-title">资讯列表</view>
        <view class="filter-bar">
          <view class="sort-button" :class="{ 'is-active': activePanel === 'sort' }" @click="togglePanel('sort')">
            {{ sortButtonText }}
            <u-icon name="arrow-down-fill" color="#FFFFFF" size="10"></u-icon>
          </view>
          <view class="filter-button" :class="{ 'is-active': activePanel === 'filter' || isFilterActive }"
                @click="togglePanel('filter')">
            {{ filterButtonText }}
            <u-icon name="arrow-down-fill" color="#FFFFFF" size="10"></u-icon>
            <view class="active-dot" v-if="isFilterActive"></view>
          </view>
          <view class="search-box">
            <uni-easyinput
                class="search-input"
                suffix-icon="search"
                v-model="searchKeyword"
                placeholder="搜索资讯"
                :clearable="true"
                @confirm="handleSearch"
                @clear="handleClear"
                @input="onSearchInput"
            ></uni-easyinput>
          </view>
        </view>
      </view>
    </view>

    <view class="content-section">
      <view class="tabs-container">
        <u-tabs
            v-if="tagList.length > 1"
            :list="tagList"
            :current="currentTabIndex"
            keyName="name"
            @change="handleTabChange"
            lineColor="#023F98"
            lineHeight="6"
            lineWidth="auto"
            :show-scrollbar="false"
            :activeStyle="{
              color: '#023F98',
              fontSize: '32rpx',
              fontWeight: 'bold'
            }"
            :inactiveStyle="{
              color: '#9B9A9A',
              fontSize: '32rpx',
              fontWeight: 'normal'
            }"
        ></u-tabs>
      </view>

      <scroll-view scroll-y class="article-list-scroll" @scrolltolower="loadMore" enable-flex>
        <view class="article-list">
          <view class="article-card" v-for="item in articleList" :key="item.id" @click="gotoDetail(item.id)">
            <view class="card-cover">
              <u-image
                  :src="getFullImageUrl(item.coverImageUrl)"
                  width="100%"
                  height="190rpx"
                  radius="16"
                  :fade="true"
                  :lazy-load="true"
                  @error="onImageError"
                  @load="onImageLoad"
              >
                <template v-slot:loading>
                  <view class="image-loading">
                    <u-loading-icon color="#667eea" size="20"></u-loading-icon>
                    <text class="loading-text">加载中...</text>
                  </view>
                </template>
                <template v-slot:error>
                  <view class="image-error">
                    <u-icon name="photo-off" color="#9ca3af" size="24"></u-icon>
                    <text class="error-text">图片加载失败</text>
                  </view>
                </template>
              </u-image>
            </view>
            <view class="card-content">
              <text class="card-title">{{ item.title }}</text>
              <view class="card-tags" v-if="item.parsedTags && item.parsedTags.length > 0">
                <text class="tag-item" v-for="tag in item.parsedTags" :key="tag.id">{{ tag.name }}</text>
              </view>
              <view class="card-meta">
                <text class="meta-source">{{ item.source }}</text>
                <text class="meta-date">{{ formatDate(item.publishTime) }}</text>
              </view>
            </view>
          </view>
        </view>

        <u-empty v-if="loadStatus === 'nomore' && articleList.length === 0"
                 mode="news" text="暂无资讯"
                 marginTop="100"></u-empty>
        <u-loadmore v-else :status="loadStatus" line="true"/>
      </scroll-view>
    </view>

    <view class="dropdown-wrapper" v-if="activePanel">
      <view class="dropdown-mask" @click="closePanel"></view>
      <view class="dropdown-panel" :class="{ show: activePanel }">
        <view v-if="activePanel === 'sort'" class="sort-panel">
          <view
              class="sort-option"
              v-for="sort in sortActions"
              :key="sort.value"
              :class="{ 'active': tempFilters.sort === sort.value }"
              @click="handleSortSelect(sort)"
          >
            {{ sort.name }}
            <u-icon v-if="tempFilters.sort === sort.value" name="checkmark" color="#023F98" size="18"></u-icon>
          </view>
        </view>

        <view v-if="activePanel === 'filter'" class="filter-panel">
          <scroll-view scroll-y class="filter-scroll">
            <view class="panel-section">
              <text class="section-title">内容地区</text>
              <view class="panel-options">
                <view
                    class="option-btn"
                    v-for="region in regionActions"
                    :key="region.value"
                    :class="{ 'active': tempFilters.region === region.value }"
                    @click="tempFilters.region = region.value"
                >{{ region.name }}
                </view>
              </view>
            </view>
            <view class="panel-section">
              <text class="section-title">发布时间</text>
              <view class="panel-options">
                <view
                    class="option-btn"
                    v-for="time in timeActions"
                    :key="time.value"
                    :class="{ 'active': tempFilters.time === time.value }"
                    @click="tempFilters.time = time.value"
                >{{ time.name }}
                </view>
              </view>
            </view>
          </scroll-view>
          <view class="panel-footer">
            <button class="footer-btn reset" @click="handleFilterReset">重置</button>
            <button class="footer-btn confirm" @click="handleFilterConfirm">确定</button>
          </view>
        </view>
      </view>
    </view>

    <CustomTabBar :current="1"/>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import CustomTabBar from '@/components/layout/CustomTabBar.vue'; // 修正了导入路径
import { onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
import { getArticleList } from '@/api/content/article.js';
import { listAllTag } from '@/api/content/tag.js';
import { getFullImageUrl } from '@/utils/image.js';
import { listAllRegion } from "@/api/content/region";

// --- 响应式状态 ---
const articleList = ref([]);
const tagList = ref([{ id: null, name: '全部' }]);
const currentTabIndex = ref(0);
const loadStatus = ref('loadmore');
const searchKeyword = ref('');
const activePanel = ref(null);
const assets = ref({}); // 【新增】用于存储静态资源

// --- 查询参数 ---
const queryParams = reactive({
  region: null, // <--- 修改此处
  pageNum: 1,
  pageSize: 10,
  title: null,
  tagIds: null,
  orderByColumn: 'sort_order',
  isAsc: 'asc',
  status: '1',
  'params[beginPublishTime]': null,
  'params[endPublishTime]': null,
});

// --- 筛选状态管理 ---
const tempFilters = reactive({
  sort: 'default',
  region: 'all',
  time: 'all',
});

const appliedFilters = reactive({
  sort: 'default',
  region: 'all',
  time: 'all',
});

// --- 筛选选项静态配置 ---
const sortActions = [
  { name: '默认排序', value: 'default' },
  { name: '最新发布', value: 'publish_time' },
  { name: '热度排序', value: 'view_count' },
];

const regionActions = ref([]);

const timeActions = [
  { name: '不限时间', value: 'all' },
  { name: '最近一周', value: 'week' },
  { name: '最近一月', value: 'month' },
  { name: '最近一年', value: 'year' },
];

// --- Computed Properties for UI ---

// 【新增】动态计算头部背景样式
const headerStyle = computed(() => {
  // 从缓存中获取URL，这里不需要备用方案
  const imageUrl = assets.value.bg_article_header;
  if (imageUrl) {
    return {
      backgroundImage: `url('${imageUrl}')`
    };
  }
  // 如果没有配置，则返回一个空对象，不应用任何背景
  return {};
});

const sortButtonText = computed(() => {
  const sortItem = sortActions.find(item => item.value === appliedFilters.sort);
  return sortItem ? sortItem.name : '默认排序';
});

const isFilterActive = computed(() => appliedFilters.region !== 'all' || appliedFilters.time !== 'all');

const filterButtonText = computed(() => {
  if (!isFilterActive.value) {
    return '筛选';
  }
  let count = 0;
  if (appliedFilters.region !== 'all') count++;
  if (appliedFilters.time !== 'all') count++;
  return `筛选(${count})`;
});

// --- Methods ---
const togglePanel = (panelName) => {
  if (activePanel.value === panelName) {
    activePanel.value = null;
  } else {
    tempFilters.sort = appliedFilters.sort;
    tempFilters.region = appliedFilters.region;
    tempFilters.time = appliedFilters.time;
    activePanel.value = panelName;
  }
};

const closePanel = () => {
  activePanel.value = null;
};

const handleSortSelect = (sort) => {
  appliedFilters.sort = sort.value;
  queryParams.orderByColumn = sort.value === 'default' ? 'sort_order' : sort.value;
  queryParams.isAsc = sort.value === 'default' ? 'asc' : 'desc';
  closePanel();
  loadArticles(true);
};

const handleFilterReset = () => {
  appliedFilters.region = 'all';
  appliedFilters.time = 'all';
  tempFilters.region = 'all';
  tempFilters.time = 'all';
  updateFilterParams();
  closePanel();
  loadArticles(true);
};

const handleFilterConfirm = () => {
  appliedFilters.region = tempFilters.region;
  appliedFilters.time = tempFilters.time;
  updateFilterParams();
  closePanel();
  loadArticles(true);
};

const updateFilterParams = () => {
  queryParams.region = appliedFilters.region === 'all' ? null : appliedFilters.region; // <--- 修改此处
  const now = new Date();
  let beginDate = null;
  if (appliedFilters.time !== 'all') {
    const endDate = new Date();
    if (appliedFilters.time === 'week') endDate.setDate(now.getDate() - 7);
    else if (appliedFilters.time === 'month') endDate.setMonth(now.getMonth() - 1);
    else if (appliedFilters.time === 'year') endDate.setFullYear(now.getFullYear() - 1);
    beginDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
  }
  queryParams['params[beginPublishTime]'] = beginDate;
};

const parseArticles = (rows) => {
  return rows.map((article) => {
    let parsedTags = [];
    if (article.tags && typeof article.tags === 'string') {
      try {
        parsedTags = JSON.parse(article.tags);
      } catch (e) {
        console.error('解析文章标签JSON失败:', article.id, article.tags, e);
      }
    }
    return { ...article, parsedTags: Array.isArray(parsedTags) ? parsedTags : [] };
  });
};

const loadArticles = async (isRefresh = false) => {
  if (!isRefresh && loadStatus.value === 'nomore') return;
  if (isRefresh) {
    queryParams.pageNum = 1;
    articleList.value = [];
  }
  loadStatus.value = 'loading';
  try {
    const response = await getArticleList(queryParams);
    const newArticles = parseArticles(response.rows);
    if (isRefresh) {
      articleList.value = newArticles;
    } else {
      articleList.value.push(...newArticles);
    }
    if (response.rows.length < queryParams.pageSize || articleList.value.length >= response.total) {
      loadStatus.value = 'nomore';
    } else {
      loadStatus.value = 'loadmore';
    }
  } catch (error) {
    loadStatus.value = 'loadmore';
    console.error('加载文章列表失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    uni.stopPullDownRefresh();
  }
};

const loadTags = async () => {
  try {
    const response = await listAllTag();
    const backendTags = Array.isArray(response.data) ? response.data : [];
    tagList.value = [{ id: null, name: '全部' }, ...backendTags];
  } catch (error) {
    console.error('加载标签列表失败:', error);
  }
};

const loadRegions = async () => {
  try {
    const response = await listAllRegion();
    const backendRegions = Array.isArray(response.data) ? response.data : [];

    // 1. [新增] 从后端返回的列表中，过滤掉名为“全球”的选项
    const filteredRegions = backendRegions.filter(item => item.name !== '全球');

    // 2. [修改] 使用过滤后的列表来格式化
    const formattedRegions = filteredRegions.map(item => ({
      name: item.name,
      value: item.code
    }));

    // 3. 最后再把我们前端自定义的“全部地区”加到最前面
    regionActions.value = [{ name: '全部地区', value: 'all' }, ...formattedRegions];

  } catch (error) {
    console.error('加载地区列表失败:', error);
    regionActions.value = [{ name: '全部地区', value: 'all' }];
  }
};

const handleTabChange = (tab) => {
  currentTabIndex.value = tab.index;
  queryParams.tagIds = tab.id === null ? null : String(tab.id);
  loadArticles(true);
};

const onSearchInput = (value) => {
  searchKeyword.value = value;
  if (!value || value.trim() === '') {
    handleClear();
  }
};

const handleSearch = () => {
  const keyword = searchKeyword.value.trim();
  queryParams.title = keyword || null;
  loadArticles(true);
};

const handleClear = () => {
  searchKeyword.value = '';
  queryParams.title = null;
  loadArticles(true);
  setTimeout(() => uni.hideKeyboard(), 300);
};

const onImageLoad = () => {};
const onImageError = () => {};

const gotoDetail = (id) => {
  uni.navigateTo({
    url: `/pages_sub/pages_article/detail?id=${id}`,
  });
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return dateString.split(' ')[0];
};

const loadMore = () => {
  if (loadStatus.value === 'loadmore') {
    queryParams.pageNum++;
    loadArticles();
  }
};

const initPageData = async () => {
  try {
    await Promise.all([
      loadTags(),
      loadRegions(),
      loadArticles(true)
    ]);
  } catch (error) {
    console.error('页面初始化失败:', error);
    uni.showToast({title: '页面加载失败', icon: 'none'});
  }
};

onMounted(() => {
  // 【新增】页面加载时，从全局缓存读取静态资源
  assets.value = uni.getStorageSync('staticAssets') || {};
  initPageData();
});

onShow(() => {
  uni.hideTabBar();
});

onPullDownRefresh(() => {
  loadArticles(true);
});

onReachBottom(() => {
  loadMore();
});
</script>

<style lang="scss" scoped>
/* 所有样式保持不变 */
:root {
  --radius-small: 8rpx;
  --radius-medium: 16rpx;
  --radius-large: 24rpx;
  --radius-xl: 32rpx;
  --separator-color: #E5E7EB;
  --content-padding: 32rpx;
}
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #FFFFFF;
}
.header-section {
  position: relative;
  flex-shrink: 0;
  z-index: 10;
  /* 【修改】移除 background 属性 */
  background-color: #f0f2f5; /* 添加一个基础背景色，防止图片加载慢时白屏 */
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-bottom: 24rpx;
}
.custom-nav-bar {
  padding: 0 var(--content-padding) 32rpx;
  .status-bar {
    height: var(--status-bar-height);
  }
  .nav-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 184rpx;
    box-sizing: border-box;
    padding-top: var(--status-bar-height);
    font-size: 34rpx;
    font-weight: bold;
    color: #FFFFFF;
  }
}
.filter-bar {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.sort-button,
.filter-button {
  font-family: "Alibaba PuHuiTi 3.0-55 Regular", sans-serif;
  font-size: 28rpx;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  gap: 8rpx;
  position: relative;
  transition: opacity 0.2s ease;
  &.is-active {
    opacity: 0.8;
  }
}
.filter-button .active-dot {
  position: absolute;
  top: -4rpx;
  right: -16rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #ef4444;
  border-radius: 50%;
  border: 2rpx solid #0F4CA7;
}
.search-box {
  flex: 1;
  height: 64rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-left: auto;
  :deep(.uni-easyinput__content) {
    background: transparent !important;
    border: none !important;
  }
  :deep(.uni-easyinput__placeholder) {
    color: #9B9A9A !important;
  }
}
.dropdown-wrapper {
  position: fixed;
  z-index: 999;
  top: 326rpx;
  left: 0;
  right: 0;
}
.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease;
}
.dropdown-panel {
  position: absolute;
  top: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: var(--radius-large);
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-20px);
  pointer-events: none;
  transition: transform 0.25s ease, opacity 0.25s ease;
  &.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
}
.sort-panel {
  padding: 16rpx 0;
  .sort-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 32rpx;
    font-size: 28rpx;
    color: #333333;
    transition: all 0.2s ease;
    &.active {
      color: #023F98;
      font-weight: 500;
      background-color: rgba(42, 97, 241, 0.05);
    }
  }
}
.filter-panel {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  .filter-scroll {
    flex: 1;
    padding: 32rpx;
  }
  .panel-section {
    margin-bottom: 48rpx;
    &:last-child {
      margin-bottom: 24rpx;
    }
    .section-title {
      font-weight: 500;
      font-size: 30rpx;
      color: #23232A;
      margin-bottom: 28rpx;
      display: block;
    }
    .panel-options {
      display: flex;
      flex-wrap: wrap;
      gap: 24rpx;
    }
    .option-btn {
      width: 200rpx;
      height: 68rpx;
      background: #F2F4FA;
      border-radius: var(--radius-small);
      font-size: 28rpx;
      color: #66666E;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;
      transition: all 0.2s ease;
      &.active {
        background: rgba(42, 97, 241, 0.1);
        color: #023F98;
        font-weight: 500;
      }
    }
  }
  .panel-footer {
    display: flex;
    gap: 24rpx;
    padding: 24rpx 32rpx 32rpx;
    border-top: 1rpx solid #F0F2F5;
    .footer-btn {
      flex: 1;
      height: 80rpx;
      margin: 0;
      font-size: 30rpx;
      border-radius: var(--radius-small);
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      &.reset {
        background-color: #F2F4FA;
        color: #66666E;
      }
      &.confirm {
        background: #0F4CA7;
        color: #FFFFFF;
      }
    }
  }
}
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #FFFFFF;
  margin-top: -24rpx;
  border-top-left-radius: var(--radius-large);
  border-top-right-radius: var(--radius-large);
  position: relative;
  z-index: 20;
}
.tabs-container {
  flex-shrink: 0;
  width: 100%;
  background: #ffffff;
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
  position: relative;
  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--content-padding);
    right: var(--content-padding);
    height: 1rpx;
    background-color: var(--separator-color);
  }
  :deep(.u-tabs) {
    padding: 12rpx 0;
  }
}
.article-list-scroll {
  flex: 1;
  height: 0;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: calc(160rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
}
.article-list {
  background: #FFFFFF;
}
.article-card {
  display: flex;
  gap: 30rpx;
  padding: 32rpx var(--content-padding);
  background: #ffffff;
  position: relative;
  &:not(:last-child) {
    border-bottom: 1rpx solid var(--separator-color);
  }
  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  /* --- [START] MODIFIED STYLES --- */
  .card-title {
    width: 346rpx;
    height: 80rpx;
    font-family: "Alibaba PuHuiTi 3.0", sans-serif;
    font-weight: normal;
    font-size: 28rpx;
    color: #23232A;
    text-align: justified;
    line-height: 1.5; /* Keeping line-height for proper spacing */
    font-style: normal;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: auto;
  }
  .card-meta {
    display: flex;
    align-items: center;
    gap: 20rpx; /* Changed from 32rpx to 20rpx */
    margin-top: 24rpx;
    font-size: 24rpx;
    color: #9B9A9A;
  }
  /* --- [END] MODIFIED STYLES --- */
  .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-top: 16rpx;
  }
  .tag-item {
    width: 112rpx;
    height: 40rpx;
    background: #FFFFFF;
    border-radius: 4rpx;
    border: 1rpx solid #023F98;
    color: #023F98;
    font-size: 22rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
  .card-cover {
    flex-shrink: 0;
    width: 336rpx;
    height: 190rpx;
    border-radius: var(--radius-medium);
    overflow: hidden;
  }
}
.image-loading,
.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: var(--radius-medium);
}
.loading-text,
.error-text {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 8rpx;
}
:deep(.u-loadmore__content__text) {
  font-size: 24rpx !important;
  color: #9B9A9A !important;
  line-height: 34rpx !important;
}
</style>