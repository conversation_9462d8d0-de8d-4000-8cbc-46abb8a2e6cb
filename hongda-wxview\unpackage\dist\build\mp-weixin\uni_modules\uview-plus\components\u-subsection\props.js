"use strict";const e=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),o=e.defineMixin({props:{list:{type:Array,default:()=>t.props.subsection.list},current:{type:[String,Number],default:()=>t.props.subsection.current},activeColor:{type:String,default:()=>t.props.subsection.activeColor},inactiveColor:{type:String,default:()=>t.props.subsection.inactiveColor},mode:{type:String,default:()=>t.props.subsection.mode},fontSize:{type:[String,Number],default:()=>t.props.subsection.fontSize},bold:{type:Boolean,default:()=>t.props.subsection.bold},bgColor:{type:String,default:()=>t.props.subsection.bgColor},keyName:{type:String,default:()=>t.props.subsection.keyName},activeColorKeyName:{type:String,default:()=>t.props.subsection.activeColorKeyName},inactiveColorKeyName:{type:String,default:()=>t.props.subsection.inactiveColorKeyName},disabled:{type:String,default:()=>t.props.subsection.disabled}}});exports.props=o;
