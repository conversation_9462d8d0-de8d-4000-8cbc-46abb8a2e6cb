"use strict";const e=require("../../libs/vue.js"),i=require("../../libs/config/props.js"),r=e.defineMixin({props:{color:{type:String,default:()=>i.props.line.color},length:{type:[String,Number],default:()=>i.props.line.length},direction:{type:String,default:()=>i.props.line.direction},hairline:{type:<PERSON><PERSON><PERSON>,default:()=>i.props.line.hairline},margin:{type:[String,Number],default:()=>i.props.line.margin},dashed:{type:<PERSON><PERSON><PERSON>,default:()=>i.props.line.dashed}}});exports.props=r;
