<view class="event-list-page data-v-6421797e"><view class="header-wrapper data-v-6421797e"><image class="header-bg data-v-6421797e" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-6421797e"><view class="navbar-title data-v-6421797e"><text class="title-text data-v-6421797e">热门活动列表</text></view></view><view class="top-controls data-v-6421797e"><up-subsection wx:if="{{c}}" class="data-v-6421797e" bindchange="{{b}}" u-i="6421797e-0" bind:__l="__l" u-p="{{c}}"></up-subsection><view class="search-wrapper data-v-6421797e"><custom-search-box wx:if="{{g}}" class="data-v-6421797e" bindsearch="{{d}}" bindinput="{{e}}" u-i="6421797e-1" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"></custom-search-box></view></view></view><view wx:if="{{h}}" class="filter-bar sticky-filter-bar data-v-6421797e"><view class="filter-main-buttons data-v-6421797e"><view class="filter-button data-v-6421797e" bindtap="{{l}}"><text class="filter-text data-v-6421797e">{{i}}</text><up-icon wx:if="{{k}}" class="{{['data-v-6421797e', j && 'rotate-180']}}" u-i="6421797e-2" bind:__l="__l" u-p="{{k}}"></up-icon></view><view class="filter-button data-v-6421797e" bindtap="{{p}}"><text class="filter-text data-v-6421797e">{{m}}</text><up-icon wx:if="{{o}}" class="{{['data-v-6421797e', n && 'rotate-180']}}" u-i="6421797e-3" bind:__l="__l" u-p="{{o}}"></up-icon></view><view class="filter-button data-v-6421797e" bindtap="{{t}}"><text class="filter-text data-v-6421797e">{{q}}</text><up-icon wx:if="{{s}}" class="{{['data-v-6421797e', r && 'rotate-180']}}" u-i="6421797e-4" bind:__l="__l" u-p="{{s}}"></up-icon></view><view class="filter-button data-v-6421797e" bindtap="{{y}}"><text class="filter-text data-v-6421797e">{{v}}</text><up-icon wx:if="{{x}}" class="{{['data-v-6421797e', w && 'rotate-180']}}" u-i="6421797e-5" bind:__l="__l" u-p="{{x}}"></up-icon></view></view><view wx:if="{{z}}" class="filter-panel data-v-6421797e"><text class="section-title data-v-6421797e">排序</text><view class="option-grid data-v-6421797e"><view wx:for="{{A}}" wx:for-item="option" wx:key="b" class="{{['data-v-6421797e', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-6421797e">{{option.a}}</text></view></view><view class="filter-buttons data-v-6421797e"><view class="filter-btn reset-btn data-v-6421797e" bindtap="{{B}}"><text class="btn-text data-v-6421797e">重置</text></view><view class="filter-btn complete-btn data-v-6421797e" bindtap="{{C}}"><text class="btn-text data-v-6421797e">完成</text></view></view></view><view wx:if="{{D}}" class="filter-panel data-v-6421797e"><view class="option-grid data-v-6421797e" style="margin-bottom:20rpx"><view class="{{['data-v-6421797e', 'option-item', F]}}" bindtap="{{G}}"><text class="option-text data-v-6421797e">{{E}}</text></view></view><text class="section-title data-v-6421797e">热门地区</text><view class="option-grid data-v-6421797e"><view wx:for="{{H}}" wx:for-item="option" wx:key="b" class="{{['data-v-6421797e', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-6421797e">{{option.a}}</text></view></view><view wx:if="{{I}}" class="data-v-6421797e" style="margin-top:20rpx"><text class="section-title data-v-6421797e">其他地区</text><view class="option-grid data-v-6421797e"><view wx:for="{{J}}" wx:for-item="city" wx:key="b" class="{{['data-v-6421797e', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-6421797e">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-6421797e"><view class="filter-btn reset-btn data-v-6421797e" bindtap="{{K}}"><text class="btn-text data-v-6421797e">重置</text></view><view class="filter-btn complete-btn data-v-6421797e" bindtap="{{L}}"><text class="btn-text data-v-6421797e">完成</text></view></view></view><view wx:if="{{M}}" class="filter-panel data-v-6421797e"><text class="section-title data-v-6421797e">时间</text><view class="option-grid data-v-6421797e"><view wx:for="{{N}}" wx:for-item="option" wx:key="b" class="{{['data-v-6421797e', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-6421797e">{{option.a}}</text></view></view><view class="filter-buttons data-v-6421797e"><view class="filter-btn reset-btn data-v-6421797e" bindtap="{{O}}"><text class="btn-text data-v-6421797e">重置</text></view><view class="filter-btn complete-btn data-v-6421797e" bindtap="{{P}}"><text class="btn-text data-v-6421797e">完成</text></view></view></view><view wx:if="{{Q}}" class="filter-panel data-v-6421797e"><text class="section-title data-v-6421797e">全部状态</text><view class="option-grid data-v-6421797e"><view wx:for="{{R}}" wx:for-item="option" wx:key="b" class="{{['data-v-6421797e', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-6421797e">{{option.a}}</text></view></view><view class="filter-buttons data-v-6421797e"><view class="filter-btn reset-btn data-v-6421797e" bindtap="{{S}}"><text class="btn-text data-v-6421797e">重置</text></view><view class="filter-btn complete-btn data-v-6421797e" bindtap="{{T}}"><text class="btn-text data-v-6421797e">完成</text></view></view></view></view><scroll-view wx:if="{{U}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-6421797e" bindscrolltolower="{{ac}}" refresher-enabled refresher-triggered="{{ad}}" bindrefresherrefresh="{{ae}}"><view wx:if="{{V}}" class="empty-state data-v-6421797e"><up-empty wx:if="{{W}}" class="data-v-6421797e" u-i="6421797e-6" bind:__l="__l" u-p="{{W}}"></up-empty><view wx:if="{{X}}" class="retry-container data-v-6421797e"><up-button wx:if="{{Z}}" class="data-v-6421797e" u-s="{{['d']}}" bindclick="{{Y}}" u-i="6421797e-7" bind:__l="__l" u-p="{{Z}}"> 重新加载 </up-button></view></view><event-card wx:for="{{aa}}" wx:for-item="event" wx:key="a" class="data-v-6421797e" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-6421797e"><up-loadmore wx:if="{{ab}}" class="data-v-6421797e" u-i="6421797e-9" bind:__l="__l" u-p="{{ab}}"/></view></scroll-view><view wx:if="{{af}}" class="filter-bar calendar-filter-bar sticky-filter-bar data-v-6421797e"><view class="filter-main-buttons data-v-6421797e"><view class="filter-button data-v-6421797e" bindtap="{{aj}}"><text class="filter-text data-v-6421797e">{{ag}}</text><up-icon wx:if="{{ai}}" class="{{['data-v-6421797e', ah && 'rotate-180']}}" u-i="6421797e-10" bind:__l="__l" u-p="{{ai}}"></up-icon></view><view class="filter-button data-v-6421797e" bindtap="{{an}}"><text class="filter-text data-v-6421797e">{{ak}}</text><up-icon wx:if="{{am}}" class="{{['data-v-6421797e', al && 'rotate-180']}}" u-i="6421797e-11" bind:__l="__l" u-p="{{am}}"></up-icon></view><view class="filter-button filter-placeholder data-v-6421797e"><text class="filter-text data-v-6421797e"></text></view><view class="filter-button filter-placeholder data-v-6421797e"><text class="filter-text data-v-6421797e"></text></view></view><view wx:if="{{ao}}" class="filter-panel data-v-6421797e"><view class="option-grid data-v-6421797e" style="margin-bottom:20rpx"><view class="{{['data-v-6421797e', 'option-item', aq]}}" bindtap="{{ar}}"><text class="option-text data-v-6421797e">{{ap}}</text></view></view><text class="section-title data-v-6421797e">热门地区</text><view class="option-grid data-v-6421797e"><view wx:for="{{as}}" wx:for-item="option" wx:key="b" class="{{['data-v-6421797e', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-6421797e">{{option.a}}</text></view></view><view wx:if="{{at}}" class="data-v-6421797e" style="margin-top:20rpx"><text class="section-title data-v-6421797e">其他地区</text><view class="option-grid data-v-6421797e"><view wx:for="{{av}}" wx:for-item="city" wx:key="b" class="{{['data-v-6421797e', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-6421797e">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-6421797e"><view class="filter-btn reset-btn data-v-6421797e" bindtap="{{aw}}"><text class="btn-text data-v-6421797e">重置</text></view><view class="filter-btn complete-btn data-v-6421797e" bindtap="{{ax}}"><text class="btn-text data-v-6421797e">完成</text></view></view></view><view wx:if="{{ay}}" class="filter-panel data-v-6421797e"><text class="section-title data-v-6421797e">时间</text><view class="option-grid data-v-6421797e"><view wx:for="{{az}}" wx:for-item="option" wx:key="b" class="{{['data-v-6421797e', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-6421797e">{{option.a}}</text></view></view><view class="filter-buttons data-v-6421797e"><view class="filter-btn reset-btn data-v-6421797e" bindtap="{{aA}}"><text class="btn-text data-v-6421797e">重置</text></view><view class="filter-btn complete-btn data-v-6421797e" bindtap="{{aB}}"><text class="btn-text data-v-6421797e">完成</text></view></view></view></view><scroll-view wx:if="{{aC}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-6421797e" bindscrolltolower="{{aL}}" refresher-enabled refresher-triggered="{{aM}}" bindrefresherrefresh="{{aN}}"><view wx:if="{{aD}}" class="empty-state data-v-6421797e"><up-empty wx:if="{{aE}}" class="data-v-6421797e" u-i="6421797e-12" bind:__l="__l" u-p="{{aE}}"></up-empty><view wx:if="{{aF}}" class="retry-container data-v-6421797e"><up-button wx:if="{{aH}}" class="data-v-6421797e" u-s="{{['d']}}" bindclick="{{aG}}" u-i="6421797e-13" bind:__l="__l" u-p="{{aH}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-6421797e" bindclickItem="{{aI}}" bindviewMore="{{aJ}}" u-i="6421797e-14" bind:__l="__l" u-p="{{aK||''}}"/><view class="calendar-bottom-safe data-v-6421797e"/></scroll-view><custom-tab-bar wx:if="{{aO}}" class="data-v-6421797e" u-i="6421797e-15" bind:__l="__l" u-p="{{aO}}"/></view>