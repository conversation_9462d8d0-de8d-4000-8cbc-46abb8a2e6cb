<template>
  <view class="header-container" :style="headerStyle">
    <view class="header-content">
      <view class="search-bar-wrapper" :style="searchBarStyle" @click="handleSearchClick">
        <text class="search-placeholder">搜索资讯、活动</text>
        <image
            v-if="searchIconUrl"
            class="search-icon"
            :src="searchIconUrl"
            mode="aspectFit"
        ></image>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'; // [修改] 导入 computed

// ====================================================================================
// --- 您可以在这里调整布局的细节 ---
//
// 所有单位都是像素(px)，会自动处理rpx转换和屏幕适配
// ====================================================================================

/** * 【参数1】头部容器左右的内边距 (padding)。
 * 注意：修改此值后，也需要同步修改下面 <style> 部分 .header-content 的 padding 值。
 */
const HEADER_HORIZONTAL_PADDING = uni.upx2px(24); // 默认 24rpx

/** * 【参数2】搜索栏右侧与胶囊按钮左侧的间距。
 */
const GAP_BETWEEN_SEARCH_AND_CAPSULE = 10; // 默认 10px 的间距

/** * 【参数3】搜索栏高度的微调值。
 * 设置为 0  : 搜索栏与胶囊等高。
 * 设置为 -4 : 搜索栏比胶囊矮 4px。
 * 设置为 4  : 搜索栏比胶囊高 4px。
 */
const SEARCH_BAR_HEIGHT_ADJUSTMENT = 0; // 默认等高

/** * 【参数4】头部容器在胶囊上下方增加的额外垂直内边距。
 * 这会让整个头部变得更高，给胶囊和搜索栏更多的“呼吸空间”。
 */
const HEADER_EXTRA_VERTICAL_PADDING = uni.upx2px(0); // 默认在上下各增加 10rpx 的额外边距


// ====================================================================================
// --- 以下是自动计算逻辑，通常无需修改 ---
// ====================================================================================

// [新增] 定义 ref 来存储从全局缓存中读取的静态资源
const assets = ref(uni.getStorageSync('staticAssets') || {});

// [新增] 为“搜索图标”创建计算属性
const searchIconUrl = computed(() => {
  // 使用我们约定的“暗号” icon_home_search
  return assets.value.icon_home_search || '';
});


const headerStyle = ref({});
const searchBarStyle = ref({});
const emit = defineEmits(['height-calculated']);

onMounted(() => {
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  const systemInfo = uni.getSystemInfoSync();

  // --- 动态计算样式 ---

  // 1. 计算头部内容区域的高度
  const headerContentHeight =
      (menuButtonInfo.top - systemInfo.statusBarHeight) * 2 +
      menuButtonInfo.height +
      (HEADER_EXTRA_VERTICAL_PADDING * 2);

  // 2. 计算搜索框的宽度
  const searchBarWidth =
      menuButtonInfo.left -
      HEADER_HORIZONTAL_PADDING -
      GAP_BETWEEN_SEARCH_AND_CAPSULE;

  // 3. 计算搜索框的高度
  const searchBarHeight = menuButtonInfo.height + SEARCH_BAR_HEIGHT_ADJUSTMENT;

  // 4. 计算头部容器完整的总高度
  const totalHeaderHeight = headerContentHeight + systemInfo.statusBarHeight;

  // 将计算结果赋值给响应式变量
  headerStyle.value = {
    height: `${totalHeaderHeight}px`,
    paddingTop: `${systemInfo.statusBarHeight}px`
  };

  searchBarStyle.value = {
    width: `${searchBarWidth}px`,
    height: `${searchBarHeight}px`,
  };

  // 向父组件发送计算出的实际总高度
  emit('height-calculated', totalHeaderHeight);
});


const handleSearchClick = () => {
  uni.navigateTo({
    url: '/pages_sub/pages_other/search'
  });
};
</script>

<style lang="scss" scoped>
.header-container {
  width: 100%;
  background-color: #023f98;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
}

.header-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  /* 这里的 padding-left/right 需要与 JS 中的 HEADER_HORIZONTAL_PADDING 常量值保持一致 */
  padding: 0 24rpx;
  box-sizing: border-box;
}

.search-bar-wrapper {
  background-color: #ffffff;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.search-bar-wrapper:active {
  opacity: 0.8;
}

.search-placeholder {
  font-size: 28rpx;
  color: #9b9a9a;
}

/* [新增] 自定义搜索图标的样式 */
.search-icon {
  width: 25rpx;
  height: 25rpx;
  flex-shrink: 0;
}
</style>