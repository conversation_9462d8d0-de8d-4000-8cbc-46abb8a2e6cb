"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/image.js"),r={__name:"EventDetailContent",props:{eventDetail:{type:Object,required:!0}},setup(r){const a=r,i=e.computed((()=>{var e;if(!(null==(e=a.eventDetail)?void 0:e.details)||"string"!=typeof a.eventDetail.details)return"";let r=a.eventDetail.details;try{return r=r.replace(/<img([^>]*?)src=["']([^"']*?)["']([^>]*?)>/gi,((e,r,a,i)=>{const l=t.getFullImageUrl(a);if(/style\s*=/.test(r+i)){const e=/style\s*=\s*["']([^"']*?)["']/i,t=(r+i).match(e);if(t){const a=t[1],s=a.includes("max-width")?a:a+"; max-width: 100%; height: auto;";return`<img${r}src="${l}"${i}`.replace(e,`style="${s}"`)}}return`<img${r}src="${l}" style="max-width: 100%; height: auto; border-radius: 8rpx; margin: 16rpx 0;"${i}>`})),r=r.replace(/<img([^>]*?)>/gi,(e=>/style\s*=/.test(e)?e:e.replace(">",' style="max-width: 100%; height: auto; border-radius: 8rpx; margin: 16rpx 0;">'))),r}catch(i){return console.error("富文本处理失败:",i),a.eventDetail.details}}));return(t,a)=>e.e({a:i.value&&i.value.trim().length>0},i.value&&i.value.trim().length>0?{b:i.value}:r.eventDetail.summary&&r.eventDetail.summary.trim().length>0?{d:e.t(r.eventDetail.summary)}:{},{c:r.eventDetail.summary&&r.eventDetail.summary.trim().length>0})}},a=e._export_sfc(r,[["__scopeId","data-v-d82a0a35"]]);wx.createComponent(a);
