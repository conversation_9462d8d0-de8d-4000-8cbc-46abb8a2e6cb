"use strict";const e=require("../../common/vendor.js"),o=require("../../api/data/event.js"),l=require("./api/data/registration.js"),t=require("../../utils/image.js");if(!Array){(e.resolveComponent("up-navbar")+e.resolveComponent("up-loading-page")+e.resolveComponent("up-image"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-navbar/u-navbar.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js")+(()=>"../../uni_modules/uview-plus/components/u-image/u-image.js")+a+n+s)();const a=()=>"../../components/event/EventInfoCard.js",n=()=>"../../components/event/EventDetailContent.js",s=()=>"../../components/event/EventActionBar.js",r={__name:"detail",setup(a){const n=e.ref(null),s=e.ref(null),r=e.ref("loading"),i=e.ref(!0),u=e.ref(0),c=e.ref(0),v=e.ref(44),g=e.computed((()=>`${c.value+v.value}px`));e.onMounted((()=>{const o=e.index.getSystemInfoSync();c.value=o.statusBarHeight||0}));const d=()=>{try{return e.index.getStorageSync("token")||null}catch(o){return null}},m=e.computed((()=>(u.value,!!d()))),p=e.computed((()=>{var e,o;const l=null==(e=s.value)?void 0:e.registrationStartTime,t=null==(o=s.value)?void 0:o.registrationEndTime;try{const e=new Date;return l&&e<new Date(l)?0:(!l||e>=new Date(l))&&(!t||e<=new Date(t))?1:t&&e>new Date(t)?2:1}catch(a){return console.warn("报名状态计算失败:",a),1}})),f=e.computed((()=>{if(!s.value)return"加载中...";switch(r.value){case"loading":return"检查状态中...";case"registered":return"已报名";case"not_logged_in":switch(p.value){case 0:return"即将开始";case 1:default:return"立即报名";case 2:return"报名截止"}case"unregistered":switch(p.value){case 0:return"即将开始";case 1:default:return"立即报名";case 2:return"报名截止"}case"error":switch(p.value){case 0:return"即将开始";case 1:default:return"立即报名";case 2:return"报名截止"}default:return"立即报名"}})),w=e.computed((()=>!s.value||("loading"===r.value||("registered"===r.value||(0===p.value||2===p.value))))),h=()=>{console.log("=== 开始智能导航回退处理 ==="),e.index.navigateBack({success:()=>{console.log("正常回退成功")},fail:o=>{console.warn("正常回退失败:",o),e.index.navigateTo({url:"/pages/event/index",success:()=>{console.log("跳转到活动列表页面成功")},fail:o=>{console.warn("跳转到活动列表页面失败:",o),e.index.switchTab({url:"/pages/index/index",success:()=>{console.log("跳转到首页成功")},fail:o=>{console.error("所有导航方案都失败了:",o),e.index.showToast({title:"导航失败，请重新打开小程序",icon:"none"})}})}})}})},x=async()=>{console.log("=== 开始独立的报名状态检查 ===");try{if(!d())return console.log("用户未登录，设置状态为 not_logged_in"),void(r.value="not_logged_in");if(!n.value)return console.warn("缺少 eventId，无法检查报名状态"),void(r.value="error");console.log("前置条件满足，开始调用 API 检查报名状态...");try{const o=(e.index.getStorageSync("registrationStatus")||{})[n.value];o&&o.isRegistered&&(console.log("从本地存储发现已报名状态，立即更新 UI"),r.value="registered")}catch(o){console.warn("读取本地报名状态失败:",o)}const t=await l.checkRegistrationStatusApi(n.value);if(200===t.code){const o=t.isRegistered||!1;console.log("获取报名状态成功:",o);const l=o?"registered":"unregistered";r.value!==l?(console.log("报名状态发生变化:",r.value,"->",l),r.value=l,"registered"===l&&"registered"!==r.value&&e.index.$u.toast("报名状态已更新")):console.log("报名状态无变化，保持现状")}else console.warn("检查报名状态 API 返回错误:",t),r.value="error"}catch(o){console.error("报名状态检查失败:",o),r.value="error"}console.log("=== 报名状态检查完成，最终状态:",r.value," ===")};e.onLoad((async l=>{if(n.value=l.id,!n.value)return e.index.$u.toast("活动不存在"),void setTimeout((()=>{e.index.navigateBack()}),1500);e.index.$on("dataChanged",(async()=>{if(console.log("📩 收到数据变化事件，重新获取活动详情..."),n.value)try{console.log("开始重新获取活动详情数据...");const l=await o.getEventDetailApi(n.value);if(200===l.code){const o=l.data;o.details=o.details||"",o.summary=o.summary||"",o.title=o.title||"",o.location=o.location||"",o.coverImageUrl=o.coverImageUrl||"",s.value=o,console.log("活动详情数据已更新，最新报名人数:",o.registeredCount),await x(),e.index.showToast({title:"数据已更新",icon:"success",duration:1500})}}catch(l){console.error("重新获取活动详情失败:",l)}else console.warn("事件监听：缺少 eventId，跳过数据刷新")}));try{console.log("开始加载活动详情，eventId:",n.value);const e=await o.getEventDetailApi(n.value);if(200!==e.code)throw new Error(e.msg||"获取活动详情失败");{console.log("活动详情API调用成功，开始处理数据...");const o=e.data;o.details&&"string"==typeof o.details?o.details=o.details.trim():o.details="",o.summary&&"string"==typeof o.summary?o.summary=o.summary.trim():o.summary="",o.title=o.title||"",o.location=o.location||"",o.coverImageUrl=o.coverImageUrl||"",s.value=o,console.log("活动详情数据已完全加载并赋值:",{id:s.value.id,title:s.value.title,status:s.value.status})}console.log("活动详情加载完成，现在开始检查报名状态..."),await x(),console.log("报名状态检查完成")}catch(t){console.error("获取活动详情失败",t),e.index.$u.toast(t.message||"加载失败，请稍后重试"),r.value="error"}finally{console.log("页面加载流程完成，停止loading状态"),i.value=!1}})),e.onShow((async()=>{console.log("=== 活动详情页面 onShow 触发 ==="),u.value++;const o=d();console.log("当前登录状态:",!!o),console.log("当前token:",o),console.log("当前活动ID:",n.value),console.log("活动详情是否已加载:",!!s.value),console.log("页面是否还在加载中:",i.value),console.log("当前报名状态:",r.value);try{const o=e.index.getStorageSync("needRefreshRegistrationStatus");if(o&&o.eventId===n.value&&(console.log("发现状态刷新标记，立即刷新报名状态:",o),e.index.removeStorageSync("needRefreshRegistrationStatus"),n.value))return void(await x())}catch(l){console.warn("检查状态刷新标记失败:",l)}if(!i.value&&n.value)console.log("条件满足，开始重新检查报名状态..."),await x();else if(console.log("跳过报名状态检查，原因:",{isLoading:i.value,hasEventId:!!n.value}),i.value&&n.value){console.log("页面正在加载中，等待加载完成后再检查报名状态...");const o=e.watch(i,(async e=>{e||(console.log("页面加载完成，现在开始检查报名状态..."),await x(),o())}))}})),e.onUnload((()=>{e.index.$off("dataChanged")})),e.onShareAppMessage((e=>{var o,l;return console.log("分享触发，来源:",e.from),{title:(null==(o=s.value)?void 0:o.title)||"精彩活动推荐",path:`/pages_sub/pages_event/detail?id=${n.value}`,imageUrl:t.getFullImageUrl(null==(l=s.value)?void 0:l.coverImageUrl)}}));const y=()=>{var o,l,t,a,i;if(console.log("=== 点击报名按钮 ==="),console.log("按钮当前显示文字:",f.value),console.log("按钮是否禁用:",w.value),console.log("当前报名状态:",r.value),console.log("当前登录状态:",m.value),console.log("当前token:",d()),console.log("活动详情:",{id:null==(o=s.value)?void 0:o.id,title:null==(l=s.value)?void 0:l.title,status:null==(t=s.value)?void 0:t.status}),u.value++,!m.value||"not_logged_in"===r.value)return console.log("用户未登录，跳转到登录页"),void e.index.navigateTo({url:"/pages_sub/pages_other/login"});if(console.log("用户已登录"),"registered"===r.value)console.log("用户已报名此活动"),e.index.$u.toast("您已报名此活动");else if("unregistered"===r.value||"error"===r.value){const o=p.value;0===o?(console.log("活动尚未开始"),e.index.$u.toast("活动尚未开始")):2===o?(console.log("活动报名已截止"),e.index.$u.toast("活动报名已截止")):1===o?(console.log("用户未报名，跳转到报名页面"),console.log("跳转参数:",{id:n.value,title:null==(a=s.value)?void 0:a.title}),e.index.navigateTo({url:`/pages_sub/pages_event/registration?id=${n.value}&title=${encodeURIComponent((null==(i=s.value)?void 0:i.title)||"")}`})):(console.log("活动状态异常"),e.index.$u.toast("活动状态异常，请稍后再试"))}else console.log("报名状态还在加载中，请稍后再试"),e.index.$u.toast("正在检查报名状态，请稍后再试")},I=()=>{var e;console.log("用户点击分享按钮"),console.log("分享活动信息:",{id:n.value,title:null==(e=s.value)?void 0:e.title})};return(o,l)=>e.e({a:e.o(h),b:e.p({title:"活动详情",fixed:!0,safeAreaInsetTop:!0,bgColor:"#ffffff",leftIcon:"arrow-left",leftIconColor:"#333333",titleStyle:"color: #333333; font-weight: bold;"}),c:i.value},i.value?{d:e.p({loadingText:"正在加载活动详情...",loadingMode:"spinner"})}:{},{e:!i.value&&s.value},!i.value&&s.value?{f:e.p({src:e.unref(t.getFullImageUrl)(s.value.coverImageUrl),mode:"aspectFill",height:"420rpx",width:"100%"}),g:e.p({eventDetail:s.value}),h:e.p({eventDetail:s.value}),i:g.value}:{},{j:!i.value&&s.value},!i.value&&s.value?{k:e.o(I),l:e.o(y),m:e.p({eventDetail:s.value,isLoading:i.value,registrationStatus:r.value,isButtonDisabled:w.value,buttonText:f.value})}:{})}},i=e._export_sfc(r,[["__scopeId","data-v-cd9c415d"]]);r.__runtimeHooks=2,wx.createPage(i);
