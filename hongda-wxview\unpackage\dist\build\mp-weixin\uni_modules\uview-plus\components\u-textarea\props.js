"use strict";const e=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),a=e.defineMixin({props:{value:{type:[String,Number],default:()=>t.props.textarea.value},modelValue:{type:[String,Number],default:()=>t.props.textarea.value},placeholder:{type:[String,Number],default:()=>t.props.textarea.placeholder},placeholderClass:{type:String,default:()=>t.props.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>t.props.input.placeholderStyle},height:{type:[String,Number],default:()=>t.props.textarea.height},confirmType:{type:String,default:()=>t.props.textarea.confirmType},disabled:{type:Boolean,default:()=>t.props.textarea.disabled},count:{type:<PERSON><PERSON><PERSON>,default:()=>t.props.textarea.count},focus:{type:<PERSON><PERSON>an,default:()=>t.props.textarea.focus},autoHeight:{type:<PERSON><PERSON><PERSON>,default:()=>t.props.textarea.autoHeight},fixed:{type:Boolean,default:()=>t.props.textarea.fixed},cursorSpacing:{type:Number,default:()=>t.props.textarea.cursorSpacing},cursor:{type:[String,Number],default:()=>t.props.textarea.cursor},showConfirmBar:{type:Boolean,default:()=>t.props.textarea.showConfirmBar},selectionStart:{type:Number,default:()=>t.props.textarea.selectionStart},selectionEnd:{type:Number,default:()=>t.props.textarea.selectionEnd},adjustPosition:{type:Boolean,default:()=>t.props.textarea.adjustPosition},disableDefaultPadding:{type:Boolean,default:()=>t.props.textarea.disableDefaultPadding},holdKeyboard:{type:Boolean,default:()=>t.props.textarea.holdKeyboard},maxlength:{type:[String,Number],default:()=>t.props.textarea.maxlength},border:{type:String,default:()=>t.props.textarea.border},formatter:{type:[Function,null],default:()=>t.props.textarea.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}});exports.props=a;
