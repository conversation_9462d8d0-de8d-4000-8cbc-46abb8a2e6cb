"use strict";const e=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),r=e.defineMixin({props:{loading:{type:Boolean,default:()=>t.props.skeleton.loading},animate:{type:<PERSON>olean,default:()=>t.props.skeleton.animate},rows:{type:[String,Number],default:()=>t.props.skeleton.rows},rowsWidth:{type:[String,Number,Array],default:()=>t.props.skeleton.rowsWidth},rowsHeight:{type:[String,Number,Array],default:()=>t.props.skeleton.rowsHeight},title:{type:<PERSON><PERSON><PERSON>,default:()=>t.props.skeleton.title},titleWidth:{type:[String,Number],default:()=>t.props.skeleton.titleWidth},titleHeight:{type:[String,Number],default:()=>t.props.skeleton.titleHeight},avatar:{type:<PERSON><PERSON><PERSON>,default:()=>t.props.skeleton.avatar},avatarSize:{type:[String,Number],default:()=>t.props.skeleton.avatarSize},avatarShape:{type:String,default:()=>t.props.skeleton.avatarShape}}});exports.props=r;
