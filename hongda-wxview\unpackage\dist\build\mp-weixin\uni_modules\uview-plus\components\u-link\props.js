"use strict";const e=require("../../libs/vue.js"),p=require("../../libs/config/props.js"),r=e.defineMixin({props:{color:{type:String,default:()=>p.props.link.color},fontSize:{type:[String,Number],default:()=>p.props.link.fontSize},underLine:{type:Boolean,default:()=>p.props.link.underLine},href:{type:String,default:()=>p.props.link.href},mpTips:{type:String,default:()=>p.props.link.mpTips},lineColor:{type:String,default:()=>p.props.link.lineColor},text:{type:String,default:()=>p.props.link.text}}});exports.props=r;
