"use strict";const e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),o=require("../../libs/mixin/mixin.js"),t=require("../../libs/function/index.js"),r=require("../../../../common/vendor.js"),n={name:"u-overlay",mixins:[i.mpMixin,o.mixin,e.props],computed:{overlayStyle(){const e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return t.deepMerge(e,t.addStyle(this.customStyle))}},emits:["click"],methods:{clickHandler(){this.$emit("click")}}};if(!Array){r.resolveComponent("u-transition")()}Math;const s=r._export_sfc(n,[["render",function(e,i,o,t,n,s){return{a:r.o(s.clickHandler),b:r.o(e.noop),c:r.p({show:e.show,"custom-class":"u-overlay",duration:e.duration,"custom-style":s.overlayStyle})}}],["__scopeId","data-v-43b1d16b"]]);wx.createComponent(s);
