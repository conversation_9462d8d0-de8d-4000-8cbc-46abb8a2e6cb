"use strict";const t=require("../../libs/function/index.js"),e=require("../../libs/function/test.js"),r={computed:{value(){const{text:r,mode:s,format:n,href:u}=this;return"price"===s?e.test.func(n)?n(r):t.priceFormat(r,2):"date"===s?(!e.test.date(r)&&t.error(),e.test.func(n)?n(r):n?t.timeFormat(r,n):t.timeFormat(r,"yyyy-mm-dd")):"phone"===s?e.test.func(n)?n(r):"encrypt"===n?`${r.substr(0,3)}****${r.substr(7)}`:r:"name"===s?e.test.func(n)?n(r):"encrypt"===n?this.formatName(r):r:"link"===s?(!e.test.url(u)&&t.error(),r):r}},methods:{formatName(t){let e="";if(2===t.length)e=t.substr(0,1)+"*";else if(t.length>2){let r="";for(let e=0,s=t.length-2;e<s;e++)r+="*";e=t.substr(0,1)+r+t.substr(-1,1)}else e=t;return e}}};exports.value=r;
