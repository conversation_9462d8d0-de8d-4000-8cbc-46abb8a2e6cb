"use strict";const t=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),p=t.defineMixin({props:{type:{type:String,default:()=>e.props.text.type},show:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.text.show},text:{type:[String,Number],default:()=>e.props.text.text},prefixIcon:{type:String,default:()=>e.props.text.prefixIcon},suffixIcon:{type:String,default:()=>e.props.text.suffixIcon},mode:{type:String,default:()=>e.props.text.mode},href:{type:String,default:()=>e.props.text.href},format:{type:[String,Function],default:()=>e.props.text.format},call:{type:Boolean,default:()=>e.props.text.call},openType:{type:String,default:()=>e.props.text.openType},bold:{type:<PERSON><PERSON>an,default:()=>e.props.text.bold},block:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.text.block},lines:{type:[String,Number],default:()=>e.props.text.lines},color:{type:String,default:()=>e.props.text.color},size:{type:[String,Number],default:()=>e.props.text.size},iconStyle:{type:[Object,String],default:()=>e.props.text.iconStyle},decoration:{tepe:String,default:()=>e.props.text.decoration},margin:{type:[Object,String,Number],default:()=>e.props.text.margin},lineHeight:{type:[String,Number],default:()=>e.props.text.lineHeight},align:{type:String,default:()=>e.props.text.align},wordWrap:{type:String,default:()=>e.props.text.wordWrap},flex1:{type:Boolean,default:()=>e.props.text.flex1}}});exports.props=p;
