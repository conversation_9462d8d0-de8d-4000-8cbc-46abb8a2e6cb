"use strict";const o=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),e=require("../../libs/mixin/mixin.js"),i=require("../../libs/function/index.js"),n=require("../../../../common/vendor.js"),s={name:"u-loadmore",mixins:[t.mpMixin,e.mixin,o.props],data:()=>({dotText:"●"}),computed:{loadTextStyle(){return{color:this.color,fontSize:i.addUnit(this.fontSize),lineHeight:i.addUnit(this.fontSize),backgroundColor:this.bgColor}},showText(){let o="";return o="loadmore"==this.status?this.loadmoreText:"loading"==this.status?this.loadingText:"nomore"==this.status&&this.isDot?this.dotText:this.nomoreText,o}},emits:["loadmore"],methods:{addStyle:i.addStyle,addUnit:i.addUnit,loadMore(){"loadmore"==this.status&&this.$emit("loadmore")}}};if(!Array){(n.resolveComponent("u-line")+n.resolveComponent("u-loading-icon"))()}Math||((()=>"../u-line/u-line.js")+(()=>"../u-loading-icon/u-loading-icon.js"))();const d=n._export_sfc(s,[["render",function(o,t,e,i,s,d){return n.e({a:o.line},o.line?{b:n.p({length:"140rpx",color:o.lineColor,hairline:!1,dashed:o.dashed})}:{},{c:"loading"===o.status&&o.icon},"loading"===o.status&&o.icon?{d:n.p({color:o.iconColor,size:o.iconSize,mode:o.loadingIcon})}:{},{e:n.t(d.showText),f:n.s(d.loadTextStyle),g:n.n("nomore"==o.status&&1==o.isDot?"u-loadmore__content__dot-text":"u-loadmore__content__text"),h:n.o(((...o)=>d.loadMore&&d.loadMore(...o))),i:n.n("loadmore"==o.status||"nomore"==o.status?"u-more":""),j:o.line},o.line?{k:n.p({length:"140rpx",color:o.lineColor,hairline:!1,dashed:o.dashed})}:{},{l:n.s(d.addStyle(o.customStyle)),m:n.s({backgroundColor:o.bgColor,marginBottom:d.addUnit(o.marginBottom),marginTop:d.addUnit(o.marginTop),height:d.addUnit(o.height)})})}],["__scopeId","data-v-adef30d2"]]);wx.createComponent(d);
