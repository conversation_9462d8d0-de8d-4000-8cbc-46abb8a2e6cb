<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
      <!-- 第一行：活动名称和活动地点 -->
      <el-form-item label="活动名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动地点" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入活动地点"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <!-- 第二行：报名状态、搜索和重置按钮 -->
      <el-form-item label="报名状态" prop="registrationStatus">
        <el-select
          v-model="queryParams.registrationStatus"
          placeholder="请选择报名状态"
          clearable
          style="width: 120px"
        >
          <el-option label="未开始" value="0" />
          <el-option label="报名中" value="1" />
          <el-option label="已结束" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['content:event:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['content:event:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['content:event:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['content:event:export']"
        >导出</el-button>
      </el-col>
      
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="eventList" 
      row-key="id" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动ID" align="center" prop="id" width="80" />
      <el-table-column label="活动名称" align="center" prop="title" show-overflow-tooltip />
      <!-- 报名情况列 -->
      <el-table-column label="报名情况" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.maxParticipants > 0">
            {{ scope.row.registeredCount || 0 }} / {{ scope.row.maxParticipants }}
          </span>
          <span v-else>
            {{ scope.row.registeredCount || 0 }} / 不限制
          </span>
        </template>
      </el-table-column>
      <el-table-column label="活动地点" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ formatEventLocation(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名状态" align="center" width="100">
         <template #default="scope">
            <el-tag :type="getRegistrationStatusType(scope.row.registrationStatus)">
              {{ getRegistrationStatusText(scope.row.registrationStatus) }}
            </el-tag>
         </template>
      </el-table-column>
      <el-table-column label="是否热门" align="center" prop="isHot" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isHot"
            :active-value="1"
            :inactive-value="0"
            @change="(value) => handleHotStatusChange(scope.row, value)"
            :loading="scope.row.hotStatusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column label="是否推广" align="center" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isPromoted"
            :active-value="1"
            :inactive-value="0"
            @change="handleChangePromotionStatus(scope.row)"
            :loading="scope.row.promotionStatusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column label="精选排序" align="center" width="150">
        <template #default="scope">
          <el-input-number
            v-if="scope.row.isHot === 1"
            v-model="scope.row.sortOrder"
            :min="0"
            :controls="false"
            controls-position="right"
            style="width: 100px"
            @change="handleSortOrderChange(scope.row)"
          />
          <!-- 非热门活动显示横杠 -->
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-dropdown trigger="click">
            <el-button link type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  icon="View" 
                  @click="handleViewRegistrations(scope.row)" 
                  v-hasPermi="['content:event:query']">
                  查看报名
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Edit" 
                  @click="handleUpdate(scope.row)" 
                  v-hasPermi="['content:event:edit']">
                  修改
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Delete" 
                  @click="handleDelete(scope.row)" 
                  v-hasPermi="['content:event:remove']"
                  :style="{ color: 'var(--el-color-danger)' }">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 活动表单对话框组件 -->
    <EventFormDialog ref="eventDialogRef" @success="getList" />

    <!-- 报名列表对话框组件 -->
    <RegistrationListDialog ref="registrationDialogRef" />

    <!-- V3.0版本移除推广配置对话框，推广配置移至活动编辑页面 -->




  </div>
</template>

<script setup name="Event">
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();

// 图标导入
import { User } from '@element-plus/icons-vue';

// API导入
import { listEvent, delEvent, changeEventHotStatus, changeEventPromotionStatus, changeEventSortOrder } from "@/api/content/event";

// 组件导入
import EventFormDialog from '@/components/Event/EventFormDialog.vue';
import RegistrationListDialog from '@/components/Event/RegistrationListDialog.vue';

// 字典
const { hongda_event_status } = proxy.useDict('hongda_event_status');

// 组件引用
const eventDialogRef = ref(null);
const registrationDialogRef = ref(null);

/**
 * 格式化活动地点 - 后台管理页面显示完整省市信息
 * @param {Object} event 活动对象
 * @returns {String} 格式化后的地点信息
 */
const formatEventLocation = (event) => {
  // 直辖市处理：如果省市相同，只显示一个
  if (event.province && event.city) {
    // 判断是否为直辖市（省市名称相同或相似）
    const isDirectMunicipality = 
      event.province === event.city || 
      event.province.replace(/市$/, '') === event.city.replace(/市$/, '');
    
    if (isDirectMunicipality) {
      // 直辖市：只显示省份名称（保留"市"字）
      return event.province;
    } else {
      // 普通省市：显示省+市
      return `${event.province}${event.city}`;
    }
  }
  
  // 如果只有省份，直接使用
  if (event.province && event.province.trim()) {
    return event.province.trim();
  }
  
  // 如果只有city字段，直接使用
  if (event.city && event.city.trim()) {
    return event.city.trim();
  }
  
  // 如果只有完整地址，尝试提取省市信息
  if (event.location && event.location.trim()) {
    const location = event.location.trim();
    // 简单的省市提取逻辑，匹配常见的省市格式
    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);
    if (match) {
      const province = match[1];
      const city = match[2];
      if (city && province !== city) {
        return `${province}${city}`;
      }
      return province;
    }
  }
  
  return '待定';
};

// 基础状态
const eventList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// V3.0版本移除推广配置对话框相关数据，推广配置移至活动编辑页面

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    location: null,
    registrationStatus: null,
  }
});

const { queryParams } = toRefs(data);





// 通用API调用函数
const apiCall = async (apiFunc, params = null) => {
  try {
    return await apiFunc(params);
  } catch (error) {
    proxy.$modal.msgError(error.message || "操作失败");
    throw error;
  }
};





// 事件处理方法
function getList() {
  loading.value = true;

  // 创建一个不包含前端筛选参数的查询对象，用于获取所有数据进行前端分页
  const backendQuery = {
    pageNum: 1,
    pageSize: 9999, // 获取所有数据
    title: queryParams.value.title,
    location: queryParams.value.location
  };

  apiCall(listEvent, backendQuery).then(response => {
    let allRows = response.rows || [];

    // 前端筛选：按报名状态过滤
    if (queryParams.value.registrationStatus !== null && queryParams.value.registrationStatus !== '') {
      const targetRegistrationStatus = parseInt(queryParams.value.registrationStatus);
      allRows = allRows.filter(event => {
        return event.registrationStatus === targetRegistrationStatus;
      });
    }

    // 前端分页
    const pageSize = queryParams.value.pageSize;
    const pageNum = queryParams.value.pageNum;
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    eventList.value = allRows.slice(startIndex, endIndex);
    total.value = allRows.length; // 筛选后的总数
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}





function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  eventDialogRef.value.openDialog();
}

function handleUpdate(row) {
  const eventId = row?.id || ids.value[0];
  eventDialogRef.value.openDialog(eventId);
}



function handleDelete(row) {
  const _ids = row?.id || ids.value;
  proxy.$modal.confirm('是否确认删除活动管理编号为"' + _ids + '"的数据项？').then(() => {
    return apiCall(delEvent, _ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('content/event/export', {
    ...queryParams.value
  }, `event_${new Date().getTime()}.xlsx`);
}





function handleViewRegistrations(row) {
  registrationDialogRef.value.openDialog(row);
}



async function handleHotStatusChange(row, newValue) {
  const statusText = newValue === 1 ? '热门' : '普通';
  
  try {
    await proxy.$modal.confirm(`确认将活动"${row.title}"设置为${statusText}活动？`, "状态变更", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
        
    row.hotStatusLoading = true;
    
    await apiCall(changeEventHotStatus, {
      id: row.id,
      isHot: newValue
    });
    
    proxy.$modal.msgSuccess(`活动状态已更新为${statusText}活动`);
    
  } catch (error) {
    if (error === 'cancel') {
      row.isHot = row.isHot === 1 ? 0 : 1;
    } else {
      row.isHot = row.isHot === 1 ? 0 : 1;
      proxy.$modal.msgError("状态更新失败，请稍后重试");
    }
  } finally {
    row.hotStatusLoading = false;
  }
}

/**
 * 处理推广状态变更 - V3.0简化版
 * 直接切换推广状态，不涉及推广配置内容
 */
async function handleChangePromotionStatus(row) {
  const statusText = row.isPromoted === 1 ? '开启推广' : '关闭推广';
  
  try {
    await proxy.$modal.confirm(`确认${statusText}活动"${row.title}"？`, "推广状态变更", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    
    // 如果是开启推广，需要检查推广图片和排序是否已配置
    if (row.isPromoted === 1) {
      if (!row.promotionImageUrl || row.sortOrder === null || row.sortOrder === undefined) {
        row.isPromoted = 0; // 恢复开关状态
        proxy.$modal.msgError("请先在编辑页面配置推广图片和推广顺序后再开启推广");
        return;
      }
    }
        
    row.promotionStatusLoading = true;
    
    // 只传递ID和推广状态，不涉及其他推广配置
    await apiCall(changeEventPromotionStatus, {
      id: row.id,
      isPromoted: row.isPromoted
    });
    
    proxy.$modal.msgSuccess(`活动推广状态已${statusText.replace('推广', '')}`);
    
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消，恢复开关状态
      row.isPromoted = row.isPromoted === 1 ? 0 : 1;
    } else {
      // 操作失败，恢复开关状态
      row.isPromoted = row.isPromoted === 1 ? 0 : 1;
      proxy.$modal.msgError("推广状态切换失败，请稍后重试");
    }
  } finally {
    row.promotionStatusLoading = false;
  }
}

// V3.0版本移除了复杂的推广配置对话框相关方法
// 推广配置现在在活动编辑页面处理，这里只保留状态切换功能



/**
 * 获取报名状态标签类型
 */
const getRegistrationStatusType = (status) => {
  switch (status) {
    case 0: return 'warning';  // 未开始 - 橙色
    case 1: return 'success';  // 报名中 - 绿色
    case 2: return 'info';     // 已结束 - 灰色
    default: return 'info';
  }
};

/**
 * 获取报名状态文本
 */
const getRegistrationStatusText = (status) => {
  switch (status) {
    case 0: return '未开始';
    case 1: return '报名中';
    case 2: return '已结束';
    default: return '未知';
  }
};

/**
 * 处理精选排序值变化的函数
 * @param {object} row - 当前操作的行数据
 */
async function handleSortOrderChange(row) {
  // 临时存储修改前的值，用于失败时回滚UI
  const originalSortOrder = row.sortOrder;

  try {
    await changeEventSortOrder({
      id: row.id,
      sortOrder: row.sortOrder
    });
    // 成功反馈
    proxy.$modal.msgSuccess("排序更新成功");
    
    // 如果希望立即看到排序效果，可以重新获取列表
    // getList();

  } catch (error) {
    // 失败反馈
    proxy.$modal.msgError("更新失败，请重试");
    
    // 关键：将界面的值恢复到修改前的状态，保持UI与数据一致
    row.sortOrder = originalSortOrder;
  }
}



// 初始化
getList();
</script>

<style scoped>
/* 主组件样式，保持简洁 */
</style>
