"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),a=require("../../libs/mixin/mixin.js"),r=require("../../libs/config/props.js"),i=require("../../libs/config/color.js"),l=require("../../libs/function/index.js"),s=require("../../../../common/vendor.js"),o={name:"u-form-item",mixins:[t.mpMixin,a.mixin,e.props],data:()=>({message:"",parentData:{labelPosition:"left",labelAlign:"left",labelStyle:{},labelWidth:45,errorType:"message"},color:i.color,itemRules:[]}),computed:{propsLine:()=>r.props.line},mounted(){this.init()},emits:["click"],watch:{rules:{immediate:!0,handler(e){this.setRules(e)}}},methods:{addStyle:l.addStyle,addUnit:l.addUnit,init(){this.updateParentData(),this.parent},setRules(e){0!==e.length?this.itemRules=e:this.itemRules=[]},updateParentData(){this.getParentData("u-form")},clearValidate(){this.message=null},resetField(){const e=l.getProperty(this.parent.originalModel,this.prop);l.setProperty(this.parent.model,this.prop,e),this.message=null},clickHandler(){this.$emit("click")}}};if(!Array){(s.resolveComponent("u-icon")+s.resolveComponent("u-line"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-line/u-line.js"))();const n=s._export_sfc(o,[["render",function(e,t,a,r,i,l){return s.e({a:e.required||e.leftIcon||e.label},e.required||e.leftIcon||e.label?s.e({b:e.required},(e.required,{}),{c:e.leftIcon},e.leftIcon?{d:s.p({name:e.leftIcon,"custom-style":e.leftIconStyle})}:{},{e:s.t(e.label),f:s.s(i.parentData.labelStyle),g:s.s({justifyContent:"left"===i.parentData.labelAlign?"flex-start":"center"===i.parentData.labelAlign?"center":"flex-end"}),h:l.addUnit(e.labelWidth||i.parentData.labelWidth),i:"left"===i.parentData.labelPosition?0:"5px"}):{},{j:e.$slots.right},(e.$slots.right,{}),{k:s.o(((...e)=>l.clickHandler&&l.clickHandler(...e))),l:s.s(l.addStyle(e.customStyle)),m:s.s({flexDirection:"left"===(e.labelPosition||i.parentData.labelPosition)?"row":"column"}),n:!!i.message&&"message"===i.parentData.errorType},i.message&&"message"===i.parentData.errorType?{o:s.t(i.message),p:l.addUnit("top"===i.parentData.labelPosition?0:e.labelWidth||i.parentData.labelWidth)}:{},{q:e.borderBottom},e.borderBottom?{r:s.p({color:i.message&&"border-bottom"===i.parentData.errorType?i.color.error:l.propsLine.color,customStyle:`margin-top: ${i.message&&"message"===i.parentData.errorType?"5px":0}`})}:{},{s:i.message&&"message"===i.parentData.errorType?1:""})}],["__scopeId","data-v-4bcba634"]]);wx.createComponent(n);
