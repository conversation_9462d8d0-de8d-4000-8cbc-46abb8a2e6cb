<template>
  <view class="bottom-action-bar" v-if="!isLoading && eventDetail">
    <up-button
      open-type="share"
      @click="$emit('share')"
      :customStyle="{
        width: '214rpx',
        height: '76rpx',
        margin: '0',
        padding: '0',
        border: 'none',
        backgroundColor: 'rgba(42, 97, 241, 0.2)',
        borderRadius: '38rpx'
      }"
    >
      <view class="share-button-content">
        <image class="share-icon" :src="shareIconUrl" mode="aspectFit"></image>
        <text class="share-text">分享</text>
      </view>
    </up-button>

    <up-button
      type="primary"
      :disabled="isButtonDisabled"
      @click="$emit('register')"
      :customStyle="{
        width: '464rpx',
        height: '76rpx',
        margin: '0',
        backgroundColor: registrationStatus === 'registered' ? '#C4CFD1' : (isButtonDisabled ? '#cccccc' : '#023F98'),
        border: 'none',
        borderRadius: '38rpx',
        color: '#ffffff',
        fontFamily: 'Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif',
        fontWeight: 'normal',
        fontSize: '28rpx'
      }"
    >
      {{ buttonText }}
    </up-button>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

defineProps({
  eventDetail: { type: Object, required: true },
  isLoading: { type: Boolean, default: false },
  registrationStatus: { type: String, required: true },
  isButtonDisabled: { type: Boolean, required: true },
  buttonText: { type: String, required: true }
})

// 静态资源 URL（不再使用本地兜底）
const shareIconUrl = ref('')

// 组件挂载时读取静态资源配置
onMounted(() => {
  const assets = uni.getStorageSync('staticAssets')
  
  shareIconUrl.value = assets?.detail_icon_share || ''
})
</script>

<style lang="scss" scoped>
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 100;
  /* 兼容性处理：为不支持env()的环境提供兜底值 */
  height: 156rpx;
  height: calc(156rpx + env(safe-area-inset-bottom, 0rpx));
  background-color: #FFFFFF;
  border-top: 2rpx solid #EEEEEE;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  /* 兼容性处理：为不支持env()的环境提供兜底值 */
  padding-bottom: 0rpx;
  padding-bottom: env(safe-area-inset-bottom, 0rpx);
}

.share-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.share-text {
  width: 56rpx;
  height: 44rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #023F98;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.share-button-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

:deep(.up-button--square) {
  border-radius: 10rpx !important;
}

:deep(.up-button--primary) {
  border-radius: 44rpx !important;
}
</style>

