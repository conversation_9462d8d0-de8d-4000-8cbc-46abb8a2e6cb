"use strict";const e=require("../../libs/vue.js"),r=require("../../libs/config/props.js"),t=e.defineMixin({props:{list:{type:Array,default:()=>r.props.swiper.list},indicator:{type:Boolean,default:()=>r.props.swiper.indicator},indicatorActiveColor:{type:String,default:()=>r.props.swiper.indicatorActiveColor},indicatorInactiveColor:{type:String,default:()=>r.props.swiper.indicatorInactiveColor},indicatorStyle:{type:[String,Object],default:()=>r.props.swiper.indicatorStyle},indicatorMode:{type:String,default:()=>r.props.swiper.indicatorMode},autoplay:{type:Boolean,default:()=>r.props.swiper.autoplay},current:{type:[String,Number],default:()=>r.props.swiper.current},currentItemId:{type:String,default:()=>r.props.swiper.currentItemId},interval:{type:[String,Number],default:()=>r.props.swiper.interval},duration:{type:[String,Number],default:()=>r.props.swiper.duration},circular:{type:Boolean,default:()=>r.props.swiper.circular},previousMargin:{type:[String,Number],default:()=>r.props.swiper.previousMargin},nextMargin:{type:[String,Number],default:()=>r.props.swiper.nextMargin},acceleration:{type:Boolean,default:()=>r.props.swiper.acceleration},displayMultipleItems:{type:Number,default:()=>r.props.swiper.displayMultipleItems},easingFunction:{type:String,default:()=>r.props.swiper.easingFunction},keyName:{type:String,default:()=>r.props.swiper.keyName},imgMode:{type:String,default:()=>r.props.swiper.imgMode},height:{type:[String,Number],default:()=>r.props.swiper.height},bgColor:{type:String,default:()=>r.props.swiper.bgColor},radius:{type:[String,Number],default:()=>r.props.swiper.radius},loading:{type:Boolean,default:()=>r.props.swiper.loading},showTitle:{type:Boolean,default:()=>r.props.swiper.showTitle}}});exports.props=t;
