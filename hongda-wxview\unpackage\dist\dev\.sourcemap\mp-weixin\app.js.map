{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n// 1. 导入API方法 (保留，用于加载图片等其他资源)\r\nimport { getAllAssets } from '@/api/platform/asset.js';\r\n\r\nexport default {\r\n  onLaunch: function () {\r\n    console.log('App Launch');\r\n\r\n    // 【优化】分离资源加载和字体加载\r\n    this.fetchAndCacheAssets(); // 继续负责图片等后台资源\r\n    this.loadCdnFonts();        // 【新增】专门负责从CDN加载所有字体\r\n    this.loadIconFont();        // 保留uview-plus图标字体的加载\r\n  },\r\n  onShow: function () {\r\n    console.log('App Show');\r\n  },\r\n  onHide: function () {\r\n    console.log('App Hide');\r\n  },\r\n  methods: {\r\n    /**\r\n     * 【新增】从CDN加载所有自定义字体\r\n     * 由于后台系统只能上传文件，我们在此处直接指定稳定高效的jsDelivr CDN地址\r\n     */\r\n    loadCdnFonts() {\r\n      const fontsToLoad = [\r\n        {\r\n          family: 'Alibaba PuHuiTi 3.0',\r\n          // 阿里巴巴普惠体】jsDelivr CDN地址\r\n          source: 'url(\"https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/AlibabaPuHuiTi-3-55-Regular.subset.woff2\")'\r\n        },\r\n        {\r\n          family: 'YouSheBiaoTiHei',\r\n          //优设标题黑】jsDelivr CDN地址\r\n          source: 'url(\"https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/YouSheBiaoTiHei.subset.woff2\")'\r\n        }\r\n      ];\r\n\r\n      let loadedCount = 0;\r\n      const totalFonts = fontsToLoad.length;\r\n\r\n      fontsToLoad.forEach(font => {\r\n        uni.loadFontFace({\r\n          global: true,\r\n          family: font.family,\r\n          source: font.source,\r\n          success: () => {\r\n            console.log(`CDN字体 [${font.family}] 加载成功!`);\r\n            loadedCount++;\r\n\r\n            // 当所有字体都加载完成时，触发全局事件和设置全局状态\r\n            if (loadedCount === totalFonts) {\r\n              // 设置全局状态\r\n              this.globalData = this.globalData || {};\r\n              this.globalData.fontLoaded = true;\r\n\r\n              // 触发全局事件，通知所有组件字体已加载完成\r\n              uni.$emit('customFontsLoaded');\r\n              console.log('所有自定义字体加载完成，已触发全局事件');\r\n            }\r\n          },\r\n          fail(err) {\r\n            console.error(`CDN字体 [${font.family}] 加载失败:`, err);\r\n            loadedCount++;\r\n\r\n            // 即使有字体加载失败，也要检查是否所有字体都处理完了\r\n            if (loadedCount === totalFonts) {\r\n              this.globalData = this.globalData || {};\r\n              this.globalData.fontLoaded = true;\r\n              uni.$emit('customFontsLoaded');\r\n              console.log('字体加载处理完成（部分可能失败），已触发全局事件');\r\n            }\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 【保留并优化】获取、缓存静态资源（不再负责字体）\r\n     * 此函数继续为App中的图片、背景等资源服务\r\n     */\r\n    async fetchAndCacheAssets() {\r\n      try {\r\n        const response = await getAllAssets();\r\n        if (response.code === 200 && Array.isArray(response.data)) {\r\n          const assetMap = response.data.reduce((map, item) => {\r\n            if (item.assetKey && item.assetUrl) {\r\n              map[item.assetKey] = item.assetUrl;\r\n            }\r\n            return map;\r\n          }, {});\r\n\r\n          uni.setStorageSync('staticAssets', assetMap);\r\n          console.log('小程序图片等静态资源已更新并缓存成功！');\r\n          // 【已移除】不再从此函数中调用旧的字体加载方法\r\n        }\r\n      } catch (error) {\r\n        console.error('获取小程序静态资源失败', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 加载uView图标字体 (保留)\r\n     */\r\n    loadIconFont() {\r\n      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\r\n      uni.loadFontFace({\r\n        global: true,\r\n        family: 'uicon-iconfont',\r\n        source: 'url(\"https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf\")',\r\n        success() {\r\n          console.log('uview-plus图标字体加载成功');\r\n        },\r\n        fail(err) {\r\n          console.error('uview-plus图标字体加载失败:', err);\r\n          uni.loadFontFace({\r\n            global: true,\r\n            family: 'uicon-iconfont',\r\n            source: 'url(\"https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf\")',\r\n            success() {\r\n              console.log('备用图标字体加载成功');\r\n            },\r\n            fail(err2) {\r\n              console.error('备用图标字体也加载失败:', err2);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      // #endif\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 每个页面公共css */\r\n\r\n/* 防止页面跳转时内容残留 */\r\nuni-page-wrapper {\r\n  overflow: hidden !important;\r\n}\r\nuni-page-body {\r\n  overflow: hidden !important;\r\n}\r\n\r\n/* 【修改】更新全局字体栈，将新字体作为最高优先级 */\r\nbody, page, view, text {\r\n  font-family: 'Alibaba PuHuiTi 3.0', sans-serif;\r\n}\r\n</style>\r\n", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nimport uviewPlus from '@/uni_modules/uview-plus'\r\n\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\r\n\t// 保留您原有的 uview-plus 插件配置\r\n\tapp.use(uviewPlus, () => {\r\n\t\treturn {\r\n\t\t\toptions: {\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\tunit: 'rpx'\r\n\t\t\t\t},\r\n\t\t\t\tprops: {\r\n\t\t\t\t\t// ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\r\n// #endif"], "names": ["uni", "getAllAssets", "createSSRApp", "App", "uviewPlus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,MAAK,YAAU;AAAA,EACb,UAAU,WAAY;AACpBA,kBAAAA,mCAAY,YAAY;AAGxB,SAAK,oBAAmB;AACxB,SAAK,aAAY;AACjB,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,IAKP,eAAe;AACb,YAAM,cAAc;AAAA,QAClB;AAAA,UACE,QAAQ;AAAA;AAAA,UAER,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,QAAQ;AAAA;AAAA,UAER,QAAQ;AAAA,QACV;AAAA;AAGF,UAAI,cAAc;AAClB,YAAM,aAAa,YAAY;AAE/B,kBAAY,QAAQ,UAAQ;AAC1BA,sBAAAA,MAAI,aAAa;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,SAAS,MAAM;AACbA,gCAAY,MAAA,OAAA,iBAAA,UAAU,KAAK,MAAM,SAAS;AAC1C;AAGA,gBAAI,gBAAgB,YAAY;AAE9B,mBAAK,aAAa,KAAK,cAAc,CAAA;AACrC,mBAAK,WAAW,aAAa;AAG7BA,kCAAI,MAAM,mBAAmB;AAC7BA,4BAAAA,MAAY,MAAA,OAAA,iBAAA,qBAAqB;AAAA,YACnC;AAAA,UACD;AAAA,UACD,KAAK,KAAK;AACRA,0BAAAA,sCAAc,UAAU,KAAK,MAAM,WAAW,GAAG;AACjD;AAGA,gBAAI,gBAAgB,YAAY;AAC9B,mBAAK,aAAa,KAAK,cAAc,CAAA;AACrC,mBAAK,WAAW,aAAa;AAC7BA,kCAAI,MAAM,mBAAmB;AAC7BA,4BAAAA,MAAY,MAAA,OAAA,iBAAA,0BAA0B;AAAA,YACxC;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,MAAM,sBAAsB;AAC1B,UAAI;AACF,cAAM,WAAW,MAAMC,mBAAAA;AACvB,YAAI,SAAS,SAAS,OAAO,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,gBAAM,WAAW,SAAS,KAAK,OAAO,CAAC,KAAK,SAAS;AACnD,gBAAI,KAAK,YAAY,KAAK,UAAU;AAClC,kBAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,YAC5B;AACA,mBAAO;AAAA,UACR,GAAE,CAAE,CAAA;AAELD,wBAAAA,MAAI,eAAe,gBAAgB,QAAQ;AAC3CA,wBAAAA,MAAY,MAAA,OAAA,iBAAA,qBAAqB;AAAA,QAEnC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iBAAc,eAAe,KAAK;AAAA,MACpC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AAEbA,oBAAAA,MAAI,aAAa;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AACRA,wBAAAA,MAAY,MAAA,OAAA,kBAAA,oBAAoB;AAAA,QACjC;AAAA,QACD,KAAK,KAAK;AACRA,wBAAA,MAAA,MAAA,SAAA,kBAAc,uBAAuB,GAAG;AACxCA,wBAAAA,MAAI,aAAa;AAAA,YACf,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,UAAU;AACRA,4BAAAA,MAAA,MAAA,OAAA,kBAAY,YAAY;AAAA,YACzB;AAAA,YACD,KAAK,MAAM;AACTA,4BAAc,MAAA,MAAA,SAAA,kBAAA,gBAAgB,IAAI;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IAEF;AAAA,EACH;AACF;AClHO,SAAS,YAAY;AAC3B,QAAM,MAAME,cAAY,aAACC,SAAG;AAG5B,MAAI,IAAIC,4BAAAA,WAAW,MAAM;AACxB,WAAO;AAAA,MACN,SAAS;AAAA,QACR,QAAQ;AAAA,UACP,MAAM;AAAA,QACN;AAAA,QACD,OAAO;AAAA;AAAA,QAEN;AAAA,MACD;AAAA,IACD;AAAA,EACH,CAAE;AAED,SAAO;AAAA,IACN;AAAA,EACA;AACF;;;"}