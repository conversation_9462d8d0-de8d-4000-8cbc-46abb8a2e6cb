"use strict";const e=require("../../../../common/vendor.js"),t=require("./parser.js"),n=[],i={name:"mp-html",data:()=>({nodes:[]}),props:{containerStyle:{type:String,default:""},content:{type:String,default:""},copyLink:{type:[Boolean,String],default:!0},domain:String,errorImg:{type:String,default:""},lazyLoad:{type:[Boolean,String],default:!1},loadingImg:{type:String,default:""},pauseVideo:{type:[<PERSON>olean,String],default:!0},previewImg:{type:[Boolean,String],default:!0},scrollTable:[Boolean,String],selectable:[Boolean,String],setTitle:{type:[Boolean,String],default:!0},showImgMenu:{type:[Boolean,String],default:!0},tagStyle:Object,useAnchor:[<PERSON><PERSON><PERSON>,<PERSON>]},emits:["load","ready","imgtap","linktap","play","error"],components:{node:()=>"./node/node.js"},watch:{content(e){this.setContent(e)}},created(){this.plugins=[];for(let e=n.length;e--;)this.plugins.push(new n[e](this))},mounted(){this.content&&!this.nodes.length&&this.setContent(this.content)},beforeDestroy(){this._hook("onDetached")},methods:{in(e,t,n){e&&t&&n&&(this._in={page:e,selector:t,scrollTop:n})},navigateTo(t,n){return new Promise(((i,o)=>{if(!this.useAnchor)return void o(Error("Anchor is disabled"));n=n||parseInt(this.useAnchor)||0;let s=" ";s=">>>";const r=e.index.createSelectorQuery().in(this._in?this._in.page:this).select((this._in?this._in.selector:"._root")+(t?`>>>#${t}`:"")).boundingClientRect();this._in?r.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect():r.selectViewport().scrollOffset(),r.exec((t=>{if(!t[0])return void o(Error("Label not found"));const s=t[1].scrollTop+t[0].top-(t[2]?t[2].top:0)+n;this._in?this._in.page[this._in.scrollTop]=s:e.index.pageScrollTo({scrollTop:s,duration:300}),i()}))}))},getText(e){let t="";return function e(n){for(let i=0;i<n.length;i++){const o=n[i];if("text"===o.type)t+=o.text.replace(/&amp;/g,"&");else if("br"===o.name)t+="\n";else{const n="p"===o.name||"div"===o.name||"tr"===o.name||"li"===o.name||"h"===o.name[0]&&o.name[1]>"0"&&o.name[1]<"7";n&&t&&"\n"!==t[t.length-1]&&(t+="\n"),o.children&&e(o.children),n&&"\n"!==t[t.length-1]?t+="\n":"td"!==o.name&&"th"!==o.name||(t+="\t")}}}(e||this.nodes),t},getRect(){return new Promise(((t,n)=>{e.index.createSelectorQuery().in(this).select("#_root").boundingClientRect().exec((e=>e[0]?t(e[0]):n(Error("Root label not found"))))}))},pauseMedia(){for(let e=(this._videos||[]).length;e--;)this._videos[e].pause()},setPlaybackRate(e){this.playbackRate=e;for(let t=(this._videos||[]).length;t--;)this._videos[t].playbackRate(e)},setContent(e,n){n&&this.imgList||(this.imgList=[]);const i=new t.Parser(this).parse(e);if(this.$set(this,"nodes",n?(this.nodes||[]).concat(i):i),this._videos=[],this.$nextTick((()=>{this._hook("onLoad"),this.$emit("load")})),this.lazyLoad||this.imgList._unloadimgs<this.imgList.length/2){let e=0;const t=n=>{n&&n.height||(n={}),n.height===e?this.$emit("ready",n):(e=n.height,setTimeout((()=>{this.getRect().then(t).catch(t)}),350))};this.getRect().then(t).catch(t)}else this.imgList._unloadimgs||this.getRect().then((e=>{this.$emit("ready",e)})).catch((()=>{this.$emit("ready",{})}))},_hook(e){for(let t=n.length;t--;)this.plugins[t][e]&&this.plugins[t][e]()}}};if(!Array){e.resolveComponent("node")()}const o=e._export_sfc(i,[["render",function(t,n,i,o,s,r){return e.e({a:!s.nodes[0]},s.nodes[0]?{b:e.p({childs:s.nodes,opts:[i.lazyLoad,i.loadingImg,i.errorImg,i.showImgMenu,i.selectable],name:"span"})}:{},{c:e.n((i.selectable?"_select ":"")+"_root"),d:e.s(i.containerStyle)})}]]);wx.createComponent(o);
