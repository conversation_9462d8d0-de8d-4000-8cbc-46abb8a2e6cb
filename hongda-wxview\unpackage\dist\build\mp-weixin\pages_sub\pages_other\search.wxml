<view class="page-container data-v-35ce4c98"><view class="custom-header data-v-35ce4c98"><view class="status-bar data-v-35ce4c98"></view><view class="nav-bar data-v-35ce4c98" style="{{c}}"><u-icon wx:if="{{b}}" class="data-v-35ce4c98" bindclick="{{a}}" u-i="35ce4c98-0" bind:__l="__l" u-p="{{b}}"></u-icon><view class="page-title-container data-v-35ce4c98"><text class="page-title data-v-35ce4c98">红大出海</text></view></view><view class="search-section data-v-35ce4c98"><view class="search-input-wrapper data-v-35ce4c98"><u-icon wx:if="{{d}}" class="data-v-35ce4c98" u-i="35ce4c98-1" bind:__l="__l" u-p="{{d}}"></u-icon><input class="search-input data-v-35ce4c98" placeholder="搜索活动、资讯" confirm-type="search" bindconfirm="{{e}}" value="{{f}}" bindinput="{{g}}"/><u-icon wx:if="{{h}}" class="data-v-35ce4c98" bindclick="{{i}}" u-i="35ce4c98-2" bind:__l="__l" u-p="{{j}}"></u-icon></view><text wx:if="{{k}}" class="cancel-btn data-v-35ce4c98" bindtap="{{l}}">取消</text></view></view><view class="tabs-container data-v-35ce4c98"><view class="custom-tabs data-v-35ce4c98"><view wx:for="{{m}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', 'data-v-35ce4c98', tab.c && 'active']}}" bindtap="{{tab.d}}" style="{{tab.e}}">{{tab.a}}</view></view></view><scroll-view scroll-y class="result-list-scroll data-v-35ce4c98" bindscrolltolower="{{y}}"><view wx:if="{{n}}" class="loading-container data-v-35ce4c98"><u-loading-icon wx:if="{{o}}" class="data-v-35ce4c98" u-i="35ce4c98-3" bind:__l="__l" u-p="{{o}}"></u-loading-icon></view><view wx:else class="data-v-35ce4c98"><view wx:if="{{p}}" class="empty-state-container data-v-35ce4c98"><u-empty wx:if="{{q}}" class="data-v-35ce4c98" u-i="35ce4c98-4" bind:__l="__l" u-p="{{r}}"></u-empty><u-empty wx:else class="data-v-35ce4c98" u-i="35ce4c98-5" bind:__l="__l" u-p="{{s||''}}"></u-empty></view><view wx:else class="result-list data-v-35ce4c98"><view wx:for="{{t}}" wx:for-item="item" wx:key="j" class="event-card data-v-35ce4c98" bindtap="{{item.k}}"><view class="card-left data-v-35ce4c98"><image class="event-image data-v-35ce4c98" src="{{item.a}}" mode="aspectFill" lazy-load="{{true}}"></image><view wx:if="{{item.b}}" class="{{['data-v-35ce4c98', 'status-tag', item.d]}}">{{item.c}}</view></view><view class="card-right data-v-35ce4c98"><text class="event-title data-v-35ce4c98">{{item.e}}</text><view class="event-info-row data-v-35ce4c98"><view class="time-location-item data-v-35ce4c98"><image class="event-info-icon data-v-35ce4c98" src="{{v}}" mode="aspectFit"></image><text class="info-text data-v-35ce4c98">{{item.f}}</text></view><view class="time-location-item data-v-35ce4c98"><image class="event-info-icon data-v-35ce4c98" src="{{w}}" mode="aspectFit"></image><text class="info-text data-v-35ce4c98">{{item.g}}</text></view></view><view class="event-info remaining-spots data-v-35ce4c98"><text class="spots-count data-v-35ce4c98">{{item.h}}: {{item.i}}</text></view></view></view><u-loadmore wx:if="{{x}}" class="data-v-35ce4c98" u-i="35ce4c98-6" bind:__l="__l" u-p="{{x}}"/></view></view></scroll-view></view>