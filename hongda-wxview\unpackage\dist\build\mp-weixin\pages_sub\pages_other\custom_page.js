"use strict";const e=require("../../common/vendor.js"),t=require("./api/platform/page.js"),a=require("../../utils/date.js"),l=require("../../utils/mpHtmlStyles.js");if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("mp-html")+e.resolveComponent("u-loading-page"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/mp-html/components/mp-html/mp-html.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js"))();const o={__name:"custom_page",setup(o){const u=e.ref(!0),n=e.ref(null),r=e.ref(0),s=e.ref(0),i=e.ref(0),v=()=>{e.index.navigateBack({delta:1})};return e.onLoad((async a=>{(()=>{try{const t=e.index.getSystemInfoSync();r.value=t.statusBarHeight||20;const a=e.index.getMenuButtonBoundingClientRect();s.value=a.height+2*(a.top-r.value),i.value=a.bottom+(a.top-r.value)}catch(t){r.value=20,s.value=44,i.value=64}})();const l=a.id;if(!l)return u.value=!1,void console.error("页面ID缺失");try{const e=await t.getPageDetailsApi(l);200===e.code&&e.data?n.value=e.data:n.value=null}catch(o){console.error("获取页面详情失败:",o),n.value=null}finally{u.value=!1}})),(t,o)=>e.e({a:n.value&&!u.value},n.value&&!u.value?{b:r.value+"px",c:e.p({name:"arrow-left",color:"#000000",size:"22"}),d:e.o(v),e:e.t(n.value.title||"详情"),f:s.value+"px",g:i.value+"px",h:e.t(n.value.title),i:e.t(e.unref(a.formatDate)(n.value.createTime,"YYYY-MM-DD")),j:e.p({content:n.value.content,"tag-style":e.unref(l.tagStyle),"lazy-load":!0}),k:i.value+"px"}:u.value?{m:e.p({"loading-text":"正在加载...",loading:u.value})}:{},{l:u.value})}},u=e._export_sfc(o,[["__scopeId","data-v-6fc858a0"]]);wx.createPage(u);
