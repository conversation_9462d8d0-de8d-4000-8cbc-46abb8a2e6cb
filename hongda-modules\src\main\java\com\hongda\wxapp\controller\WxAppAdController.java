package com.hongda.wxapp.controller;

import com.hongda.common.annotation.Anonymous;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaAd;
import com.hongda.platform.service.IHongdaAdService;
import com.hongda.wxapp.domain.vo.AdVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Anonymous
@Tag(name = "小程序广告接口", description = "小程序端广告相关接口")
@RestController
@RequestMapping("/api/v1/ad")
public class WxAppAdController extends BaseController {

    @Autowired
    private IHongdaAdService hongdaAdService;

    // Tab Bar 页面列表，用于判断跳转方式
    private static final List<String> TAB_BAR_PAGES = Arrays.asList(
            "/pages/index/index",
            "/pages/article/index",
            "/pages/event/index",
            "/pages/country/index",
            "/pages/profile/index"
    );

    @Operation(summary = "获取广告列表", description = "根据位置代码获取启用状态的广告列表")
    @GetMapping("/list")
    public AjaxResult getAdListByPosition(
            @Parameter(description = "广告位置代码", example = "HOME_BANNER") @RequestParam("positionCode") String positionCode,
            @Parameter(description = "状态(0-禁用,1-启用)", example = "1") @RequestParam(value = "status", defaultValue = "1") Integer status,
            @Parameter(description = "页面大小", example = "10") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        HongdaAd queryParams = new HongdaAd();
        queryParams.setPositionCode(positionCode);
        queryParams.setStatus(status);

        List<HongdaAd> adList = hongdaAdService.selectHongdaAdList(queryParams);

        Date now = new Date();
        List<HongdaAd> validAdList = adList.stream()
                .filter(ad -> {
                    if (ad.getStartTime() != null && now.before(ad.getStartTime())) return false;
                    if (ad.getEndTime() != null && now.after(ad.getEndTime())) return false;
                    return true;
                })
                .collect(Collectors.toList());

        List<AdVO> voList = validAdList.stream().map(ad -> {
            AdVO vo = new AdVO();
            BeanUtils.copyProperties(ad, vo);

            String linkType = ad.getLinkType();
            String linkTarget = ad.getLinkTarget();

            if (linkType == null || linkTarget == null) {
                return vo;
            }

            switch (linkType) {
                case "ARTICLE":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_article/detail?id=" + linkTarget);
                    break;
                case "EVENT":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_event/detail?id=" + linkTarget);
                    break;
                case "COUNTRY":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_country/detail?id=" + linkTarget);
                    break;
                case "PARK":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_other/park_detail?id=" + linkTarget);
                    break;
                case "CUSTOM_PAGE":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_other/custom_page?id=" + linkTarget);
                    break;
                // [新增] 增加对国别政策详情页的处理
                case "COUNTRY_POLICY":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_country/policy_detail?id=" + linkTarget);
                    break;
                case "PAGE":
                    if (TAB_BAR_PAGES.contains(linkTarget.split("\\?")[0])) {
                        vo.setLinkType("TAB_PAGE");
                    } else {
                        vo.setLinkType("INTERNAL_PAGE");
                    }
                    vo.setLinkTarget(linkTarget);
                    break;
                case "EXTERNAL":
                    vo.setLinkType("EXTERNAL_LINK");
                    vo.setLinkTarget(linkTarget);
                    break;
                case "NONE":
                    break;
            }
            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(voList);
    }
}