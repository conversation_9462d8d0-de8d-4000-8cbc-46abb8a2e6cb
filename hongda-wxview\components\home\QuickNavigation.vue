<template>
  <view v-if="navList.length > 0" class="nav-container">
    <view class="nav-grid">
      <view
          v-for="item in navList"
          :key="item.id"
          class="nav-item"
          @click="handleNavClick(item)"
      >
        <view class="nav-icon-wrapper">
          <image class="nav-icon" :src="item.iconUrl" mode="aspectFill"></image>
        </view>
        <text class="nav-text">{{ item.title }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getNavListByPosition } from '@/api/platform/nav.js';
import { navigateTo } from '@/utils/navigation.js';

const navList = ref([]);

const fetchNavData = async () => {
  try {
    const res = await getNavListByPosition('HOME_QUICK_NAV');
    if (res && res.data) {
      navList.value = res.data;
    }
  } catch (error) {
    console.error('获取快捷导航失败:', error);
  }
};

const handleNavClick = (navItem) => {
  navigateTo(navItem);
};

onMounted(() => {
  fetchNavData();
});
</script>

<style lang="scss" scoped>
.nav-container {
  width: 100%;
  background-color: #ffffff;
  padding: 24rpx 0rpx;
  box-sizing: border-box;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  /* [修改] 将行间距（文字与下方图标的距离）设置为 24rpx */
  row-gap: 24rpx;
  /* [修改] 将列间距恢复到一个更协调的值 */
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* [修改] 将图标与对应文字的间距设置为 18rpx */
  gap: 18rpx;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.nav-icon-wrapper {
  /* 图标容器宽高仍为 80rpx */
  width: 80rpx;
  height: 80rpx;
  border-radius: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f8fafc;
}

.nav-icon {
  width: 100%;
  height: 100%;
}

.nav-text {
  font-size: 24rpx;
  color: #23232A;
  text-align: center;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>