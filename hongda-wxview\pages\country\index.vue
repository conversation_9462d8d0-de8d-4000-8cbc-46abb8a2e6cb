<template>
  <view class="page-container">
    <view class="header-background" :style="{ backgroundImage: `url(${headerBgUrl})` }">
      <view class="page-title-wrapper">
        <text class="page-title">国别列表</text>
      </view>

      <view class="search-bar-wrapper">
        <uni-search-bar
            class="search-bar"
            placeholder="搜索国别名称"
            v-model="searchKeyword"
            @confirm="onSearch"
            @cancel="onCancelSearch"
            @clear="onSearch"
            radius="100"
            bgColor="#ffffff"
        />
      </view>

      <view class="continent-tabs-container">
        <scroll-view
            class="continent-tabs"
            scroll-x="true"
            :show-scrollbar="false"
            @scroll="onTabsScroll"
            :scroll-left="tabScrollLeft"
            scroll-with-animation
        >
          <view
              v-for="(tab, index) in continents"
              :key="tab.value"
              :class="['tab-item', { active: activeContinent === tab.value }]"
              @click="onFilterTap(tab.value, index)"
              :id="'tab-' + index"
              :style="{ backgroundImage: activeContinent === tab.value ? `url(${activeTabBgUrl})` : 'none' }"
          >
            {{ tab.label }}
          </view>
        </scroll-view>
        <view class="active-indicator" :style="{ left: indicatorLeft + 'px', opacity: showIndicator ? 1 : 0 }"></view>
      </view>
    </view>

    <scroll-view class="country-list-wrapper" scroll-y @scrolltolower="loadMore">
      <view class="country-card" v-for="item in countryList" :key="item.id" @click="goToDetail(item.id)">
        <image class="country-image" :src="baseUrl + item.listCoverUrl" mode="aspectFill" />

        <view class="country-info">
          <view class="info-top">
            <view class="name-line">
              <text class="name-cn">{{ item.nameCn }}</text>
              <text class="name-en">{{ item.nameEn }}</text>
            </view>
            <text class="summary">{{ item.summary }}</text>
          </view>
          <image class="country-flag" :src="baseUrl + item.flagUrl" mode="scaleToFill" />
        </view>
      </view>

      <view v-if="loading" class="status-tip">
        <uni-load-more status="loading" />
      </view>
      <view v-else-if="countryList.length === 0" class="empty-message-container">
        <text class="empty-text">暂无相关国家信息</text>
      </view>

      <view class="scroll-view-bottom-spacer"></view>
    </scroll-view>

    <CustomTabBar :current="3" />
  </view>
</template>

<script setup>
import { ref, computed, nextTick, getCurrentInstance } from 'vue';
import { onPullDownRefresh, onLoad, onShow } from '@dcloudio/uni-app';
import { getCountryList } from '@/api/content/country.js';
import { IMAGE_BASE_URL } from '@/utils/config.js';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';

// 资源配置
const headerBgUrl = computed(() => assets.value.bg_country_list_header || '');
const activeTabBgUrl = computed(() => assets.value.bg_country_list_active_tab || '');

// 基础配置和状态
const baseUrl = IMAGE_BASE_URL;
const searchKeyword = ref('');
const activeContinent = ref('ALL');
const countryList = ref([]);
const loading = ref(false);
const instance = getCurrentInstance();
const assets = ref(uni.getStorageSync('staticAssets') || {});

// 大洲Tab数据
const continents = [
  { label: '全部', value: 'ALL' },
  { label: '亚洲', value: 'ASIA' },
  { label: '欧洲', value: 'EUROPE' },
  { label: '北美洲', value: 'NORTH_AMERICA' },
  { label: '南美洲', value: 'SOUTH_AMERICA' },
  { label: '非洲', value: 'AFRICA' },
  { label: '大洋洲', value: 'OCEANIA' }
];

// --- 指示器相关状态 ---
const activeIndex = ref(0);
const tabsInfo = ref([]); // 存储Tab信息
const currentScrollLeft = ref(0); // 当前滚动位置
const showIndicator = ref(false); // 控制指示器显示
const tabScrollLeft = ref(0); // 控制滚动位置
const tabsContainerWidth = ref(0); // tabs容器宽度

/**
 * 计算指示器位置
 */
const indicatorLeft = computed(() => {
  if (!tabsInfo.value.length || activeIndex.value >= tabsInfo.value.length) {
    return -999;
  }

  const activeTab = tabsInfo.value[activeIndex.value];
  if (!activeTab) return -999;

  // 指示器宽度的一半 (20rpx转px)
  const indicatorHalfWidth = uni.upx2px(20);

  // Tab中心位置 = Tab左边距 + Tab宽度的一半
  const tabCenterX = activeTab.left + activeTab.width / 2;

  // 指示器left = Tab中心位置 - 滚动距离 - 指示器宽度的一半
  const left = tabCenterX - currentScrollLeft.value - indicatorHalfWidth;

  return left;
});

/**
 * Tab滚动事件处理
 */
const onTabsScroll = (event) => {
  currentScrollLeft.value = event.detail.scrollLeft;
};

/**
 * 获取Tab布局信息
 */
const getTabsRect = () => {
  return new Promise((resolve) => {
    const query = uni.createSelectorQuery().in(instance);

    // 获取tabs容器信息
    query.select('.continent-tabs').boundingClientRect();
    // 获取所有tab项信息
    query.selectAll('.tab-item').boundingClientRect();

    query.exec((res) => {
      if (res && res[1] && res[1].length) {
        const containerRect = res[0];
        const tabRects = res[1];

        tabsContainerWidth.value = containerRect ? containerRect.width : 0;
        tabsInfo.value = tabRects;
        showIndicator.value = true;
        resolve(tabRects);
      } else {
        resolve([]);
      }
    });
  });
};

/**
 * 确保选中的Tab在可视区域内
 */
const scrollToActiveTab = () => {
  if (!tabsInfo.value.length || activeIndex.value >= tabsInfo.value.length) {
    return;
  }

  const activeTab = tabsInfo.value[activeIndex.value];
  const containerWidth = tabsContainerWidth.value;

  if (!activeTab || !containerWidth) return;

  // Tab中心位置
  const tabCenter = activeTab.left + activeTab.width / 2;
  // 容器中心位置
  const containerCenter = containerWidth / 2;
  // 需要滚动到的位置
  let scrollTo = tabCenter - containerCenter;

  // 边界处理
  const maxScroll = Math.max(0, activeTab.dataset?.scrollWidth - containerWidth || 0);
  scrollTo = Math.max(0, Math.min(scrollTo, maxScroll));

  tabScrollLeft.value = scrollTo;
};

/**
 * Tab点击事件
 */
const onFilterTap = async (continentValue, index) => {
  if (activeContinent.value === continentValue) return;

  const oldIndex = activeIndex.value;
  activeContinent.value = continentValue;
  activeIndex.value = index;

  // 如果Tab信息还没加载，先加载
  if (!tabsInfo.value.length) {
    await getTabsRect();
  }

  // 滚动到选中的Tab
  nextTick(() => {
    scrollToActiveTab();
  });

  fetchData(true);
};

/**
 * 初始化Tab布局信息
 */
const initTabsLayout = async () => {
  // 等待DOM渲染
  await nextTick();

  // 多次尝试获取布局信息，确保准确性
  let retryCount = 0;
  const maxRetries = 5;

  const tryGetLayout = async () => {
    const rects = await getTabsRect();

    if (rects.length === 0 && retryCount < maxRetries) {
      retryCount++;
      setTimeout(tryGetLayout, 100);
    } else if (rects.length > 0) {
      // 布局信息获取成功，滚动到当前选中的Tab
      scrollToActiveTab();
    }
  };

  tryGetLayout();
};

/**
 * 获取国别列表数据
 */
const fetchData = async (isRefresh = false) => {
  if (loading.value) return;
  loading.value = true;
  try {
    const res = await getCountryList({
      continent: activeContinent.value,
      keyword: searchKeyword.value
    });
    countryList.value = isRefresh ? res.data : [...countryList.value, ...res.data];
  } catch (error) {
    console.error('获取国别列表失败:', error);
    uni.showToast({ title: '数据加载失败', icon: 'none' });
  } finally {
    loading.value = false;
    if (isRefresh) {
      uni.stopPullDownRefresh();
    }
  }
};

/**
 * 搜索相关方法
 */
const onSearch = () => {
  fetchData(true);
};

const onCancelSearch = () => {
  searchKeyword.value = '';
  fetchData(true);
};

/**
 * 跳转到详情页
 */
const goToDetail = (countryId) => {
  uni.navigateTo({ url: `/pages_sub/pages_country/detail?id=${countryId}` });
};

/**
 * 加载更多
 */
const loadMore = () => {};

// 页面生命周期
onLoad(() => {
  fetchData(true);
  // 延迟初始化Tab布局，确保DOM完全渲染
  setTimeout(initTabsLayout, 200);
});

onShow(() => {
  uni.hideTabBar();
  // 页面显示时重新计算布局（处理屏幕旋转等情况）
  setTimeout(initTabsLayout, 100);
});

onPullDownRefresh(() => {
  fetchData(true);
});
</script>

<style lang="scss" scoped>
/* 页面总容器 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: transparent;
}

/* 顶部背景区域 */
.header-background {
  height: 420rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 10;
}

/* 页面标题容器 */
.page-title-wrapper {
  position: absolute;
  top: 94rpx;
  left: 0;
  width: 750rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}

/* 搜索栏包裹容器 */
.search-bar-wrapper {
  position: absolute;
  top: 182rpx;
  left: 0;
  width: 750rpx;
  height: 88rpx;
  box-sizing: border-box;
}
:deep(.uni-search-bar) {
  margin: 14rpx 16rpx 14rpx 32rpx;
  height: calc(100% - 28rpx);
  padding: 0 !important;
}
:deep(.uni-search-bar__box) {
  height: 100% !important;
  justify-content: flex-start !important;
}
:deep(.uni-search-bar__text-placeholder) {
  font-size: 28rpx !important;
  color: #9B9A9A !important;
}

:deep(.uni-searchbar__cancel) {
  font-size: 30rpx !important;
  color: #FFFFFF !important;
}

/* Tab容器和指示器 */
.continent-tabs-container {
  position: absolute;
  top: 320rpx;
  transform: translateY(-50%);
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-top: 10rpx;
}

.continent-tabs {
  white-space: nowrap;
  &::-webkit-scrollbar {
    display: none;
  }
}

.continent-tabs::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}

.tab-item {
  display: inline-block;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #23232A;
  border-radius: 30rpx;
  background-color: #FFFFFF;
  transition: all 0.3s;
  background-size: cover;
  background-position: center;
  margin-right: 20rpx;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    color: #23232A;
    font-weight: bold;
  }
}

/* 指示器样式优化 */
.active-indicator {
  position: absolute;
  bottom: -32rpx;
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 25rpx solid #fff;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  z-index: 10;
  opacity: 0; /* 初始隐藏 */
}

/* 国别列表包裹容器 */
.country-list-wrapper {
  flex: 1;
  height: 0;
  background-color: #ffffff;
  position: relative;
  z-index: 15;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: -32rpx;
  box-sizing: border-box;
  overflow: hidden;
}

/* 国别卡片 */
.country-card {
  display: flex;
  align-items: center;
  width: 100%;
  height: 272rpx;
  padding: 0 30rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 卡片左侧封面图 */
.country-image {
  flex-shrink: 0;
  width: 336rpx;
  height: 192rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

/* 卡片右侧信息布局 */
.country-info {
  flex: 1;
  min-width: 0;
  height: 192rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 顶部信息块 */
.info-top {
  display: flex;
  flex-direction: column;
}

/* 国名行 */
.name-line {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;

  .name-cn {
    font-size: 28rpx;
    font-weight: bold;
    color: #23232A;
  }
  .name-en {
    font-size: 22rpx;
    margin-left: 12rpx;
    color: #9B9A9A;
  }
}

/* 简介 */
.summary {
  font-size: 24rpx;
  color: #23232A;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* 国旗 */
.country-flag {
  width: 60rpx;
  height: 40rpx;
  border-radius: 4rpx;
  border: 1rpx solid #eee;
  align-self: flex-start;
}

/* 状态提示 */
.status-tip, .empty-message-container {
  padding: 80rpx 0;
  text-align: center;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.scroll-view-bottom-spacer {
  height: 180rpx;
}
</style>