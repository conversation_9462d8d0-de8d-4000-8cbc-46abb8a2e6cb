package com.hongda.content.mapper;

import com.hongda.content.domain.HongdaRegion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区管理Mapper接口
 * * <AUTHOR>
 * @date 2025-08-01
 */
public interface HongdaRegionMapper
{
    /**
     * 查询地区管理
     * * @param id 地区管理主键
     * @return 地区管理
     */
    public HongdaRegion selectHongdaRegionById(Long id);

    /**
     * 查询地区管理列表
     * * @param hongdaRegion 地区管理
     * @return 地区管理集合
     */
    public List<HongdaRegion> selectHongdaRegionList(HongdaRegion hongdaRegion);

    /**
     * 新增地区管理
     * * @param hongdaRegion 地区管理
     * @return 结果
     */
    public int insertHongdaRegion(HongdaRegion hongdaRegion);

    /**
     * 修改地区管理
     * * @param hongdaRegion 地区管理
     * @return 结果
     */
    public int updateHongdaRegion(HongdaRegion hongdaRegion);

    /**
     * 删除地区管理
     * * @param id 地区管理主键
     * @return 结果
     */
    public int deleteHongdaRegionById(Long id);

    /**
     * 批量删除地区管理
     * * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaRegionByIds(Long[] ids);

    // --- [新增] 开始 ---

    /**
     * 根据地区代码查询关联的文章数量
     * @param regionCode 地区代码
     * @return 关联的文章数量
     */
    public Integer countArticlesByRegion(@Param("regionCode") String regionCode);

    /**
     * 根据ID数组查询地区列表
     * @param ids 地区ID数组
     * @return 地区列表
     */
    public List<HongdaRegion> selectHongdaRegionByIds(Long[] ids);

    // --- [新增] 结束 ---
}