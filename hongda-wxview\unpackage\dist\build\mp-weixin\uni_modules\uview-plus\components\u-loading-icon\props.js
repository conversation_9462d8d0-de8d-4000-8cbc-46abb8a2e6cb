"use strict";const o=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),e=o.defineMixin({props:{show:{type:Boolean,default:()=>t.props.loadingIcon.show},color:{type:String,default:()=>t.props.loadingIcon.color},textColor:{type:String,default:()=>t.props.loadingIcon.textColor},vertical:{type:<PERSON>olean,default:()=>t.props.loadingIcon.vertical},mode:{type:String,default:()=>t.props.loadingIcon.mode},size:{type:[String,Number],default:()=>t.props.loadingIcon.size},textSize:{type:[String,Number],default:()=>t.props.loadingIcon.textSize},text:{type:[String,Number],default:()=>t.props.loadingIcon.text},timingFunction:{type:String,default:()=>t.props.loadingIcon.timingFunction},duration:{type:[String,Number],default:()=>t.props.loadingIcon.duration},inactiveColor:{type:String,default:()=>t.props.loadingIcon.inactiveColor}}});exports.props=e;
