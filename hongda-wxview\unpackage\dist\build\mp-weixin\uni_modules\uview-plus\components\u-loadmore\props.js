"use strict";const o=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),r=o.defineMixin({props:{status:{type:String,default:()=>e.props.loadmore.status},bgColor:{type:String,default:()=>e.props.loadmore.bgColor},icon:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.loadmore.icon},fontSize:{type:[String,Number],default:()=>e.props.loadmore.fontSize},iconSize:{type:[String,Number],default:()=>e.props.loadmore.iconSize},color:{type:String,default:()=>e.props.loadmore.color},loadingIcon:{type:String,default:()=>e.props.loadmore.loadingIcon},loadmoreText:{type:String,default:()=>e.props.loadmore.loadmoreText},loadingText:{type:String,default:()=>e.props.loadmore.loadingText},nomoreText:{type:String,default:()=>e.props.loadmore.nomoreText},isDot:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.loadmore.isDot},iconColor:{type:String,default:()=>e.props.loadmore.iconColor},marginTop:{type:[String,Number],default:()=>e.props.loadmore.marginTop},marginBottom:{type:[String,Number],default:()=>e.props.loadmore.marginBottom},height:{type:[String,Number],default:()=>e.props.loadmore.height},line:{type:Boolean,default:()=>e.props.loadmore.line},lineColor:{type:String,default:()=>e.props.loadmore.lineColor},dashed:{type:Boolean,default:()=>e.props.loadmore.dashed}}});exports.props=r;
