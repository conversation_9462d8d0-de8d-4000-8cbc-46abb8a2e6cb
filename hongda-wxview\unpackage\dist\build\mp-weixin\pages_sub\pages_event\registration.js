"use strict";const e=require("../../common/vendor.js"),o=require("./api/data/registration.js"),l=require("../../api/data/event.js");if(!Array){(e.resolveComponent("up-navbar")+e.resolveComponent("up-loading-page")+e.resolveComponent("up-button")+e.resolveComponent("up-empty")+e.resolveComponent("up-input")+e.resolveComponent("up-form-item")+e.resolveComponent("up-textarea")+e.resolveComponent("up-form")+e.resolveComponent("up-picker"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-navbar/u-navbar.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js")+(()=>"../../uni_modules/uview-plus/components/u-button/u-button.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../uni_modules/uview-plus/components/u-input/u-input.js")+(()=>"../../uni_modules/uview-plus/components/u-form-item/u-form-item.js")+(()=>"../../uni_modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../uni_modules/uview-plus/components/u-form/u-form.js")+(()=>"../../uni_modules/uview-plus/components/u-picker/u-picker.js"))();const r={__name:"registration",setup(r){const a=e.ref(null),t=e.ref(null),n=e.ref([]),i=e.reactive({}),u=e.reactive({}),s=e.ref(!0),d=e.ref(!1),p=e.ref(!1),c=e.ref(!1),g=e.ref([]),v=e.ref(""),b=e.ref(!1),x=e.ref(),m=e.ref();e.onLoad((async o=>{if(a.value=o.id,!a.value)return e.index.$u.toast("活动信息错误"),void setTimeout((()=>{e.index.navigateBack()}),1500)})),e.onShow((async()=>{console.log("=== 报名页面 onShow 触发 ===");const o=!!e.index.getStorageSync("token");p.value=o,console.log("当前登录状态:",o),console.log("当前token:",e.index.getStorageSync("token")),o?(console.log("用户已登录，开始加载活动信息和表单数据"),a.value&&await y(),!a.value||n.value&&0!==n.value.length?s.value=!1:await w()):(console.log("用户未登录，返回上一页让detail页面处理登录跳转"),s.value=!1,e.index.showToast({title:"请先登录后报名",icon:"none",duration:1500}),setTimeout((()=>{console.log("返回上一页（detail页面）"),e.index.navigateBack({fail:()=>{console.log("返回失败，跳转到首页"),e.index.switchTab({url:"/pages/index/index"})}})}),800))}));const f=()=>{console.log("手动跳转到登录页"),e.index.navigateTo({url:"/pages/login/index"})},h=()=>{console.log("=== 开始智能导航回退处理 ==="),e.index.navigateBack({success:()=>{console.log("正常回退成功")},fail:o=>{console.warn("⚠️ 正常回退失败:",o),e.index.navigateTo({url:"/pages/event/index",success:()=>{console.log("跳转到活动列表页面成功")},fail:o=>{console.warn("跳转到活动列表页面失败:",o),e.index.switchTab({url:"/pages/index/index",success:()=>{console.log("跳转到首页成功")},fail:o=>{console.error("所有导航方案都失败了:",o),e.index.showToast({title:"导航失败，请重新打开小程序",icon:"none"})}})}})}})},y=async()=>{try{const e=await l.getEventDetailApi(a.value);if(200!==e.code||!e.data)throw new Error(e.msg||"获取活动信息失败");t.value=e.data,console.log("成功加载活动信息:",t.value)}catch(e){console.error("加载活动信息失败:",e)}},w=async()=>{try{s.value=!0;const e=await o.getFormDefinitionApi(a.value);if(200!==e.code||!e.data)throw new Error(e.msg||"获取表单配置失败");{let o;if("string"==typeof e.data)try{o=JSON.parse(e.data),console.log("解析后的表单定义:",o)}catch(l){throw console.error("解析表单定义JSON失败:",l),new Error("表单配置格式错误")}else o=e.data;const r=o.fields||[];Array.isArray(r)&&r.length>0?(n.value=r,console.log("成功加载表单配置，字段数量:",r.length),F(),C()):(console.warn("表单配置中没有字段或字段不是数组:",o),n.value=[])}}catch(r){console.error("加载表单定义失败:",r),e.index.$u.toast(r.message||"加载表单失败，请稍后重试"),n.value=[]}finally{s.value=!1}},F=()=>{n.value.forEach((e=>{e.field&&(i[e.field]=e.defaultValue||"")}))},C=()=>{n.value.forEach((e=>{if(e.field&&e.required){const o=[{required:!0,message:`请${"select"===e.type?"选择":"输入"}${e.label}`,trigger:["blur","change"]}];"email"===e.type?o.push({pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:["blur"]}):"phone"===e.type&&o.push({pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:["blur"]}),u[e.field]=o}}))},S=(e,o)=>{const l=i[e];if(!l||!o)return"";const r=o.find((e=>e.value===l));return r?r.label:""},k=e=>{const{value:o}=e;o&&o[0]&&v.value&&(i[v.value]=o[0].value),c.value=!1},B=async()=>{if(x.value)try{await x.value.validate(),b.value=!0}catch(o){console.error("表单验证失败:",o),e.index.$u.toast("请检查表单填写是否正确")}else e.index.$u.toast("表单初始化失败")},q=()=>{b.value=!1},_=async()=>{q();try{d.value=!0;const r=await o.submitRegistrationApi({eventId:a.value,formData:i});if(200!==r.code)throw new Error(r.msg||"报名失败");e.index.$u.toast("报名成功！");try{const o=e.index.getStorageSync("registrationStatus")||{};o[a.value]={isRegistered:!0,timestamp:Date.now(),formData:i,source:"user_registration"},e.index.setStorageSync("registrationStatus",o),console.log("已在本地存储中标记报名状态（状态分离模式）:",o)}catch(l){console.warn("保存本地报名状态失败:",l)}setTimeout((()=>{console.log("发送数据变化广播事件..."),e.index.$emit("dataChanged"),console.log("已发送 dataChanged 事件"),e.index.navigateBack({success:()=>{console.log("返回上一页成功"),setTimeout((()=>{e.index.$emit("dataChanged"),console.log("页面返回后再次发送 dataChanged 事件")}),100)},fail:o=>{console.warn("页面返回失败:",o),e.index.switchTab({url:"/pages/index/index"})}})}),1500)}catch(l){console.error("提交报名失败:",l),l.message?e.index.$u.toast(l.message):e.index.$u.toast("提交失败，请稍后重试")}finally{d.value=!1}};return(o,l)=>{var r;return e.e({a:e.o(h),b:e.p({title:(null==(r=t.value)?void 0:r.title)||"活动报名",fixed:!0,safeAreaInsetTop:!0,bgColor:"transparent",leftIcon:"arrow-left",leftIconColor:"#333333",titleStyle:"color: #333333; font-weight: bold;"}),c:s.value},s.value?{d:e.p({loadingText:"正在加载报名表单...",loadingMode:"spinner"})}:p.value?!p.value||n.value&&0!==n.value.length?p.value&&n.value&&n.value.length>0?e.e({l:t.value},t.value?{m:e.t(t.value.title)}:{},{n:e.f(n.value,((o,l,r)=>{var a,t,n,u,s,d,p,b;return e.e({a:"input"===o.type},"input"===o.type?{b:"910b4048-7-"+r+",910b4048-6-"+r,c:e.o((e=>i[o.field]=e),l),d:e.p({placeholder:(null==(a=o.props)?void 0:a.placeholder)||"请输入",clearable:!0,maxlength:(null==(t=o.props)?void 0:t.maxlength)||100,customStyle:"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:i[o.field]}),e:"910b4048-6-"+r+",910b4048-5",f:e.p({label:o.label,prop:o.field,required:o.required})}:"textarea"===o.type?{h:"910b4048-9-"+r+",910b4048-8-"+r,i:e.o((e=>i[o.field]=e),l),j:e.p({placeholder:(null==(n=o.props)?void 0:n.placeholder)||"请输入",maxlength:(null==(u=o.props)?void 0:u.maxlength)||500,height:"120",count:!0,customStyle:"width: 686rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:i[o.field]}),k:"910b4048-8-"+r+",910b4048-5",l:e.p({label:o.label,prop:o.field,required:o.required})}:"select"===o.type?{n:e.o((l=>(o=>{o.options&&Array.isArray(o.options)?(v.value=o.field,g.value=[o.options],c.value=!0):e.index.$u.toast("选项配置错误")})(o)),l),o:"910b4048-11-"+r+",910b4048-10-"+r,p:e.p({value:S(o.field,o.options),placeholder:(null==(s=o.props)?void 0:s.placeholder)||"请选择",readonly:!0,suffixIcon:"arrow-down-fill",customStyle:"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"}),q:"910b4048-10-"+r+",910b4048-5",r:e.p({label:o.label,prop:o.field,required:o.required})}:"number"===o.type?{t:"910b4048-13-"+r+",910b4048-12-"+r,v:e.o((e=>i[o.field]=e),l),w:e.p({placeholder:(null==(d=o.props)?void 0:d.placeholder)||"请输入数字",type:"number",clearable:!0,customStyle:"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:i[o.field]}),x:"910b4048-12-"+r+",910b4048-5",y:e.p({label:o.label,prop:o.field,required:o.required})}:"phone"===o.type?{A:"910b4048-15-"+r+",910b4048-14-"+r,B:e.o((e=>i[o.field]=e),l),C:e.p({placeholder:(null==(p=o.props)?void 0:p.placeholder)||"请输入手机号",type:"number",clearable:!0,maxlength:11,customStyle:"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:i[o.field]}),D:"910b4048-14-"+r+",910b4048-5",E:e.p({label:o.label,prop:o.field,required:o.required})}:"email"===o.type?{G:"910b4048-17-"+r+",910b4048-16-"+r,H:e.o((e=>i[o.field]=e),l),I:e.p({placeholder:(null==(b=o.props)?void 0:b.placeholder)||"请输入邮箱地址",clearable:!0,customStyle:"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:i[o.field]}),J:"910b4048-16-"+r+",910b4048-5",K:e.p({label:o.label,prop:o.field,required:o.required})}:{},{g:"textarea"===o.type,m:"select"===o.type,s:"number"===o.type,z:"phone"===o.type,F:"email"===o.type,L:l})})),o:e.sr(x,"910b4048-5",{k:"formRef"}),p:e.p({model:i,rules:u,labelPosition:"top",labelWidth:"auto",labelStyle:{fontFamily:"Alibaba PuHuiTi 3.0-55 Regular",fontWeight:"normal",fontSize:"28rpx",color:"#23232A",lineHeight:"normal"}})}):{}:{j:e.p({mode:"data",text:"该活动无需报名表单",textColor:"#909399",textSize:"28"})}:{f:e.o(f),g:e.p({type:"primary",text:"立即登录",customStyle:"background-color: #f56c6c; border-color: #f56c6c; width: 200rpx; height: 70rpx; font-size: 28rpx;"}),h:e.p({mode:"permission",text:"请先登录后进行报名",textColor:"#909399",textSize:"28"})},{e:!p.value,i:p.value&&(!n.value||0===n.value.length),k:p.value&&n.value&&n.value.length>0,q:p.value&&!s.value&&n.value&&n.value.length>0},p.value&&!s.value&&n.value&&n.value.length>0?{r:e.t(d.value?"提交中...":"提交报名信息"),s:e.o(B),t:e.p({type:"primary",shape:"circle",size:"large",loading:d.value,disabled:d.value,customStyle:"width: 702rpx; height: 76rpx; background: #023F98; border-radius: 8rpx; border-color: #023F98; font-size: 28rpx;"})}:{},{v:e.sr(m,"910b4048-19",{k:"pickerRef"}),w:e.o(k),x:e.o((e=>c.value=!1)),y:e.p({show:c.value,columns:g.value,keyName:"label"}),z:b.value},b.value?{A:o.ordersWarningIconUrl,B:e.o(q),C:e.o(_),D:e.o((()=>{})),E:e.o(q)}:{})}}},a=e._export_sfc(r,[["__scopeId","data-v-910b4048"]]);wx.createPage(a);
