"use strict";const t=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),r=t.defineMixin({props:{duration:{type:Number,default:()=>e.props.tabs.duration},list:{type:Array,default:()=>e.props.tabs.list},lineColor:{type:String,default:()=>e.props.tabs.lineColor},activeStyle:{type:[String,Object],default:()=>e.props.tabs.activeStyle},inactiveStyle:{type:[String,Object],default:()=>e.props.tabs.inactiveStyle},lineWidth:{type:[String,Number],default:()=>e.props.tabs.lineWidth},lineHeight:{type:[String,Number],default:()=>e.props.tabs.lineHeight},lineBgSize:{type:String,default:()=>e.props.tabs.lineBgSize},itemStyle:{type:[String,Object],default:()=>e.props.tabs.itemStyle},scrollable:{type:Boolean,default:()=>e.props.tabs.scrollable},current:{type:[Number,String],default:()=>e.props.tabs.current},keyName:{type:String,default:()=>e.props.tabs.keyName},iconStyle:{type:[String,Object],default:()=>e.props.tabs.iconStyle}}});exports.props=r;
