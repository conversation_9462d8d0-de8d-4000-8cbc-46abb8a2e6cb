/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-a5d8397f {
  height: 100%;
  background-color: #f4f4f4;
}
.page-container.data-v-a5d8397f {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
}
.custom-header.data-v-a5d8397f {
  padding: 0 0 24rpx;
  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  height: 378rpx;
  justify-content: space-around;
}
.status-bar.data-v-a5d8397f {
  height: var(--status-bar-height);
}
.nav-bar.data-v-a5d8397f {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 32rpx;
}
.page-title-container.data-v-a5d8397f {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.page-title.data-v-a5d8397f {
  font-size: 34rpx;
  font-weight: 600;
  color: #303133;
}
.search-section.data-v-a5d8397f {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 0 32rpx;
}
.search-input-wrapper.data-v-a5d8397f {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 36rpx;
}
.search-input.data-v-a5d8397f {
  flex: 1;
  font-size: 28rpx;
  color: #23232A;
}
.cancel-btn.data-v-a5d8397f {
  color: #9B9A9A;
  font-size: 28rpx;
}
.tabs-container.data-v-a5d8397f {
  display: flex;
  justify-content: flex-start;
  padding: 20rpx 0;
  background-color: #FFFFFF;
}
.custom-tabs.data-v-a5d8397f {
  display: flex;
  gap: 20rpx;
  padding: 0 32rpx;
}
.tab-item.data-v-a5d8397f {
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  color: #23232A;
  font-size: 32rpx;
  font-weight: 500;
  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);
  transition: all 0.3s ease;
}

/* 活动按钮样式*/
.tab-item.data-v-a5d8397f:first-child {
  width: 132rpx;
  height: 60rpx;
  font-size: 32rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 资讯按钮样式*/
.tab-item.data-v-a5d8397f:last-child {
  width: 132rpx;
  height: 60rpx;
  background: rgba(42, 97, 241, 0.1);
  border-radius: 30rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #23232A;
}
.tab-item.active.data-v-a5d8397f {
  /* 【关键修改】移除 background-image */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 激活状态下的资讯按钮 */
.tab-item:last-child.active.data-v-a5d8397f {
  background: #2a61f1;
  color: #FFFFFF;
}

/* 激活状态下的活动按钮 */
.tab-item:first-child.active.data-v-a5d8397f {
  color: #23232A;
}
.result-list-scroll.data-v-a5d8397f {
  flex: 1;
  height: 0;
  background-color: #FFFFFF;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}
.result-list-scroll.data-v-a5d8397f ::-webkit-scrollbar {
  display: none;
}
.result-list.data-v-a5d8397f {
  padding: 0;
}
.loading-container.data-v-a5d8397f, .empty-state-container.data-v-a5d8397f {
  padding-top: 200rpx;
}
.event-card.data-v-a5d8397f {
  width: 100%;
  height: 272rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  border: none;
  border-top: 2rpx solid #EEEEEE;
  border-bottom: 2rpx solid #EEEEEE;
  margin-bottom: 0rpx;
  padding: 24rpx 24rpx;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
}
.card-left.data-v-a5d8397f {
  position: relative;
  width: 336rpx;
  height: 192rpx;
  flex-shrink: 0;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.event-image.data-v-a5d8397f {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 16rpx;
}
.status-tag.data-v-a5d8397f {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  width: 96rpx;
  height: 44rpx;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 6rpx 12rpx;
  color: #23232A;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: 500;
  font-size: 22rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.status-tag.ended.data-v-a5d8397f {
  background: #9B9A9A;
  color: #FFFFFF;
  width: 116rpx;
}
.status-tag.not-started.data-v-a5d8397f {
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  color: #23232A;
  width: 116rpx;
}
.status-tag.open.data-v-a5d8397f {
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  color: #23232A;
}
.card-right.data-v-a5d8397f {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.event-title.data-v-a5d8397f {
  width: 346rpx;
  height: 80rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.event-info.data-v-a5d8397f {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.event-info-row.data-v-a5d8397f {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 24rpx !important;
  margin-bottom: 18rpx !important;
  flex-wrap: nowrap !important;
}
.time-location-item.data-v-a5d8397f {
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
  flex-shrink: 0 !important;
}
.event-info-icon.data-v-a5d8397f {
  width: 32rpx !important;
  height: 32rpx !important;
  flex-shrink: 0 !important;
}
.info-text.data-v-a5d8397f {
  width: 176rpx !important;
  height: 32rpx !important;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30" !important;
  font-weight: normal !important;
  font-size: 22rpx !important;
  color: #9B9A9A !important;
  text-align: left !important;
  font-style: normal !important;
  text-transform: none !important;
  line-height: 32rpx !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
.remaining-spots.data-v-a5d8397f {
  width: 154rpx;
  height: 40rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FB8620;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
}
.remaining-spots .spots-count.data-v-a5d8397f {
  width: 100%;
  height: 36rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 20rpx;
  color: #FB8620;
  text-align: center;
  font-style: normal;
  text-transform: none;
  line-height: 36rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 状态标签样式 */
.status-tag.ended.data-v-a5d8397f {
  width: 116rpx;
  /* 报名截止 */
  background-color: #ff4757;
}
.status-tag.not-started.data-v-a5d8397f {
  width: 116rpx;
  /* 即将开始 */
  background-color: #ffa502;
}
.status-tag.open.data-v-a5d8397f {
  /* 报名中 - 保持默认宽度 */
  background-color: #2ed573;
}