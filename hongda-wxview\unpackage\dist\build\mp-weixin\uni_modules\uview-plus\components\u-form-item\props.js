"use strict";const e=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),r=e.defineMixin({props:{label:{type:String,default:()=>t.props.formItem.label},prop:{type:String,default:()=>t.props.formItem.prop},rules:{type:Array,default:()=>t.props.formItem.rules},borderBottom:{type:[String,Boolean],default:()=>t.props.formItem.borderBottom},labelPosition:{type:String,default:()=>t.props.formItem.labelPosition},labelWidth:{type:[String,Number],default:()=>t.props.formItem.labelWidth},rightIcon:{type:String,default:()=>t.props.formItem.rightIcon},leftIcon:{type:String,default:()=>t.props.formItem.leftIcon},required:{type:Bo<PERSON>an,default:()=>t.props.formItem.required},leftIconStyle:{type:[String,Object],default:()=>t.props.formItem.leftIconStyle}}});exports.props=r;
