<view class="page-container data-v-a375fa30"><view class="header-section data-v-a375fa30" style="{{o}}"><view class="custom-nav-bar data-v-a375fa30"><view class="status-bar data-v-a375fa30"></view><view class="nav-title data-v-a375fa30">资讯列表</view><view class="filter-bar data-v-a375fa30"><view class="{{['sort-button', 'data-v-a375fa30', c && 'is-active']}}" bindtap="{{d}}">{{a}} <u-icon wx:if="{{b}}" class="data-v-a375fa30" u-i="a375fa30-0" bind:__l="__l" u-p="{{b}}"></u-icon></view><view class="{{['filter-button', 'data-v-a375fa30', h && 'is-active']}}" bindtap="{{i}}">{{e}} <u-icon wx:if="{{f}}" class="data-v-a375fa30" u-i="a375fa30-1" bind:__l="__l" u-p="{{f}}"></u-icon><view wx:if="{{g}}" class="active-dot data-v-a375fa30"></view></view><view class="search-box data-v-a375fa30"><uni-easyinput wx:if="{{n}}" class="search-input data-v-a375fa30" bindconfirm="{{j}}" bindclear="{{k}}" bindinput="{{l}}" u-i="a375fa30-2" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"></uni-easyinput></view></view></view></view><view class="content-section data-v-a375fa30"><view class="tabs-container data-v-a375fa30"><u-tabs wx:if="{{p}}" class="data-v-a375fa30" bindchange="{{q}}" u-i="a375fa30-3" bind:__l="__l" u-p="{{r}}"></u-tabs></view><scroll-view scroll-y class="article-list-scroll data-v-a375fa30" bindscrolltolower="{{z}}" enable-flex><view class="article-list data-v-a375fa30"><view wx:for="{{s}}" wx:for-item="item" wx:key="l" class="article-card data-v-a375fa30" bindtap="{{item.m}}"><view class="card-cover data-v-a375fa30"><u-image wx:if="{{item.f}}" class="data-v-a375fa30" u-s="{{['loading','error']}}" binderror="{{item.c}}" bindload="{{item.d}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"><view class="image-loading data-v-a375fa30" slot="loading"><u-loading-icon wx:if="{{t}}" class="data-v-a375fa30" u-i="{{item.a}}" bind:__l="__l" u-p="{{t}}"></u-loading-icon><text class="loading-text data-v-a375fa30">加载中...</text></view><view class="image-error data-v-a375fa30" slot="error"><u-icon wx:if="{{v}}" class="data-v-a375fa30" u-i="{{item.b}}" bind:__l="__l" u-p="{{v}}"></u-icon><text class="error-text data-v-a375fa30">图片加载失败</text></view></u-image></view><view class="card-content data-v-a375fa30"><text class="card-title data-v-a375fa30">{{item.g}}</text><view wx:if="{{item.h}}" class="card-tags data-v-a375fa30"><text wx:for="{{item.i}}" wx:for-item="tag" wx:key="b" class="tag-item data-v-a375fa30">{{tag.a}}</text></view><view class="card-meta data-v-a375fa30"><text class="meta-source data-v-a375fa30">{{item.j}}</text><text class="meta-date data-v-a375fa30">{{item.k}}</text></view></view></view></view><u-empty wx:if="{{w}}" class="data-v-a375fa30" u-i="a375fa30-7" bind:__l="__l" u-p="{{x}}"></u-empty><u-loadmore wx:else class="data-v-a375fa30" u-i="a375fa30-8" bind:__l="__l" u-p="{{y||''}}"/></scroll-view></view><view wx:if="{{A}}" class="dropdown-wrapper data-v-a375fa30"><view class="dropdown-mask data-v-a375fa30" bindtap="{{B}}"></view><view class="{{['dropdown-panel', 'data-v-a375fa30', J && 'show']}}"><view wx:if="{{C}}" class="sort-panel data-v-a375fa30"><view wx:for="{{D}}" wx:for-item="sort" wx:key="e" class="{{['sort-option', 'data-v-a375fa30', sort.f && 'active']}}" bindtap="{{sort.g}}">{{sort.a}} <u-icon wx:if="{{sort.b}}" class="data-v-a375fa30" u-i="{{sort.c}}" bind:__l="__l" u-p="{{sort.d}}"></u-icon></view></view><view wx:if="{{E}}" class="filter-panel data-v-a375fa30"><scroll-view scroll-y class="filter-scroll data-v-a375fa30"><view class="panel-section data-v-a375fa30"><text class="section-title data-v-a375fa30">内容地区</text><view class="panel-options data-v-a375fa30"><view wx:for="{{F}}" wx:for-item="region" wx:key="b" class="{{['option-btn', 'data-v-a375fa30', region.c && 'active']}}" bindtap="{{region.d}}">{{region.a}}</view></view></view><view class="panel-section data-v-a375fa30"><text class="section-title data-v-a375fa30">发布时间</text><view class="panel-options data-v-a375fa30"><view wx:for="{{G}}" wx:for-item="time" wx:key="b" class="{{['option-btn', 'data-v-a375fa30', time.c && 'active']}}" bindtap="{{time.d}}">{{time.a}}</view></view></view></scroll-view><view class="panel-footer data-v-a375fa30"><button class="footer-btn reset data-v-a375fa30" bindtap="{{H}}">重置</button><button class="footer-btn confirm data-v-a375fa30" bindtap="{{I}}">确定</button></view></view></view></view><custom-tab-bar wx:if="{{K}}" class="data-v-a375fa30" u-i="a375fa30-10" bind:__l="__l" u-p="{{K}}"/></view>